
import { useState } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { useUser } from "@supabase/auth-helpers-react";
import type { StudyResult } from "./types";
import { calculateRevisionDateAndWeek, createRevisionItem, calculateWeeksNeeded, createWeeksForRevision } from './useRevisions';
import { ensureWeeksExist } from './useScheduleDates';

export const useStudyTopics = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [processingTopics, setProcessingTopics] = useState<Set<string>>(new Set());
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const user = useUser();

  const markTopicAsStudied = async (topicId: string): Promise<StudyResult> => {
    // ✅ DEBOUNCE: Evitar processamento duplo do mesmo tópico
    if (processingTopics.has(topicId)) {
      return {
        success: true,
        message: "Tópico já está sendo processado",
        isLastRevision: false
      };
    }

    setProcessingTopics(prev => new Set(prev).add(topicId));

    try {
      // Get the current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const { data: schedules, error: schedulesError } = await supabase
        .from('study_schedules')
        .select('*')
        .order('week_start_date', { ascending: true });

      if (schedulesError) throw schedulesError;

      const { data: currentTopic } = await supabase
        .from('study_schedule_items')
        .select('*, study_schedules!inner(*)')
        .eq('id', topicId)
        .single();

      if (!currentTopic) throw new Error('Topic not found');

      // ✅ CORREÇÃO: Usar data atual como base para calcular próxima revisão
      const topicDate = new Date();
      topicDate.setHours(0, 0, 0, 0); // Normalizar para início do dia

      const currentRevisionNumber = currentTopic.revision_number || 0;
      let daysToAdd = 3;

      // ✅ CORREÇÃO: Aplicar lógica de intervalos para TODOS os tópicos, não só revisões
      switch (currentRevisionNumber) {
        case 0: daysToAdd = 3; break;   // Primeira revisão: 3 dias
        case 1: daysToAdd = 7; break;   // Segunda revisão: 7 dias
        case 2: daysToAdd = 30; break;  // Terceira revisão: 30 dias
        default: daysToAdd = 30; break; // ✅ CORREÇÃO: Quarta revisão em diante: 30 dias (não parar)
      }

      // Before calculating the next revision date, make sure we have weeks in the future
      const nextRevisionDate = new Date(topicDate);
      nextRevisionDate.setDate(nextRevisionDate.getDate() + daysToAdd);

      // ✅ CORREÇÃO: Garantir que há semanas suficientes para a revisão
      // Por enquanto, vamos pular a criação automática de semanas para evitar problemas
      // A revisão será criada se houver semanas disponíveis

      // Re-fetch schedules after potentially adding new weeks
      const { data: updatedSchedules } = await supabase
        .from('study_schedules')
        .select('*')
        .order('week_start_date', { ascending: true });

      const revisionInfo = await calculateRevisionDateAndWeek(
        topicDate,
        daysToAdd,
        updatedSchedules || schedules
      );

      const updates: any = {
        study_status: 'completed',
        last_revision_date: topicDate.toISOString(),
        revision_number: currentRevisionNumber
      };

      if (revisionInfo) {
        updates.next_revision_date = revisionInfo.date.toISOString();
      }

      const { error: updateError } = await supabase
        .from('study_schedule_items')
        .update(updates)
        .eq('id', topicId);

      if (updateError) throw updateError;

      // ✅ CORREÇÃO: Remover limite de revisões - continuar com intervalos de 30 dias
      // Não parar mais as revisões após a terceira

      if (revisionInfo) {
        const revisionChain = currentTopic.revision_chain || [];
        revisionChain.push(topicId);

        await createRevisionItem(
          currentTopic,
          revisionInfo.date,
          currentRevisionNumber + 1,
          topicId,
          revisionChain,
          updatedSchedules || schedules
        );

        // Invalidar TODOS os caches relacionados incluindo streak
        await queryClient.invalidateQueries({
          predicate: (query) => {
            const key = query.queryKey[0];
            return key === 'consolidated-schedule-data' ||
                   key === 'processed-schedule-data' ||
                   key === 'schedule' ||
                   key === 'study_schedules' ||
                   key === 'study_schedule_items' ||
                   key === 'consolidated-dashboard-stats' ||
                   key === 'week-activities' ||
                   key === 'optimized-streak-stats';
          },
          refetchType: 'all'
        });

        // ✅ CORREÇÃO: Calcular dias até a revisão corretamente
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const revisionDate = new Date(revisionInfo.date);
        revisionDate.setHours(0, 0, 0, 0);
        const daysUntilRevision = Math.ceil((revisionDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

        const result = {
          success: true,
          message: "Topic marked as studied successfully!",
          nextRevision: {
            date: revisionInfo.formattedDate,
            dayOfWeek: revisionInfo.dayOfWeek,
            revisionNumber: currentRevisionNumber + 1,
            daysUntil: daysUntilRevision
          },
          isLastRevision: false
        };


        setProcessingTopics(prev => {
          const newSet = new Set(prev);
          newSet.delete(topicId);
          return newSet;
        });
        return result;
      }

      // ✅ NOVO: Verificar se precisamos criar semanas para a revisão
      const weeksInfo = await calculateWeeksNeeded(nextRevisionDate, user.id);

      setProcessingTopics(prev => {
        const newSet = new Set(prev);
        newSet.delete(topicId);
        return newSet;
      });

      if (weeksInfo) {
        // Retornar informações para o diálogo de criação de semanas
        return {
          success: false, // ✅ Não marcar como sucesso ainda
          message: "Sem semanas suficientes",
          isLastRevision: false,
          nextRevision: null,
          needsWeeks: true, // ✅ Flag para indicar que precisa criar semanas
          weeksNeeded: weeksInfo.weeksNeeded,
          revisionDate: weeksInfo.revisionDateFormatted,
          topicId, // ✅ Para reprocessar após criar semanas
          revisionNumber: currentRevisionNumber
        };
      }

      // Se não conseguiu calcular, retornar sucesso genérico
      return {
        success: true,
        message: "Tópico marcado como estudado com sucesso! A próxima revisão será agendada quando houver semanas disponíveis.",
        isLastRevision: false,
        nextRevision: null
      };

    } catch (error: any) {
      console.error('Error updating topic:', error);
      setProcessingTopics(prev => {
        const newSet = new Set(prev);
        newSet.delete(topicId);
        return newSet;
      });
      toast({
        title: "Error updating topic",
        description: error.message,
        variant: "destructive"
      });
      return {
        success: false,
        message: error.message
      };
    }
  };

  const deleteTopic = async (topicId: string) => {
    setIsLoading(true);
    try {
      // Delete the topic
      const { error } = await supabase
        .from('study_schedule_items')
        .delete()
        .eq('id', topicId);

      if (error) throw error;

      toast({
        title: "Topic deleted",
        description: "The topic has been removed from your schedule.",
      });

      // await loadCurrentSchedule(); // Removido para evitar recarregamentos
      return true;
    } catch (error: any) {
      console.error('Error deleting topic:', error);
      toast({
        title: "Error deleting topic",
        description: error.message,
        variant: "destructive"
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * ✅ NOVA FUNÇÃO: Criar semanas e reprocessar tópico
   */
  const createWeeksAndMarkStudied = async (
    topicId: string,
    weeksNeeded: number,
    revisionNumber: number
  ): Promise<StudyResult> => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Criar as semanas necessárias
      const weeksCreated = await createWeeksForRevision(weeksNeeded, user.id);

      if (!weeksCreated) {
        return {
          success: false,
          message: "Erro ao criar semanas de estudo"
        };
      }

      // Invalidar cache para recarregar semanas
      await queryClient.invalidateQueries({
        predicate: (query) => {
          const key = query.queryKey[0];
          return key === 'study_schedules' || key === 'consolidated-schedule-data';
        }
      });

      // Reprocessar o tópico agora que temos semanas
      return await markTopicAsStudied(topicId);

    } catch (error: any) {
      console.error('❌ Erro ao criar semanas e marcar tópico:', error);
      return {
        success: false,
        message: error.message
      };
    }
  };

  return {
    isLoading,
    markTopicAsStudied,
    deleteTopic,
    createWeeksAndMarkStudied // ✅ Nova função exportada
  };
};
