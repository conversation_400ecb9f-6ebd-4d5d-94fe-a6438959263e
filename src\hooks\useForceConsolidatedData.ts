import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useConsolidatedSession } from '@/hooks/useConsolidatedSession';

/**
 * 🎯 HOOK QUE FORÇA USO DE DADOS CONSOLIDADOS
 * 
 * Elimina TODAS as queries duplicadas forçando uso do cache consolidado
 * Substitui hooks que fazem queries separadas
 */

/**
 * Hook que FORÇA uso de session_events consolidados
 */
export const useForceConsolidatedSessionEvents = (sessionId: string | null) => {
  const { data: consolidatedData, isLoading } = useConsolidatedSession(sessionId);
  
  return {
    data: consolidatedData?.sessionEvents || [],
    isLoading,
    error: null
  };
};

/**
 * Hook que FORÇA uso de user_answers consolidados
 */
export const useForceConsolidatedUserAnswers = (sessionId: string | null) => {
  const { data: consolidatedData, isLoading } = useConsolidatedSession(sessionId);
  
  return {
    data: consolidatedData?.userAnswers || [],
    isLoading,
    error: null
  };
};

/**
 * Hook que FORÇA uso de dados de sessão consolidados
 */
export const useForceConsolidatedSessionData = (sessionId: string | null) => {
  const { data: consolidatedData, isLoading } = useConsolidatedSession(sessionId);
  
  return {
    data: consolidatedData?.session || null,
    isLoading,
    error: null
  };
};

/**
 * Hook que BLOQUEIA queries duplicadas redirecionando para cache
 */
export const useBlockDuplicateQueries = () => {
  const queryClient = useQueryClient();
  
  const blockQuery = (queryKey: string[], redirectToKey: string[]) => {
    // Verificar se dados já estão no cache consolidado
    const consolidatedData = queryClient.getQueryData(redirectToKey);
    
    if (consolidatedData) {
      // Definir dados no cache da query original para evitar fetch
      queryClient.setQueryData(queryKey, consolidatedData);
      return true;
    }
    
    return false;
  };
  
  return { blockQuery };
};

/**
 * Hook para interceptar e redirecionar queries duplicadas
 */
export const useQueryInterceptor = (sessionId: string | null) => {
  const queryClient = useQueryClient();
  const { data: consolidatedData } = useConsolidatedSession(sessionId);
  
  // Interceptar queries de session_events
  const interceptSessionEvents = () => {
    if (consolidatedData?.sessionEvents && sessionId) {
      const sessionEventsKey = ['session_events', sessionId];
      queryClient.setQueryData(sessionEventsKey, consolidatedData.sessionEvents);
    }
  };
  
  // Interceptar queries de user_answers
  const interceptUserAnswers = () => {
    if (consolidatedData?.userAnswers && sessionId) {
      const userAnswersKey = ['user_answers', sessionId];
      queryClient.setQueryData(userAnswersKey, consolidatedData.userAnswers);
    }
  };
  
  // Interceptar queries de study_sessions
  const interceptSessionData = () => {
    if (consolidatedData?.session && sessionId) {
      const sessionDataKey = ['study_sessions', sessionId];
      queryClient.setQueryData(sessionDataKey, consolidatedData.session);
    }
  };
  
  return {
    interceptSessionEvents,
    interceptUserAnswers,
    interceptSessionData,
    interceptAll: () => {
      interceptSessionEvents();
      interceptUserAnswers();
      interceptSessionData();
    }
  };
};
