import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Info, X } from 'lucide-react';
import type { FocusInsight } from '@/types/insights';

interface InsightTooltipProps {
  insight: FocusInsight;
  children: React.ReactNode;
  side?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

const InsightTooltip: React.FC<InsightTooltipProps> = ({ 
  insight, 
  children, 
  side = 'top',
  className = '' 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetailed, setShowDetailed] = useState(false);

  const handleMouseEnter = () => {
    setIsVisible(true);
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
    setShowDetailed(false);
  };

  const handleTooltipClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDetailed(!showDetailed);
  };

  const getTooltipPosition = () => {
    switch (side) {
      case 'top':
        return 'bottom-full left-1/2 transform -translate-x-1/2 mb-2';
      case 'bottom':
        return 'top-full left-1/2 transform -translate-x-1/2 mt-2';
      case 'left':
        return 'right-full top-1/2 transform -translate-y-1/2 mr-2';
      case 'right':
        return 'left-full top-1/2 transform -translate-y-1/2 ml-2';
      default:
        return 'bottom-full left-1/2 transform -translate-x-1/2 mb-2';
    }
  };

  const getArrowPosition = () => {
    switch (side) {
      case 'top':
        return 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-800';
      case 'bottom':
        return 'bottom-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-t-transparent border-b-gray-800';
      case 'left':
        return 'left-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-r-transparent border-l-gray-800';
      case 'right':
        return 'right-full top-1/2 transform -translate-y-1/2 border-t-transparent border-b-transparent border-l-transparent border-r-gray-800';
      default:
        return 'top-full left-1/2 transform -translate-x-1/2 border-l-transparent border-r-transparent border-b-transparent border-t-gray-800';
    }
  };

  return (
    <div 
      className={`relative inline-block ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={`absolute z-50 ${getTooltipPosition()}`}
          >
            {/* Simple tooltip */}
            {!showDetailed && (
              <div className="relative">
                <div className="bg-gray-800 text-white text-xs rounded-lg px-3 py-2 max-w-xs shadow-lg">
                  <div className="flex items-start gap-2">
                    <div className="flex-1">
                      {insight.tooltip}
                    </div>
                    <button
                      onClick={handleTooltipClick}
                      className="flex-shrink-0 p-0.5 rounded hover:bg-gray-700 transition-colors"
                    >
                      <Info className="h-3 w-3" />
                    </button>
                  </div>
                </div>
                <div className={`absolute w-0 h-0 border-4 ${getArrowPosition()}`} />
              </div>
            )}

            {/* Detailed tooltip */}
            {showDetailed && (
              <div className="relative">
                <div className="bg-white border border-gray-200 rounded-lg shadow-xl max-w-sm">
                  {/* Header */}
                  <div className={`${insight.config.bgColor} border-b border-gray-200 px-4 py-3 rounded-t-lg`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{insight.config.icon}</span>
                        <span className={`font-bold ${insight.config.textColor}`}>
                          {insight.config.label}
                        </span>
                      </div>
                      <button
                        onClick={handleTooltipClick}
                        className="p-1 rounded hover:bg-white/50 transition-colors"
                      >
                        <X className="h-4 w-4 text-gray-500" />
                      </button>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <div className="mb-3">
                      <h4 className="font-semibold text-gray-800 mb-1">
                        {insight.focus_name}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {insight.specialty_name} • {insight.theme_name}
                      </p>
                    </div>

                    {/* Stats */}
                    <div className="grid grid-cols-3 gap-3 mb-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-800">
                          {insight.frequency}
                        </div>
                        <div className="text-xs text-gray-600">
                          Ocorrências
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-800">
                          {insight.years_since_last < 999 ? insight.years_since_last : '∞'}
                        </div>
                        <div className="text-xs text-gray-600">
                          Anos atrás
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-800">
                          {insight.total_questions}
                        </div>
                        <div className="text-xs text-gray-600">
                          Questões
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <div className="text-sm text-gray-700 leading-relaxed">
                      {insight.config.description}
                    </div>

                    {/* Last occurrence info */}
                    {insight.last_occurrence_year > 0 && (
                      <div className="mt-3 text-xs text-gray-500 border-t pt-3">
                        Última aparição: {insight.last_occurrence_year}
                      </div>
                    )}
                  </div>
                </div>
                <div className={`absolute w-0 h-0 border-4 border-gray-200 ${getArrowPosition().replace('border-t-gray-800', 'border-t-gray-200').replace('border-b-gray-800', 'border-b-gray-200').replace('border-l-gray-800', 'border-l-gray-200').replace('border-r-gray-800', 'border-r-gray-200')}`} />
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default InsightTooltip;
