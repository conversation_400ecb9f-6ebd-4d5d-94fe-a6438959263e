
import React from "react";
import { <PERSON>lt<PERSON><PERSON>rovider } from "@/components/ui/tooltip";
import { FeedbackDialogProvider } from "@/components/ui/feedback-dialog";
import { QueryClient, QueryClientProvider, QueryCache, MutationCache } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route, Navigate } from "react-router-dom";
import ProtectedRoute from "@/components/ProtectedRoute";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { initSentry, setUserContext, clearUserContext } from "@/utils/sentry";
import { analytics } from "@/utils/analytics";
// import { monitoring } from "@/utils/monitoring";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Study from "@/pages/study";
import Results from "@/pages/Results";
import Progress from "@/pages/Progress";
import Questions from "@/pages/Questions";
import Flashcards from "@/pages/Flashcards";
import { useA<PERSON>, AuthProvider } from "@/contexts/AuthContext";
import { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { SessionContextProvider } from "@supabase/auth-helpers-react";
import QuestionList from "@/components/QuestionList";
import FocusConsolidation from "@/components/admin/FocusConsolidation";
import FocusNameImprovement from "@/pages/admin/FocusNameImprovement";
import FocusConsolidationByYear from "@/pages/admin/FocusConsolidationByYear";
import FocusOptimization from "@/pages/admin/FocusOptimization";
import QuestionCategorization from "@/pages/admin/QuestionCategorization";
import CategoryManagement from "@/pages/admin/CategoryManagement";
import PrevalenceAnalysisPage from "@/pages/admin/prevalence";
import StudyPreferences from "@/pages/StudyPreferences";
import HowItWorks from './pages/HowItWorks';
import Terms from './pages/Terms';
import Privacy from './pages/Privacy';
import Login from './pages/Login';
import Onboarding from "./pages/Onboarding";
import Settings from "./pages/Settings";
import RestrictedAccessPage from "./pages/RestrictedAccess";
import CollaborativeFlashcards from "@/pages/CollaborativeFlashcards";
import CollaborateFlashcards from "@/pages/CollaborateFlashcards";
import Ranking from "@/pages/Ranking";
import QuestionRanking from "@/pages/QuestionRanking";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import { useUserData } from "@/hooks/useUserData";
import UsersManagement from "@/pages/admin/UsersManagement";
import QuestionHierarchyImprovement from "@/pages/admin/QuestionHierarchyImprovement";
import Schedule from "@/pages/Schedule";
import StudyFlow from "@/pages/StudyFlow";
import FeedbackSupport from "@/pages/FeedbackSupport";
import DrWill from "@/pages/DrWill";
import { NavigationLockProvider } from "@/contexts/NavigationLockContext";
import { NavigationLockIndicator } from "@/components/NavigationLockIndicator";
import { AppStabilizer } from "@/components/AppStabilizer";
import { CurrentQuestionProvider } from "@/contexts/CurrentQuestionContext";
import { logDomainConfig, validateCurrentDomain } from "@/utils/domainConfig";
import { applyConsoleFilters } from "@/utils/consoleFilters";
import { initializeSupabaseQueryTracker, generateQueryReport, clearQueryLogs } from "@/utils/supabaseQueryTracker";
import WelcomeScreen from "@/components/WelcomeScreen";
import { useWelcomeFlow } from "@/hooks/useWelcomeFlow";
import { useUserPreferences } from "@/hooks/useUserPreferences";
import { ReferralDetector } from "@/components/ReferralDetector";
import { FloatingChatButton } from "@/components/FloatingChatButton";
import { DrWillContextTutorial } from "@/components/tutorials/DrWillContextTutorial";
import { useDrWillContextTutorial } from "@/hooks/useDrWillContextTutorial";
import { useLocation } from "react-router-dom";

// Componente simplificado para otimizações essenciais
function OptimizedAppProvider() {
  // Aplicar filtros de console e validações apenas uma vez
  React.useEffect(() => {
    // Evitar reinicialização desnecessária
    if (window.__optimizationsInitialized) {
      return;
    }

    // Aplicar filtros para suprimir warnings desnecessários
    applyConsoleFilters();

    // Validar domínio apenas em desenvolvimento
    if (import.meta.env.DEV) {
      logDomainConfig();
      if (!validateCurrentDomain()) {
        // Domain validation warning disabled
      }

      // Inicializar rastreamento de queries do Supabase
      initializeSupabaseQueryTracker();

      // Adicionar funções globais para debug
      (window as any).generateQueryReport = generateQueryReport;
      (window as any).clearQueryLogs = clearQueryLogs;


    }

    window.__optimizationsInitialized = true;
  }, []);

  return null; // Componente invisível apenas para executar otimizações
}

// ✅ INICIALIZAR SISTEMAS DE MONITORAMENTO
initSentry();

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Cache moderado para balance entre performance e dados frescos
      staleTime: 5 * 60 * 1000, // 5 minutos - mais conservador
      cacheTime: 15 * 60 * 1000, // 15 minutos - cache moderado
      refetchOnWindowFocus: false,
      refetchOnMount: true, // ✅ CORRIGIDO: Permitir refetch na montagem
      refetchOnReconnect: true, // ✅ CORRIGIDO: Permitir refetch na reconexão
      retry: 2, // Aumentado para 2 tentativas
      retryDelay: 1000,
      keepPreviousData: false, // ✅ CORRIGIDO: Não manter dados antigos por padrão
      networkMode: 'online',
    },
    mutations: {
      retry: 1, // Simplificado
      retryDelay: 1000, // Delay fixo
      networkMode: 'online',
    },
  },
  // Configuração de garbage collection mais agressiva
  queryCache: new QueryCache({
    onError: (error, query) => {
      // Error logging disabled for production
    },
  }),
  mutationCache: new MutationCache({
    onError: (error, variables, context, mutation) => {
      // Error logging disabled for production
    },
  }),
});

// Disponibilizar queryClient globalmente para otimizações de memória
(window as any).__queryClient = queryClient;

// 🔍 Inicializar sistema de logs para diagnóstico
import('@/utils/requestLogger').then(({ setupSupabaseLogger }) => {
  setupSupabaseLogger();
  // ✅ LIMPEZA: Log removido - sistema de logs ativado
});

function AppContent() {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen bg-[#FEF7CD]">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando...</p>
      </div>
    </div>;
  }

  return (
    <SessionContextProvider supabaseClient={supabase}>
      <QueryClientProvider client={queryClient}>
        <AppRoutes />
      </QueryClientProvider>
    </SessionContextProvider>
  );
}

// Componente para controlar o botão flutuante
function FloatingChatController() {
  const location = useLocation();
  const { user } = useAuth();
  const { shouldShowTutorial, markTutorialAsCompleted, skipTutorial } = useDrWillContextTutorial();

  // Páginas onde o botão deve aparecer (todas as páginas protegidas)
  const showOnPages = [
    '/plataformadeestudos',
    '/progress',
    '/flashcards',
    '/collaborative',
    '/collaborate',
    '/schedule',
    '/settings',
    '/ranking',
    '/questions',
    '/results',
    '/feedback-suporte'
  ];

  // Não mostrar na página do Dr. Will e páginas públicas
  const hideOnPages = [
    '/dr-will',
    '/',
    '/login',
    '/como-funciona',
    '/terms',
    '/privacy',
    '/onboarding',
    '/acesso-restrito',
    '/admin'
  ];

  // Mostrar se:
  // 1. Usuário está logado
  // 2. Está em uma página permitida OU não está em uma página proibida
  // 3. Não está em página de admin
  const shouldShow = user &&
    (showOnPages.some(page => location.pathname.startsWith(page)) ||
     (!hideOnPages.some(page => location.pathname.startsWith(page)) &&
      !location.pathname.startsWith('/admin')));

  // Verificar se está em uma sessão de questões para mostrar o tutorial
  const isInQuestionSession = location.pathname.startsWith('/questions/');
  const showTutorial = shouldShow && isInQuestionSession && shouldShowTutorial;

  if (!shouldShow) return null;

  return (
    <>
      <FloatingChatButton />
      <DrWillContextTutorial
        isVisible={showTutorial}
        onComplete={markTutorialAsCompleted}
        onSkip={skipTutorial}
      />
    </>
  );
}

// Novo componente que usa useUserData dentro do QueryClientProvider
function AppRoutes() {
  const { user } = useAuth();
  const { hasCompletedOnboarding, isLoading: userDataLoading, userData, isPremium } = useUserData();
  const { showWelcome, isLoading: welcomeLoading, grantPremiumAccess, userName } = useWelcomeFlow();

  // ✅ CONFIGURAR CONTEXTO DO USUÁRIO PARA MONITORAMENTO
  useEffect(() => {
    // Evitar reconfiguração desnecessária
    if (!user?.id) {
      clearUserContext();
      analytics.clearUser();
      return;
    }

    if (user && userData && !window.__userContextConfigured) {
      // Configurar Sentry
      setUserContext({
        id: user.id,
        email: user.email,
        formation_area: userData.formation_area,
        is_student: userData.is_student,
      });

      // Configurar Analytics
      analytics.setUser(user.id, {
        formation_area: userData.formation_area,
        is_student: userData.is_student,
        registration_date: userData.created_at,
      });

      window.__userContextConfigured = true;
    }
  }, [user?.id, userData?.id]);

  // Usar dados centralizados do useUserData
  const needsOnboarding = !hasCompletedOnboarding;

  if (userDataLoading || welcomeLoading) {
    return <div className="flex items-center justify-center min-h-screen bg-[#FEF7CD]">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando dados do usuário...</p>
      </div>
    </div>;
  }

  // ✅ Mostrar tela de boas-vindas para novos usuários
  if (user && showWelcome) {
    const handleGetPremiumAccess = async () => {
      const success = await grantPremiumAccess();
      if (success) {
        // Redirecionar para plataforma de estudos após ativar premium
        window.location.href = '/plataformadeestudos';
      }
    };

    return (
      <WelcomeScreen
        onGetPremiumAccess={handleGetPremiumAccess}
        userName={userName}
      />
    );
  }

  return (
        <TooltipProvider>
          <FeedbackDialogProvider>
            <NavigationLockProvider>
              <CurrentQuestionProvider>
                <NavigationLockIndicator />
                <Router
                  future={{
                    v7_startTransition: true,
                    v7_relativeSplatPath: true
                  }}
                >
                  <AppStabilizer />
                  <OptimizedAppProvider />
                  <ReferralDetector />
                  <FloatingChatController />
            <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/como-funciona" element={<HowItWorks />} />
            <Route path="/terms" element={<Terms />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/onboarding" element={<Onboarding />} />
            <Route path="/study-preferences" element={<StudyPreferences />} />
            <Route path="/acesso-restrito" element={<RestrictedAccessPage />} />
            <Route
              path="/plataformadeestudos"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Study />
                </ProtectedRoute>
              }
            />
            <Route
              path="/questions"
              element={
                <ProtectedRoute requireAccess={true}>
                  <QuestionList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/progress"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Progress />
                </ProtectedRoute>
              }
            />
            <Route
              path="/flashcards/*"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Flashcards />
                </ProtectedRoute>
              }
            />
             <Route
              path="/collaborative/flashcards"
              element={
                <ProtectedRoute requireAccess={true}>
                  <CollaborativeFlashcards />
                </ProtectedRoute>
              }
            />
             <Route
              path="/collaborate/flashcards"
              element={
                <ProtectedRoute requireAccess={true}>
                  <CollaborateFlashcards />
                </ProtectedRoute>
              }
            />
            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <Settings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/schedule"
              element={
                <ProtectedRoute requireAccess={true}>
                  <Schedule />
                </ProtectedRoute>
              }
            />
            <Route
              path="/studyflow"
              element={
                <ProtectedRoute requireAccess={true}>
                  <StudyFlow />
                </ProtectedRoute>
              }
            />
            <Route
              path="/feedback-suporte"
              element={
                <ProtectedRoute requireAccess={true}>
                  <FeedbackSupport />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dr-will"
              element={
                <ProtectedRoute requireAccess={true}>
                  <DrWill />
                </ProtectedRoute>
              }
            />

            {/* Admin Routes */}
            <Route
              path="/admin"
              element={
                <ProtectedRoute>
                  <AdminDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/focus-name-improvement"
              element={
                <ProtectedRoute>
                  <FocusNameImprovement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/focus-consolidation-by-year"
              element={
                <ProtectedRoute>
                  <FocusConsolidationByYear />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/focus-optimization"
              element={
                <ProtectedRoute>
                  <FocusOptimization />
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/question-categorization"
              element={
                <ProtectedRoute>
                  <QuestionCategorization />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/category-management"
              element={
                <ProtectedRoute>
                  <CategoryManagement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/focus-consolidation"
              element={
                <ProtectedRoute>
                  <FocusConsolidation />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/users"
              element={
                <ProtectedRoute>
                  <UsersManagement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/question-hierarchy"
              element={
                <ProtectedRoute>
                  <QuestionHierarchyImprovement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin/prevalence"
              element={
                <ProtectedRoute>
                  <PrevalenceAnalysisPage />
                </ProtectedRoute>
              }
            />

            <Route path="/focus-consolidation" element={<FocusConsolidation />} />
            <Route path="/questions/:sessionId" element={<Questions />} />
            <Route path="/results/:sessionId" element={<Results />} />
            <Route path="/results" element={<Study />} />
            <Route path="/ranking" element={<Ranking />} />
            <Route path="/ranking/questions" element={<QuestionRanking />} />

            {/* Wildcard route para qualquer caminho que não corresponda aos definidos acima */}
            <Route path="*" element={<NotFound />} />
                </Routes>
                </Router>
              </CurrentQuestionProvider>
            </NavigationLockProvider>
          </FeedbackDialogProvider>
        </TooltipProvider>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
