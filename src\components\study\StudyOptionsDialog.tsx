
import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { CheckCircle, Info, Loader2, AlertTriangle, BookOpen, Target, Zap, Settings, Brain, GraduationCap, Users } from "lucide-react";
import { motion } from "framer-motion";

import { useUser } from "@supabase/auth-helpers-react";
import { useDomain } from "@/hooks/useDomain";
import { useStudyPreferences } from "@/hooks/useStudyPreferences";

interface StudyOptionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  maxQuestions: number;
  minQuestions?: number;
  availableYears: number[];
  availableInstitutions: { id: string; name: string }[];
  onStartStudy: (quantity: number, hideAnswered?: boolean, institutionIds?: string[]) => Promise<void>;
  totalTopics?: number;
  // Props para recalcular questões
  specialtyId?: string;
  themeId?: string;
  focusId?: string;
  // Props para múltiplos tópicos
  allTopicsData?: Array<{
    topic: any;
    questionCount: number;
    questions: any[];
  }>;
  // Props para Insight do Dia
  insightData?: {
    focus_name: string;
    specialty_name: string;
    theme_name: string;
    temperature: string;
  };
}

export const StudyOptionsDialog = ({
  open,
  onOpenChange,
  maxQuestions,
  minQuestions = 1,
  availableYears,
  availableInstitutions,
  onStartStudy,
  totalTopics = 0,
  specialtyId,
  themeId,
  focusId,
  allTopicsData,
  insightData
}: StudyOptionsDialogProps) => {
  const [quantity, setQuantity] = useState(minQuestions > 10 ? minQuestions : 10);
  const [loading, setLoading] = useState(false);
  const [hideAnswered, setHideAnswered] = useState(false);
  const [personalizedOnly, setPersonalizedOnly] = useState(true); // ✅ CORREÇÃO: Marcado por padrão
  const [filteredTotal, setFilteredTotal] = useState(maxQuestions);
  const [recalculating, setRecalculating] = useState(false);

  // ✅ OTIMIZAÇÃO: Cache e debounce para evitar requests duplicadas
  const cacheRef = useRef<Map<string, number>>(new Map());
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  const user = useUser();
  const { domain } = useDomain();
  const { preferences } = useStudyPreferences();

  // ✅ OTIMIZAÇÃO: Função de recálculo com cache e debounce
  const recalculateQuestions = useCallback(async () => {
    if (!user?.id) return;

    // Criar chave de cache
    const institutionIds = personalizedOnly && preferences?.target_institutions
      ? preferences.target_institutions.map(inst => inst.id)
      : undefined;

    const cacheKey = JSON.stringify({
      hideAnswered,
      personalizedOnly,
      specialtyId,
      themeId,
      focusId,
      userId: user.id,
      domain,
      totalTopics,
      institutionIds: institutionIds?.sort() // Ordenar para consistência
    });

    // Verificar cache primeiro
    if (cacheRef.current.has(cacheKey)) {
      const cachedResult = cacheRef.current.get(cacheKey)!;
      setFilteredTotal(cachedResult);
      return;
    }

    setRecalculating(true);

    try {
      let result: number;

      // ✅ CORREÇÃO: Fazer query real em vez de estimativas
      if (hideAnswered || personalizedOnly) {
        try {
          // Importar função de query otimizada
          const { getQuestionsOptimized } = await import('@/utils/questionUtils');



          // ✅ CORREÇÃO: Para múltiplos tópicos, calcular total agregado
          if (totalTopics > 1 && !specialtyId && !themeId && !focusId && allTopicsData) {
            // ✅ CORREÇÃO: Filtrar apenas tópicos não completados
            const pendingTopics = allTopicsData.filter(({ topic }) => {
              // Assumir que tópicos sem study_status ou com status 'pending' devem ser incluídos
              const status = (topic as any).study_status;
              return !status || status === 'pending';
            });

            // Calcular total agregado apenas dos tópicos pendentes
            const topicResults = await Promise.all(
              pendingTopics.map(async ({ topic }) => {
                const topicResult = await getQuestionsOptimized(
                  topic.specialtyId,
                  topic.themeId,
                  topic.focusId,
                  1000, // Limite alto para contar todas
                  undefined, // years
                  institutionIds, // Aplicar filtro de instituições se personalizedOnly
                  false, // não randomizar
                  domain,
                  hideAnswered, // Aplicar filtro de questões já respondidas
                  user.id,
                  true // Retornar apenas IDs para performance
                );

                return topicResult.count;
              })
            );

            result = topicResults.reduce((total, count) => total + count, 0);
          } else {
            // Fazer query real com os filtros aplicados para tópico único
            const queryResult = await getQuestionsOptimized(
              specialtyId,
              themeId,
              focusId,
              1000, // Limite alto para contar todas
              undefined, // years
              institutionIds, // Aplicar filtro de instituições se personalizedOnly
              false, // não randomizar
              domain,
              hideAnswered, // Aplicar filtro de questões já respondidas
              user.id,
              true // Retornar apenas IDs para performance
            );

            result = queryResult.count;
          }


        } catch (error) {
          console.error('❌ [StudyOptionsDialog] Erro ao buscar questões filtradas:', error);
          // Fallback para estimativa se a query falhar
          let reductionFactor = 1.0;
          if (hideAnswered) reductionFactor *= 0.7;
          if (personalizedOnly && institutionIds && institutionIds.length > 0) reductionFactor *= 0.6;
          result = Math.floor(maxQuestions * reductionFactor);
        }
      } else {
        result = maxQuestions;
      }

      // Salvar no cache
      cacheRef.current.set(cacheKey, result);

      // Limitar cache a 50 entradas
      if (cacheRef.current.size > 50) {
        const firstKey = cacheRef.current.keys().next().value;
        cacheRef.current.delete(firstKey);
      }

      setFilteredTotal(result);
    } catch (error) {
      setFilteredTotal(maxQuestions);
    } finally {
      setRecalculating(false);
    }
  }, [hideAnswered, personalizedOnly, specialtyId, themeId, focusId, user?.id, domain, maxQuestions, totalTopics, allTopicsData, preferences]);

  // ✅ OTIMIZAÇÃO: Debounce para evitar requests em cascata
  useEffect(() => {
    // Só recalcular se o dialog estiver aberto
    if (!open) return;

    // Limpar timeout anterior
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Definir novo timeout
    debounceRef.current = setTimeout(() => {
      recalculateQuestions();
    }, 300); // 300ms de debounce

    // Cleanup
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
    };
  }, [open, recalculateQuestions]);

  // Limitar o máximo de questões a 25, mesmo quando houver mais disponíveis
  const HARD_LIMIT = 25;
  const displayTotalQuestions = filteredTotal;
  const effectiveMaxQuestions = Math.max(minQuestions, Math.min(filteredTotal, HARD_LIMIT));

  // Estados para mostrar/ocultar seções
  const [showIncludedTopics, setShowIncludedTopics] = useState(false);
  const [showCompletedTopics, setShowCompletedTopics] = useState(false);

  // Calcular informações dos tópicos para exibição
  const topicsInfo = useMemo(() => {
    if (!allTopicsData || totalTopics <= 1) return null;

    const pendingTopics = allTopicsData.filter(({ topic }) => {
      const status = (topic as any).study_status;
      return !status || status === 'pending';
    });

    const completedTopics = allTopicsData.filter(({ topic }) => {
      const status = (topic as any).study_status;
      return status === 'completed';
    });

    return {
      pending: pendingTopics.map(({ topic }) => ({
        name: topic.focus || topic.theme || topic.specialty || 'Tópico',
        specialty: topic.specialty || ''
      })),
      completed: completedTopics.map(({ topic }) => ({
        name: topic.focus || topic.theme || topic.specialty || 'Tópico',
        specialty: topic.specialty || ''
      }))
    };
  }, [allTopicsData, totalTopics]);

  // Ajustar a quantidade se exceder o máximo disponível quando o maxQuestions prop mudar
  useEffect(() => {
    if (quantity > effectiveMaxQuestions) {
      setQuantity(effectiveMaxQuestions);
    }
  }, [effectiveMaxQuestions, maxQuestions]);

  // Inicializar a quantidade com um valor razoável quando o diálogo abrir
  useEffect(() => {
    if (open) {
      const initialQuantity = Math.min(
        Math.max(minQuestions, 10),
        effectiveMaxQuestions
      );
      setQuantity(initialQuantity);
    }
  }, [open, minQuestions, effectiveMaxQuestions, displayTotalQuestions]);

  const handleStartStudy = async () => {
    try {
      setLoading(true);
      // Garantir que não excedemos o máximo de questões disponíveis
      const finalQuantity = Math.min(quantity, effectiveMaxQuestions);

      // Obter IDs das instituições de preferência se personalizedOnly estiver ativo
      const institutionIds = personalizedOnly && preferences?.target_institutions
        ? preferences.target_institutions.map(inst => inst.id)
        : undefined;



      await onStartStudy(finalQuantity, hideAnswered, institutionIds);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className={`w-[90dvw] sm:max-w-2xl max-h-[85dvh] rounded-2xl sm:rounded-xl overflow-y-auto shadow-2xl ${
        focusId && !specialtyId && !themeId
          ? "bg-gradient-to-br from-purple-50 via-white to-blue-50 border-2 border-purple-300"
          : totalTopics > 1 && !focusId && !specialtyId && !themeId
          ? "bg-gradient-to-br from-green-50 via-white to-blue-50 border-2 border-green-300"
          : "bg-gradient-to-br from-white to-blue-50 border-2 border-black"
      }`}>
        <div className={`text-center pb-6 border-b w-full ${
          focusId && !specialtyId && !themeId
            ? "border-purple-200"
            : totalTopics > 1 && !focusId && !specialtyId && !themeId
            ? "border-green-200"
            : "border-blue-200"
        }`}>
          {/* Header especial para Insight do Dia */}
          {focusId && !specialtyId && !themeId ? (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-4 w-full"
            >
              {/* Badge Insight do Dia */}
              <div className="flex items-center justify-center">
                <div className="bg-gradient-to-r from-purple-500 to-blue-600 text-white px-6 py-3 rounded-full text-base font-bold flex items-center gap-2 shadow-lg">
                  <Brain className="h-5 w-5" />
                  💡 Insight do Dia
                </div>
              </div>

              {/* Título do Foco Real */}
              <div className="space-y-4 w-full">
                <DialogTitle className="text-2xl font-bold text-gray-900 leading-tight">
                  {insightData?.focus_name || "Foco Inteligente"}
                </DialogTitle>

                {/* Informações do Tema */}
                {insightData && (
                  <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-4 border border-purple-200 w-full">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 bg-purple-500 rounded-full flex-shrink-0"></div>
                        <span className="text-purple-700 font-medium">
                          <strong>Especialidade:</strong> {insightData.specialty_name}
                        </span>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 bg-blue-500 rounded-full flex-shrink-0"></div>
                        <span className="text-blue-700 font-medium">
                          <strong>Tema:</strong> {insightData.theme_name}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <DialogDescription className="text-base text-gray-600">
                🎯 Foco selecionado por análise inteligente para otimizar seu aprendizado hoje.
              </DialogDescription>
            </motion.div>
          ) : totalTopics > 1 && !focusId && !specialtyId && !themeId ? (
            /* Header especial para Todos os Tópicos */
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-4 w-full"
            >
              {/* Badge Todos os Tópicos */}
              <div className="flex items-center justify-center">
                <div className="bg-gradient-to-r from-green-500 to-blue-600 text-white px-6 py-3 rounded-full text-base font-bold flex items-center gap-2 shadow-lg">
                  <GraduationCap className="h-5 w-5" />
                  📚 Estudos do Dia
                </div>
              </div>

              {/* Título Simplificado */}
              <div className="space-y-2">
                <DialogTitle className="text-2xl font-bold text-gray-900 leading-tight">
                  Estudos do Dia
                </DialogTitle>
                <div className="text-sm text-gray-600">
                  Questões distribuídas entre todos os tópicos disponíveis
                </div>
              </div>

              <DialogDescription className="text-base text-gray-600">
                🎯 Estude de forma balanceada com questões de todos os tópicos disponíveis hoje.
              </DialogDescription>
            </motion.div>
          ) : (
            /* Header padrão para outros estudos */
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="flex items-center justify-center gap-3 mb-3"
            >
              <div className="p-3 bg-gradient-to-r from-blue-500 to-green-500 rounded-full border-2 border-black">
                <Settings className="h-6 w-6 text-white" />
              </div>
              <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent">
                Opções de Estudo
              </DialogTitle>
            </motion.div>
          )}

          {(!focusId || specialtyId || themeId) && !(totalTopics > 1 && !focusId && !specialtyId && !themeId) ? (
            <DialogDescription className="text-base text-gray-600">
              Configure seu estudo personalizado com as questões disponíveis.
            </DialogDescription>
          ) : null}
        </div>

        <div className="space-y-6 py-6">
          {maxQuestions === 0 ? (
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 flex items-start gap-2 text-sm text-amber-700">
              <AlertTriangle className="h-5 w-5 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Nenhuma questão disponível</p>
                <p className="mt-1">Não encontramos questões para este tópico no momento.</p>
              </div>
            </div>
          ) : maxQuestions < 3 ? (
            <div className="bg-amber-50 border border-amber-200 rounded-md p-3 flex items-start gap-2 text-sm text-amber-700">
              <Info className="h-5 w-5 flex-shrink-0 mt-0.5" />
              <div>
                <p className="font-medium">Poucas questões disponíveis</p>
                <p className="mt-1">Existem apenas {maxQuestions} questões disponíveis para este tópico.</p>
              </div>
            </div>
          ) : null}



          {/* Botões para mostrar/ocultar tópicos */}
          {topicsInfo && (
            <div className="space-y-3">
              {/* Botão para tópicos incluídos */}
              {topicsInfo.pending.length > 0 && (
                <div className="space-y-2">
                  <button
                    onClick={() => setShowIncludedTopics(!showIncludedTopics)}
                    className="w-full flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg hover:bg-green-100 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="font-semibold text-green-800">
                        Tópicos Incluídos ({topicsInfo.pending.length})
                      </span>
                    </div>
                    <div className="text-green-600">
                      {showIncludedTopics ? '▼' : '▶'}
                    </div>
                  </button>

                  {showIncludedTopics && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 ml-4">
                      <div className="space-y-2">
                        {topicsInfo.pending.map((topic, index) => (
                          <div key={index} className="flex items-start gap-2 text-sm">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                            <div>
                              <p className="font-medium text-green-800">{topic.name}</p>
                              <p className="text-green-600 text-xs">{topic.specialty}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="mt-3 p-2 bg-green-100 rounded text-xs text-green-700">
                        ✅ Estes tópicos serão incluídos no seu estudo com questões filtradas
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Botão para tópicos excluídos */}
              {topicsInfo.completed.length > 0 && (
                <div className="space-y-2">
                  <button
                    onClick={() => setShowCompletedTopics(!showCompletedTopics)}
                    className="w-full flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-gray-500" />
                      <span className="font-semibold text-gray-700">
                        Tópicos Já Finalizados ({topicsInfo.completed.length})
                      </span>
                    </div>
                    <div className="text-gray-600">
                      {showCompletedTopics ? '▼' : '▶'}
                    </div>
                  </button>

                  {showCompletedTopics && (
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 ml-4">
                      <div className="space-y-2">
                        {topicsInfo.completed.map((topic, index) => (
                          <div key={index} className="flex items-start gap-2 text-sm">
                            <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0"></div>
                            <div>
                              <p className="font-medium text-gray-700 line-through">{topic.name}</p>
                              <p className="text-gray-500 text-xs">{topic.specialty}</p>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="mt-3 p-2 bg-gray-100 rounded text-xs text-gray-600">
                        ⏭️ Estes tópicos foram excluídos por já estarem concluídos
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Seção da Quantidade */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="space-y-4"
          >
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-blue-500" />
              <Label htmlFor="quantity" className="text-sm font-bold text-gray-700">
                Quantidade de Questões
              </Label>
            </div>

            {/* Card com informações */}
            <div className="bg-gradient-to-r from-blue-50 to-green-50 border-2 border-blue-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm text-blue-700 font-medium">Questões selecionadas:</span>
                <div className="flex items-center gap-2">
                  <div className={`px-2 sm:px-3 py-1 rounded-full border-2 border-black font-bold text-sm sm:text-lg ${
                    quantity >= effectiveMaxQuestions
                      ? "bg-gradient-to-r from-amber-400 to-orange-400 text-white"
                      : "bg-gradient-to-r from-blue-500 to-green-500 text-white"
                  }`}>
                    {quantity}
                    {quantity >= effectiveMaxQuestions ? (
                      <span className="hidden sm:inline"> (máx)</span>
                    ) : ""}
                  </div>
                  <Zap className="h-4 w-4 text-blue-500" />
                </div>
              </div>
              <Slider
                id="quantity"
                min={minQuestions}
                max={effectiveMaxQuestions}
                step={1}
                value={[quantity]}
                onValueChange={(value) => setQuantity(value[0])}
                className="py-2"
              />

              <div className="flex justify-between text-xs text-blue-600 font-medium mt-2">
                <span>Mín: {minQuestions}</span>
                <span>Máx: {effectiveMaxQuestions}{maxQuestions > HARD_LIMIT ? ` (total: ${displayTotalQuestions})` : ''}</span>
              </div>
            </div>

            {maxQuestions > HARD_LIMIT && (
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 flex items-start gap-2 text-xs text-amber-700 mt-3">
                <Info className="h-4 w-4 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">Limite de sessão:</p>
                  <p>O máximo por sessão é {HARD_LIMIT} questões, embora existam {displayTotalQuestions} disponíveis no total.</p>
                </div>
              </div>
            )}
          </motion.div>

          {/* Filtro para ocultar questões já acertadas */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-3"
          >
            <div className="flex items-center gap-2">
              <BookOpen className="h-4 w-4 text-green-500" />
              <Label className="text-sm font-bold text-gray-700">
                Filtros Avançados
              </Label>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-blue-50 border-2 border-green-200 rounded-lg p-4 space-y-4">
              <div className="flex items-center space-x-3">
                <Checkbox
                  id="hide-answered"
                  checked={hideAnswered}
                  onCheckedChange={(checked) => setHideAnswered(checked as boolean)}
                  className="border-2 border-green-400 data-[state=checked]:bg-green-500"
                />
                <div className="flex-1">
                  <Label
                    htmlFor="hide-answered"
                    className="text-sm font-bold text-green-700 leading-none cursor-pointer"
                  >
                    Ocultar questões que já acertei
                  </Label>
                  <p className="text-xs text-green-600 mt-1">
                    Remove questões que você já respondeu corretamente em sessões anteriores
                  </p>
                </div>
              </div>

              {/* ✅ NOVO: Checkbox para questões personalizadas */}
              {preferences?.target_institutions && preferences.target_institutions.length > 0 && (
                <div className="flex items-center space-x-3 pt-3 border-t border-green-200">
                  <Checkbox
                    id="personalized-only"
                    checked={personalizedOnly}
                    onCheckedChange={(checked) => setPersonalizedOnly(checked as boolean)}
                    className="border-2 border-orange-400 data-[state=checked]:bg-orange-500"
                  />
                  <div className="flex-1">
                    <Label
                      htmlFor="personalized-only"
                      className="text-sm font-bold text-orange-700 leading-none cursor-pointer"
                    >
                      Apenas questões das minhas instituições
                    </Label>
                    <p className="text-xs text-orange-600 mt-1">
                      Filtra apenas questões de: {preferences.target_institutions.map(inst => inst.name).join(', ')}
                    </p>
                  </div>
                </div>
              )}

              {recalculating && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 flex items-start gap-2 text-xs text-blue-700 mt-3">
                  <Loader2 className="h-4 w-4 flex-shrink-0 mt-0.5 animate-spin" />
                  <p className="font-medium">Recalculando total de questões...</p>
                </div>
              )}
            </div>
          </motion.div>

          {/* Botão de Ação */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="pt-6 border-t border-blue-200"
          >
            <Button
              onClick={handleStartStudy}
              disabled={loading || maxQuestions === 0}
              className={`w-full font-bold border-2 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-0.5 py-3 text-lg ${
                focusId && !specialtyId && !themeId
                  ? "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 border-purple-300 text-white"
                  : totalTopics > 1 && !focusId && !specialtyId && !themeId
                  ? "bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 border-green-300 text-white"
                  : "bg-gradient-to-r from-blue-600 to-green-600 hover:from-blue-700 hover:to-green-700 border-black text-white"
              }`}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  {focusId && !specialtyId && !themeId
                    ? "Preparando Insight..."
                    : totalTopics > 1 && !focusId && !specialtyId && !themeId
                    ? "Preparando Estudos..."
                    : "Preparando sessão..."}
                </>
              ) : (
                <>
                  {focusId && !specialtyId && !themeId ? (
                    <>
                      <Brain className="mr-2 h-5 w-5" />
                      🚀 Estudar Insight do Dia
                    </>
                  ) : totalTopics > 1 && !focusId && !specialtyId && !themeId ? (
                    <>
                      <GraduationCap className="mr-2 h-5 w-5" />
                      📚 Estudar Todos os Tópicos
                    </>
                  ) : (
                    <>
                      <CheckCircle className="mr-2 h-5 w-5" />
                      Iniciar Estudo
                    </>
                  )}
                </>
              )}
            </Button>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
