
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import QuestionFilter from "./QuestionFilter";
import type { SelectedFilters } from "@/types/question";
import Header from "./Header";
import StudyNavBar from "./study/StudyNavBar";
import { useDomain } from "@/hooks/useDomain";
import { QuestionFilterTutorial } from "./filters/QuestionFilterTutorial";

interface QuestionListProps {
  domain?: string;
}

const QuestionList = ({ }: QuestionListProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { domain, isResidencia, isReady } = useDomain();
  const [searchTerm, setSearchTerm] = useState("");
  const [questions, setQuestions] = useState([]);
  const [selectedFilters, setSelectedFilters] = useState<SelectedFilters>({
    specialties: [],
    themes: [],
    focuses: [],
    locations: [],
    years: [],
    question_types: []
  });

  // Log the domain for debugging
  //console.log(`🏆 [QuestionList] Using domain from useDomain: ${domain}, isResidencia: ${isResidencia}, isReady: ${isReady}`);

  const handleStartStudy = async (sessionId: string) => {
    try {
      // Garantir que a navegação seja feita para o caminho correto
      navigate(`/questions/${sessionId}`);
    } catch (error: any) {
      toast({
        title: "Erro ao iniciar estudos",
        description: error.message,
        variant: "destructive"
      });
    }
  };


  return (
    <>
      <Header/>
      <StudyNavBar className="pt-0 sm:pt-0 mb-0 sm:mb-8" />
      <div className="min-h-screen bg-gradient-to-br from-[#FEF7CD] via-[#f8fafc] to-[#FEF7CD] pt-8 sm:pt-0">
        {/* Background pattern para evitar áreas em branco */}
        <div className="absolute inset-0 opacity-10 pointer-events-none">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f59e0b' fill-opacity='0.1'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="container max-w-6xl mx-auto px-4 py-8 space-y-8 animate-fade-in relative">

          {/* Filter Section */}
          {/* Mobile - Sem div intermediária */}
          <div className="block sm:hidden">
            <QuestionFilter
              selectedFilters={selectedFilters}
              setSelectedFilters={setSelectedFilters}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              setQuestions={setQuestions}
              onSessionCreated={handleStartStudy}
              onShowRandomDialog={() => {}} // Não usado mais diretamente
              domain={domain}
            />
          </div>

          {/* Desktop - Com div intermediária */}
          <div className="hidden sm:block bg-white/95 backdrop-blur-sm rounded-xl border-2 border-yellow-400/70 shadow-xl">
            <QuestionFilter
              selectedFilters={selectedFilters}
              setSelectedFilters={setSelectedFilters}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
              setQuestions={setQuestions}
              onSessionCreated={handleStartStudy}
              onShowRandomDialog={() => {}} // Não usado mais diretamente
              domain={domain}
            />
          </div>
        </div>
      </div>

      {/* Tutorial de Filtros */}
      <QuestionFilterTutorial />
    </>
  );
};

export default QuestionList;
