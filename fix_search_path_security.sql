-- =====================================================
-- CORREÇÃO DE SEARCH PATH MUTABLE - get_focus_insights
-- =====================================================
-- Execute este arquivo no SQL Editor do Supabase

-- =====================================================
-- CORRIGIR get_focus_insights
-- =====================================================

CREATE OR REPLACE FUNCTION public.get_focus_insights(
  p_institution_ids TEXT[],
  p_exclude_focus_ids TEXT[] DEFAULT '{}',
  p_frequency_threshold INTEGER DEFAULT 3,
  p_recency_threshold INTEGER DEFAULT 3,
  p_years_range INTEGER DEFAULT 10
)
RETURNS TABLE(
  focus_id TEXT,
  focus_name TEXT,
  specialty_name TEXT,
  theme_name TEXT,
  frequency INTEGER,
  last_occurrence_year INTEGER,
  years_since_last INTEGER,
  total_questions INTEGER,
  temperature TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  current_year INTEGER := EXTRACT(YEAR FROM CURRENT_DATE);
  min_year INTEGER := current_year - p_years_range;
  daily_seed INTEGER := EXTRACT(DOY FROM CURRENT_DATE);
  institution_uuids UUID[];
BEGIN
  -- Converter TEXT[] para UUID[]
  SELECT ARRAY(SELECT uuid(unnest(p_institution_ids))) INTO institution_uuids;
  
  RETURN QUERY
  WITH focus_stats AS (
    SELECT 
      sc.id::TEXT as focus_id,
      sc.name as focus_name,
      sp.name as specialty_name,
      th.name as theme_name,
      COUNT(q.id) as frequency,
      COALESCE(MAX(q.exam_year), 0) as last_occurrence_year,
      CASE 
        WHEN MAX(q.exam_year) IS NULL THEN 999
        ELSE current_year - MAX(q.exam_year)
      END as years_since_last,
      (
        SELECT COUNT(*)
        FROM questions q2
        WHERE q2.focus_id = sc.id
          AND q2.exam_location = ANY(institution_uuids)
      ) as total_questions
    FROM study_categories sc
    INNER JOIN study_categories th ON sc.parent_id = th.id AND th.type = 'theme'
    INNER JOIN study_categories sp ON th.parent_id = sp.id AND sp.type = 'specialty'
    LEFT JOIN questions q ON q.focus_id = sc.id
      AND q.exam_location = ANY(institution_uuids)
      AND q.exam_year >= min_year
    WHERE sc.type = 'focus'
      AND sc.id::TEXT != ALL(p_exclude_focus_ids)
      AND EXISTS (
        SELECT 1 FROM questions q3 
        WHERE q3.focus_id = sc.id
          AND q3.exam_location = ANY(institution_uuids)
      )
    GROUP BY sc.id, sc.name, sp.name, th.name
  )
  SELECT 
    fs.focus_id,
    fs.focus_name,
    fs.specialty_name,
    fs.theme_name,
    fs.frequency::INTEGER,
    fs.last_occurrence_year::INTEGER,
    fs.years_since_last::INTEGER,
    fs.total_questions::INTEGER,
    CASE 
      WHEN fs.frequency <= 1 AND fs.years_since_last > p_recency_threshold THEN 'frio'
      WHEN fs.frequency <= 1 AND fs.years_since_last <= p_recency_threshold THEN 'morno'
      WHEN fs.frequency >= p_frequency_threshold AND fs.years_since_last > p_recency_threshold THEN 'quente'
      WHEN fs.frequency >= p_frequency_threshold AND fs.years_since_last <= p_recency_threshold THEN 'vulcanico'
      ELSE 'frio'
    END as temperature
  FROM focus_stats fs
  WHERE fs.total_questions > 0
  ORDER BY 
    -- Ordenação aleatória baseada no dia do ano + hash do focus_id
    (daily_seed + abs(hashtext(fs.focus_id))) % 1000,
    -- Prioridade por temperatura (vulcânico primeiro)
    CASE 
      WHEN fs.frequency >= p_frequency_threshold AND fs.years_since_last <= p_recency_threshold THEN 1
      WHEN fs.frequency >= p_frequency_threshold AND fs.years_since_last > p_recency_threshold THEN 2
      WHEN fs.frequency <= 1 AND fs.years_since_last <= p_recency_threshold THEN 3
      ELSE 4
    END,
    fs.frequency DESC;
END;
$$;

-- =====================================================
-- VERIFICAÇÃO DA CORREÇÃO
-- =====================================================

-- Verificar se a função foi atualizada corretamente
SELECT
  routine_name,
  routine_type,
  security_type,
  routine_definition LIKE '%SET search_path = public%' as has_fixed_search_path
FROM information_schema.routines
WHERE routine_name = 'get_focus_insights'
AND routine_schema = 'public';

-- Testar a função (opcional)
-- SELECT * FROM get_focus_insights(ARRAY[]::TEXT[], ARRAY[]::TEXT[], 3, 3, 10) LIMIT 5;
