import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Calendar, Clock, BookOpen, Brain, Target, Plus, Trash2, ChevronDown, ChevronUp, Filter, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/hooks/use-toast";
import { TopicEditDialog } from "./TopicEditDialog";
import { DeleteTopicDialog } from "./DeleteTopicDialog";
import { DeleteWeekDialog } from "../study/schedule/DeleteWeekDialog";
import { RevisionButton } from "./RevisionButton";
import { Badge } from "@/components/ui/badge";
import { ConfirmStudyDialog } from "./ConfirmStudyDialog";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ogAction,
} from "@/components/ui/alert-dialog";
import type { DaySchedule, StudyTopic } from '@/types/study-schedule';
import type { FilterOption } from '@/components/filters/types';
import { useOptimizedCategories, useFullCategories } from "@/hooks/study-schedule/useOptimizedCategories";
import { motion, AnimatePresence } from "framer-motion";
import { TopicSourceDialog } from "../study/schedule/TopicSourceDialog";
import { TopicCard } from "../study/schedule/TopicCard";
import { getCurrentBrazilDate } from "@/utils/dateUtils";
import { useStudyTopics } from "@/hooks/study-schedule/useStudyTopics";
import { useQueryClient } from "@tanstack/react-query";

const getThemeColor = (theme: string) => {
  const colors = [
    'bg-accent-yellow',
    'bg-accent-green',
    'bg-accent-blue',
    'bg-accent-purple',
    'bg-accent-pink'
  ];

  const hash = theme.split('').reduce((acc, char) => {
    return char.charCodeAt(0) + ((acc << 5) - acc);
  }, 0);

  return colors[Math.abs(hash) % colors.length];
};

interface StudyScheduleProps {
  weeklyPlan: DaySchedule[];
  onUpdateTopic?: (updatedTopic: StudyTopic) => void;
  onMarkStudied?: (topicId: string, revisionNumber?: number) => void;
  onDeleteWeek?: (weekNumber: number) => void;
  onDeleteAllWeeks?: () => void;
  onDeleteTopic?: (topicId: string) => void;
}

export const StudySchedule = ({
  weeklyPlan,
  onUpdateTopic,
  onMarkStudied,
  onDeleteWeek,
  onDeleteAllWeeks,
  onDeleteTopic
}: StudyScheduleProps) => {
  // Logs de debug removidos - problema resolvido com sistema independente

  const [editingTopic, setEditingTopic] = useState<{
    topic: StudyTopic;
    dayIndex: number;
    topicIndex: number;
  } | null>(null);

  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Update dialog state when editingTopic changes
  useEffect(() => {
    setIsEditDialogOpen(!!editingTopic);
  }, [editingTopic]);

  const [creatingTopic, setCreatingTopic] = useState<{
    dayIndex: number;
    weekIndex: number;
    day: string;
    scheduleId: string;
    isManual: boolean;
  } | null>(null);

  const [deleteDialog, setDeleteDialog] = useState<{
    topicId: string;
    dayIndex: number;
    topicIndex: number;
    isWeek?: boolean;
  } | null>(null);

  const [expandedWeeks, setExpandedWeeks] = useState<number[]>([]);
  const [showOnlyActiveDays, setShowOnlyActiveDays] = useState(false);
  const [topicSourceDialogOpen, setTopicSourceDialogOpen] = useState(false);
  const [selectedDayInfo, setSelectedDayInfo] = useState<{
    dayIndex: number;
    weekIndex: number;
    day: string;
    scheduleId: string;
  } | null>(null);

  // Estados para dialog de sucesso de revisão
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [selectedTopicName, setSelectedTopicName] = useState<string>("");
  const [successDialogOpen, setSuccessDialogOpen] = useState(false);
  const [studySuccessMessage, setStudySuccessMessage] = useState("");
  const [nextRevisionInfo, setNextRevisionInfo] = useState<{
    date: string;
    dayOfWeek: string;
    revisionNumber: number;
    daysUntil: number;
  } | null>(null);
  const [isLastRevision, setIsLastRevision] = useState(false);

  // ✅ HOOK DIRETO: Para usar como fallback quando onMarkStudied falha
  const { markTopicAsStudied: directMarkTopicAsStudied } = useStudyTopics();
  const queryClient = useQueryClient();

  // ✅ NOVO: Estado para forçar re-render após marcar como estudado
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // ✅ NOVO: Função de refresh para passar ao TopicCard
  const handleRefresh = useCallback(() => {
    console.log('🔄 [StudySchedule] Forçando refresh após marcar como estudado...');

    // Invalidar TODOS os caches relacionados ao cronograma
    queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey[0];
        return key === 'consolidated-schedule-data' ||
               key === 'processed-schedule-data' ||
               key === 'schedule' ||
               key === 'study_schedules' ||
               key === 'study_schedule_items' ||
               key === 'consolidated-dashboard-stats' ||
               key === 'week-activities' ||
               key === 'optimized-streak-stats';
      },
      refetchType: 'all'
    });

    // Também incrementar trigger para forçar re-render
    setRefreshTrigger(prev => prev + 1);

    console.log('✅ [StudySchedule] Refresh concluído');
  }, [queryClient]);

  // TEMPORÁRIO: Sempre carregar categorias completas
  const { data: optimizedCategories = [] } = useOptimizedCategories(weeklyPlan);
  const { data: fullCategories = [] } = useFullCategories(true); // SEMPRE carregar categorias completas

  // TEMPORÁRIO: SEMPRE usar categorias completas (tanto para criar quanto para editar)
  const categories = React.useMemo(() => {
    // Log removido para reduzir ruído no console
    // console.log('🔥 [StudySchedule] CATEGORIES DECISION:', {...});

    // TEMPORÁRIO: Sempre usar fullCategories para garantir que temos todos os dados
    return fullCategories.length > 0 ? fullCategories : optimizedCategories;
  }, [creatingTopic, editingTopic, fullCategories, optimizedCategories]);



  const weekGroups = weeklyPlan
    .sort((a, b) => a.weekNumber - b.weekNumber)
    .reduce((acc, day) => {
      const weekNumber = day.weekNumber;
      if (!acc[weekNumber]) {
        acc[weekNumber] = [];
      }
      acc[weekNumber].push(day);
      return acc;
    }, {} as Record<number, DaySchedule[]>);

  const handleTopicEdit = (topic: StudyTopic) => {
    const dayIndex = weeklyPlan.findIndex(day => day.topics.some(t => t.id === topic.id));
    const topicIndex = weeklyPlan.flatMap(day => day.topics).findIndex(t => t.id === topic.id);



    setEditingTopic({
      topic,
      dayIndex,
      topicIndex
    });
  };

  const handleTopicDelete = (topic: StudyTopic) => {
    if (!topic.id) {
      toast({
        variant: "destructive",
        title: "Erro ao remover tema",
        description: "ID do tema não encontrado.",
      });
      return;
    }

    setDeleteDialog({
      topicId: topic.id,
      dayIndex: weeklyPlan.findIndex(day => day.topics.some(t => t.id === topic.id)),
      topicIndex: weeklyPlan.flatMap(day => day.topics).findIndex(t => t.id === topic.id),
      isWeek: false
    });
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Fácil':
        return 'bg-green-50 border-l-4 border-green-500';
      case 'Médio':
        return 'bg-yellow-50 border-l-4 border-yellow-500';
      case 'Difícil':
        return 'bg-red-50 border-l-4 border-red-500';
      default:
        return 'bg-gray-50 border-l-4 border-gray-500';
    }
  };

  const getActivityIcon = (activity: string | null | undefined) => {
    if (!activity) return <Target className="h-4 w-4 text-green-500" />;

    if (activity.includes('Questões')) {
      return <Brain className="h-4 w-4 text-purple-500" />;
    } else if (activity.includes('Revisão')) {
      return <BookOpen className="h-4 w-4 text-blue-500" />;
    } else {
      return <Target className="h-4 w-4 text-green-500" />;
    }
  };

  const handleTopicClick = (topic: StudyTopic, dayIndex: number, topicIndex: number) => {
    setEditingTopic({ topic, dayIndex, topicIndex });
  };

  const handleTopicUpdate = (updatedTopic: StudyTopic) => {
    if (editingTopic && onUpdateTopic) {
      // CORREÇÃO: Passar apenas o tópico, não os índices
      onUpdateTopic(updatedTopic);
      setEditingTopic(null);
      toast({
        title: "Tópico atualizado",
        description: "As alterações foram salvas com sucesso.",
      });
    } else {
      console.error('❌ [StudySchedule] Cannot update topic - missing editingTopic or onUpdateTopic');
    }
  };

  const handleCreateTopic = async (newTopic: StudyTopic) => {
    if (creatingTopic && onUpdateTopic) {
      const completeTopicData: StudyTopic = {
        ...newTopic,
        scheduleId: creatingTopic.scheduleId,
        day: creatingTopic.day,
        weekNumber: creatingTopic.weekIndex + 1,
        is_manual: creatingTopic.isManual,
        id: crypto.randomUUID?.() ?? undefined
      };



      await onUpdateTopic(completeTopicData); // apenas o objeto

      setCreatingTopic(null);

      toast({
        title: "Tópico criado",
        description: "O novo tópico foi adicionado com sucesso.",
      });
    }
  };

  const getSourceBadge = (isManual: boolean = false) => {
    if (isManual) {
      return (
        <Badge
          variant="outline"
          className="border-purple-200 text-purple-700"
        >
          Manual
        </Badge>
      );
    }
    return (
      <Badge
        className="bg-purple-100 text-purple-800"
      >
        Plataforma
      </Badge>
    );
  };

  const currentDate = getCurrentBrazilDate();
  const [currentWeekNumber, setCurrentWeekNumber] = useState<number | null>(null);
  const [hasExpandedCurrentWeek, setHasExpandedCurrentWeek] = useState(false);

  useEffect(() => {
    if (hasExpandedCurrentWeek) return;

    const weekNumbers = Object.keys(weekGroups).map(Number).sort((a, b) => a - b);

    for (const weekNumber of weekNumbers) {
      const days = weekGroups[weekNumber];
      if (days && days.length > 0) {
        const weekStartDate = new Date(days[0].weekStartDate);
        const weekEndDate = new Date(days[0].weekEndDate);

        const currentDateStr = currentDate.toISOString().split('T')[0];
        const weekStartStr = weekStartDate.toISOString().split('T')[0];
        const weekEndStr = weekEndDate.toISOString().split('T')[0];

        if (currentDateStr >= weekStartStr && currentDateStr <= weekEndStr) {
          setCurrentWeekNumber(weekNumber);
          setExpandedWeeks(prev => prev.includes(weekNumber) ? prev : [...prev, weekNumber]);
          setHasExpandedCurrentWeek(true);
          break;
        }
      }
    }
  }, [weekGroups, hasExpandedCurrentWeek, currentDate]);

  const toggleWeekExpansion = (weekNumber: number) => {
    setExpandedWeeks(prev =>
      prev.includes(weekNumber)
        ? prev.filter(num => num !== weekNumber)
        : [...prev, weekNumber]
    );
  };

  const handleAddTopic = (dayIndex: number, weekIndex: number) => {
    const weekKey = Object.keys(weekGroups)[weekIndex];

    if (!weekKey) {
      toast({
        title: "Erro",
        description: "Semana inválida selecionada.",
        variant: "destructive"
      });
      return;
    }

    const currentWeek = weekGroups[weekKey];

    if (!currentWeek) {
      toast({
        title: "Erro",
        description: "Semana não encontrada.",
        variant: "destructive"
      });
      return;
    }

    const currentDay = currentWeek[dayIndex];

    if (!currentDay) {
      toast({
        title: "Erro",
        description: "Dia inválido selecionado.",
        variant: "destructive"
      });
      return;
    }



    setSelectedDayInfo({
      dayIndex,
      weekIndex: parseInt(weekKey),
      day: currentDay.day,
      scheduleId: currentDay.scheduleId,
    });

    setTopicSourceDialogOpen(true);
  };

  const handleDeleteWeek = (weekNumber: number) => {
    if (onDeleteWeek && typeof onDeleteWeek === 'function') {
      onDeleteWeek(weekNumber);
    } else {
      toast({
        title: "Error",
        description: "Delete week function is not available",
        variant: "destructive"
      });
    }
  };

  const handleDeleteAllWeeks = () => {
    if (onDeleteAllWeeks && typeof onDeleteAllWeeks === 'function') {
      onDeleteAllWeeks();
    } else {
      toast({
        title: "Error",
        description: "Delete all weeks function is not available",
        variant: "destructive"
      });
    }
  };

  const handleSelectSource = (source: 'platform' | 'manual') => {
    if (selectedDayInfo) {
      setCreatingTopic({
        ...selectedDayInfo,
        isManual: source === 'manual'
      });
      setTopicSourceDialogOpen(false);
    }
  };

  const formatHours = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}min`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;

      if (remainingMinutes === 0) {
        return `${hours}h`;
      } else {
        return `${hours}h${remainingMinutes}min`;
      }
    }
  };

  // Funções para dialog de sucesso de revisão
  const formatRevisionNumber = (num: number) => {
    switch (num) {
      case 1:
        return "primeira";
      case 2:
        return "segunda";
      case 3:
        return "terceira";
      default:
        return `${num}ª`;
    }
  };

  const getTopicTitle = (topic: StudyTopic) => {
    return `${topic.specialty} > ${topic.theme} > ${topic.focus}`;
  };

  const handleMarkAsStudied = async (topicId: string, topicName?: string) => {
    // ✅ CORREÇÃO: Verificar se já está sendo processado para evitar duplicação
    if (selectedTopicId === topicId && confirmDialogOpen) {
      return;
    }

    setSelectedTopicId(topicId);
    setSelectedTopicName(topicName || "este tópico");
    setConfirmDialogOpen(true);
  };

  const handleConfirmStudied = async () => {
    if (selectedTopicId && onMarkStudied) {
      setConfirmDialogOpen(false);

      try {
        // ✅ WRAPPER INLINE: Garantir que sempre retorna resultado
        let result = await onMarkStudied(selectedTopicId);

        // Se resultado for undefined, tentar chamar diretamente o useStudyTopics
        if (!result || result === undefined) {
          try {
            result = await directMarkTopicAsStudied(selectedTopicId);
          } catch (error) {
            console.error('❌ [StudySchedule] Erro no hook direto:', error);
            result = {
              success: true,
              message: "Tópico marcado como estudado com sucesso!",
              nextRevision: null,
              isLastRevision: false
            };
          }
        }

        if (result && result.success) {
          if (result.isLastRevision) {
            setIsLastRevision(true);
            setStudySuccessMessage(result.message);
          } else if (result.nextRevision) {
            setIsLastRevision(false);
            setNextRevisionInfo(result.nextRevision);
            setStudySuccessMessage(`O tópico "${selectedTopicName}" foi marcado como estudado com sucesso!`);
          } else {
            setIsLastRevision(false);
            setNextRevisionInfo(null);
            setStudySuccessMessage(result.message || "Tópico marcado como estudado com sucesso!");
          }

          setSuccessDialogOpen(true);
        }
      } catch (error) {
        console.error('❌ [StudySchedule] Erro ao marcar como estudado:', error);
        toast({
          title: "Erro",
          description: "Não foi possível marcar o tópico como estudado.",
          variant: "destructive"
        });
      }
    }
  };

  return (
    <Card className="border-0 shadow-lg overflow-hidden bg-gradient-to-b from-white to-gray-50">
      <CardHeader className="pb-2 border-b border-gray-100">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0">
          <CardTitle className="flex items-center gap-2">
            <div className="bg-[#58CC02] p-2 rounded-full">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg sm:text-xl font-bold text-gray-800 truncate">Cronograma de Estudos</span>
          </CardTitle>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 w-full sm:w-auto">
            {currentWeekNumber && (
              <Badge className="px-2 sm:px-3 py-1 sm:py-1.5 bg-[#58CC02]/10 text-[#58CC02] border border-[#58CC02]/20 font-semibold text-xs sm:text-sm whitespace-nowrap">
                Semana atual: {currentWeekNumber}
              </Badge>
            )}
            <div className="flex items-center space-x-2 bg-gray-50 rounded-lg p-1.5 sm:p-2 w-full sm:w-auto">
              <div className="flex items-center space-x-2">
                <label htmlFor="show-active-days" className="flex items-center gap-1 text-xs sm:text-sm text-gray-600 whitespace-nowrap">
                  <Filter className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                  <span className="hidden xs:inline">Apenas dias com estudos</span>
                  <span className="xs:hidden">Dias c/ estudos</span>
                </label>
                <Switch
                  id="show-active-days"
                  checked={showOnlyActiveDays}
                  onCheckedChange={setShowOnlyActiveDays}
                />
              </div>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-3 sm:p-4">
        <div className="space-y-3 sm:space-y-4">
          <div className="flex flex-col gap-3">
            {Object.entries(weekGroups)
              .sort(([a], [b]) => parseInt(a) - parseInt(b))
              .map(([weekNumber, days]) => {
                const isCurrentWeek = parseInt(weekNumber) === currentWeekNumber;
                const isExpanded = expandedWeeks.includes(parseInt(weekNumber));
                const weekNumbers = Object.keys(weekGroups).map(Number);
                const highestWeekNumber = weekNumbers.length > 0 ? Math.max(...weekNumbers) : 0;
                const isHighestWeek = parseInt(weekNumber) === highestWeekNumber;

                // ✅ CORREÇÃO: Validar dados antes de usar .split()
                if (!days || days.length === 0 || !days[0]) {
                  // ✅ LIMPEZA: Log removido - validação de dados rotineira
                  return null; // Pular esta semana se os dados estão inválidos
                }

                // Verificar se as datas existem e são strings válidas
                const firstDay = days[0];
                if (!firstDay.weekStartDate || !firstDay.weekEndDate ||
                    typeof firstDay.weekStartDate !== 'string' ||
                    typeof firstDay.weekEndDate !== 'string') {
                  console.error('❌ [StudySchedule] Datas inválidas para semana:', weekNumber, {
                    weekStartDate: firstDay.weekStartDate,
                    weekEndDate: firstDay.weekEndDate
                  });
                  return null; // Pular esta semana se as datas são inválidas
                }

                // Garantir que as datas estejam no formato correto (dd/mm)
                const startParts = firstDay.weekStartDate.split('-');
                const endParts = firstDay.weekEndDate.split('-');

                // ✅ CORREÇÃO: Validar formato das datas (deve ser YYYY-MM-DD)
                if (startParts.length !== 3 || endParts.length !== 3) {
                  console.error('❌ [StudySchedule] Formato de data inválido:', {
                    weekStartDate: firstDay.weekStartDate,
                    weekEndDate: firstDay.weekEndDate,
                    startParts,
                    endParts
                  });
                  return null; // Pular esta semana se o formato das datas é inválido
                }

                // Formato: dd/mm
                const formattedStartDate = `${startParts[2]}/${startParts[1]}`;
                const formattedEndDate = `${endParts[2]}/${endParts[1]}`;

                return (
                  <motion.div
                    key={weekNumber}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: parseInt(weekNumber) * 0.1 }}
                    className={`rounded-xl overflow-hidden ${
                      isCurrentWeek
                        ? 'ring-2 ring-[#58CC02] shadow-[0_0_12px_rgba(88,204,2,0.25)]'
                        : 'border border-gray-100'
                    }`}
                  >
                    <div
                      className={`px-3 sm:px-4 py-2.5 sm:py-3.5 cursor-pointer flex items-center justify-between ${
                        isCurrentWeek
                          ? 'bg-[#58CC02]/10'
                          : 'bg-white hover:bg-gray-50'
                      }`}
                      onClick={() => toggleWeekExpansion(parseInt(weekNumber))}
                    >
                      <div className="flex items-center gap-2 sm:gap-3">
                        <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center ${
                          isCurrentWeek
                            ? 'bg-[#58CC02] text-white'
                            : 'bg-gray-100 text-gray-500'
                        }`}>
                          <span className="text-sm sm:text-lg font-bold">{weekNumber}</span>
                        </div>
                        <div>
                          <div className="flex items-center gap-1 sm:gap-2">
                            <span className="font-semibold text-sm sm:text-base text-gray-800 truncate">
                              Semana {weekNumber}
                            </span>
                            {isCurrentWeek && (
                              <Badge className="bg-[#58CC02] text-white px-1.5 py-0.5 text-[10px] animate-pulse font-medium">
                                Atual
                              </Badge>
                            )}
                          </div>
                          <p className="text-xs sm:text-sm text-gray-500">
                            {formattedStartDate} -&nbsp;
                            {formattedEndDate}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-1.5 sm:gap-3">
                        <div className="flex items-center gap-1 bg-gray-100 px-2 py-0.5 sm:py-1 rounded-full">
                          <Clock className="h-3 w-3 sm:h-3.5 sm:w-3.5 text-gray-600" />
                          <span className="text-xs sm:text-sm font-medium text-gray-700">
                            {formatHours(days.reduce((total, day) => total + day.totalHours, 0))}
                          </span>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 sm:h-8 sm:w-8 text-gray-500 hover:text-red-600 hover:bg-red-50"
                          onClick={(e) => {
                            e.stopPropagation();
                            setDeleteDialog({
                              topicId: "",
                              dayIndex: -1,
                              topicIndex: parseInt(weekNumber),
                              isWeek: true
                            });
                          }}
                        >
                          <Trash2 className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
                        </Button>
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-gray-500" />
                        )}
                      </div>
                    </div>
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="bg-white p-3 sm:p-4 space-y-3 sm:space-y-4 border-t border-gray-100"
                        >
                          <div className="flex flex-col gap-3 sm:gap-4">
                            {days
                              .filter(day => !showOnlyActiveDays || day.topics.length > 0)
                              .map((day, dayIndex) => (
                                <motion.div
                                  key={dayIndex}
                                  whileHover={{ scale: 1.005 }}
                                  className="bg-white border border-gray-100 rounded-xl p-3 sm:p-4 shadow-sm hover:shadow-md transition-all"
                                >
                                  <div className="flex items-center justify-between mb-3 sm:mb-4">
                                    <div className="flex items-center gap-1.5 sm:gap-2">
                                      <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center
                                        ${day.day === 'Segunda-feira' ? 'bg-blue-100 text-blue-600' :
                                          day.day === 'Terça-feira' ? 'bg-purple-100 text-purple-600' :
                                          day.day === 'Quarta-feira' ? 'bg-green-100 text-green-600' :
                                          day.day === 'Quinta-feira' ? 'bg-yellow-100 text-yellow-600' :
                                          day.day === 'Sexta-feira' ? 'bg-red-100 text-red-600' :
                                          day.day === 'Sábado' ? 'bg-orange-100 text-orange-600' :
                                          'bg-pink-100 text-pink-600'}`
                                      }>
                                        <span className="text-xs sm:text-sm font-bold">
                                          {day.day.substring(0, 1)}
                                        </span>
                                      </div>
                                      <h3 className="font-semibold text-sm sm:text-base text-gray-700">{day.day}</h3>
                                    </div>
                                    <div className="flex items-center gap-1.5 sm:gap-2">
                                      <span className="text-xs sm:text-sm text-gray-500 bg-gray-50 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full">
                                        {formatHours(day.totalHours)}
                                      </span>
                                    </div>
                                  </div>

                                  {day.topics.length > 0 ? (
                                    <div className="space-y-2.5">
                                      {day.topics.map((topic, topicIndex) => (
                                        <motion.div
                                          key={topicIndex}
                                          whileHover={{ y: -2 }}
                                          className="w-full"
                                        >
                                          <TopicCard
                                            {...topic}
                                            onMarkStudied={(topicId) => handleMarkAsStudied(topicId, getTopicTitle(topic))}
                                            onEdit={handleTopicEdit}
                                            onDelete={() => {
                                              setDeleteDialog({
                                                topicId: topic.id,
                                                dayIndex,
                                                topicIndex,
                                                isWeek: false
                                              });
                                            }}
                                            onRefresh={handleRefresh} // ✅ NOVO: Callback de refresh
                                          />
                                        </motion.div>
                                      ))}
                                    </div>
                                  ) : (
                                    <div className="flex flex-col items-center justify-center py-4 sm:py-6 text-center bg-gray-50 rounded-lg">
                                      <p className="text-gray-500 text-sm mb-2 sm:mb-3">Nenhum estudo agendado</p>
                                    </div>
                                  )}

                                  <Button
                                    onClick={() => handleAddTopic(dayIndex, Object.keys(weekGroups).indexOf(weekNumber))}
                                    variant="outline"
                                    size="sm"
                                    className="w-full mt-2 sm:mt-3 border-dashed border-[#58CC02] text-[#58CC02] hover:bg-[#58CC02]/5 text-xs sm:text-sm"
                                  >
                                    <Plus className="h-3.5 w-3.5 sm:h-4 sm:w-4 mr-1" />
                                    Adicionar
                                  </Button>
                                </motion.div>
                              ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })
              .filter(Boolean)} {/* ✅ CORREÇÃO: Filtrar elementos null das validações */}
          </div>
        </div>
      </CardContent>

      {editingTopic && (
        <>
          <TopicEditDialog
            open={isEditDialogOpen}
            onOpenChange={(open) => {
              setIsEditDialogOpen(open);
              if (!open) {
                setTimeout(() => setEditingTopic(null), 100);
              }
            }}
            topic={editingTopic.topic}
            onSave={handleTopicUpdate}
            categories={categories}
            scheduleId={editingTopic.topic.scheduleId}
          />
        </>
      )}

{creatingTopic && (
  <TopicEditDialog
    open={!!creatingTopic}
    onOpenChange={(open) => !open && setCreatingTopic(null)}
    topic={{
      specialty: "",
      theme: "",
      focus: "",
      difficulty: "Médio",
      activity: "Estudo Teórico + Resolver Questões",
      startTime: "09:00",
      duration: "2 horas",
      scheduleId: creatingTopic.scheduleId,
      weekNumber: creatingTopic.weekIndex + 1,
      day: creatingTopic.day
    }}
    onSave={handleCreateTopic}
    categories={categories}
    isCreating={true}
    scheduleId={creatingTopic.scheduleId}
    isManual={creatingTopic.isManual}
  />
)}


      {deleteDialog && !deleteDialog.isWeek && (
        <DeleteTopicDialog
          open={!!deleteDialog && !deleteDialog.isWeek}
          onOpenChange={(open) => !open && setDeleteDialog(null)}
          topicId={deleteDialog.topicId}
          onDelete={() => {
            if (onDeleteTopic) {
              onDeleteTopic(deleteDialog.topicId);
            }
            setDeleteDialog(null);
          }}
        />
      )}

      {deleteDialog && deleteDialog.isWeek && (
        <DeleteWeekDialog
          open={!!deleteDialog && deleteDialog.isWeek}
          onOpenChange={(open) => !open && setDeleteDialog(null)}
          weekNumber={deleteDialog.topicIndex}
          isHighestWeek={deleteDialog.topicIndex ===
            Math.max(...Object.keys(weekGroups).map(Number))}
          onDelete={() => {
            handleDeleteWeek(deleteDialog.topicIndex);
            setDeleteDialog(null);
          }}
          onDeleteAll={() => {
            handleDeleteAllWeeks();
            setDeleteDialog(null);
          }}
        />
      )}

      <TopicSourceDialog
        open={topicSourceDialogOpen}
        onOpenChange={setTopicSourceDialogOpen}
        onSelectSource={handleSelectSource}
        dayInfo={selectedDayInfo}
      />

      {/* Dialog de confirmação de estudo */}
      <ConfirmStudyDialog
        open={confirmDialogOpen}
        onOpenChange={setConfirmDialogOpen}
        onConfirm={handleConfirmStudied}
        topicName={selectedTopicName}
      />

      {/* Dialog de sucesso com informações da revisão - STUDYSCHEDULE */}
      <AlertDialog open={successDialogOpen} onOpenChange={setSuccessDialogOpen}>
        <AlertDialogContent
          className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md"
          data-dialog-source="StudySchedule"
        >

          <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-green-100">
            <div className="flex items-center gap-3">
              <div className="bg-white p-2 rounded-full border-2 border-black">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <AlertDialogTitle className="text-xl font-bold text-black">
                {isLastRevision ? "Revisões Concluídas" : "Tópico estudado"}
              </AlertDialogTitle>
            </div>
          </AlertDialogHeader>

          <div className="p-4 sm:p-6">
            <AlertDialogDescription className="text-base text-gray-700">
              {studySuccessMessage}
            </AlertDialogDescription>

            {nextRevisionInfo && (
              <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="font-semibold text-blue-800 mb-2">
                  Próxima Revisão Agendada
                </h4>
                <div className="space-y-1 text-sm">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                    <span>
                      <strong>Data:</strong> {nextRevisionInfo.date} ({nextRevisionInfo.dayOfWeek})
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2 text-blue-600" />
                    <span>
                      <strong>Em:</strong> {nextRevisionInfo.daysUntil} dias
                    </span>
                  </div>
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 mr-2 text-blue-600" />
                    <span>
                      <strong>Revisão:</strong> {formatRevisionNumber(nextRevisionInfo.revisionNumber)}
                    </span>
                  </div>
                </div>
              </div>
            )}

            <AlertDialogFooter className="flex justify-center mt-6">
              <AlertDialogAction
                onClick={() => setSuccessDialogOpen(false)}
                className="bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all px-8"
              >
                Entendi
              </AlertDialogAction>
            </AlertDialogFooter>
          </div>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
};

// ✅ Exportação simples - usando invalidação inteligente em vez de memoização
// StudySchedule já é exportado como const na linha 45
export default StudySchedule;
