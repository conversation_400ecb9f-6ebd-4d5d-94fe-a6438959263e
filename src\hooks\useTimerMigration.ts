import { useRobustSessionTimer } from './useRobustSessionTimer';

/**
 * 🔄 HOOK DE MIGRAÇÃO - Compatibilidade com Sistema Antigo
 * 
 * Mantém a mesma interface do useSessionTimer antigo
 * mas usa o novo sistema robusto por baixo
 */

export const useSessionTimer = (sessionId: string, userId: string) => {
  const timer = useRobustSessionTimer(sessionId, userId);

  // ✅ TEMPO VISUAL REATIVO - Atualiza automaticamente
  const visualTime = timer.getVisualTime();

  // ✅ Interface compatível + nova funcionalidade
  return {
    // Interface antiga mantida
    totalElapsedTime: visualTime, // ✅ Tempo visual reativo
    currentQuestionTime: timer.getQuestionTime(),
    startQuestionTimer: timer.startQuestionTimer,
    pauseCurrentTimer: timer.pauseCurrentTimer,
    getQuestionTime: timer.getQuestionTime,
    finishSession: timer.finishSession,
    isActive: timer.isActive,

    // Novas funcionalidades
    saveOnAnswer: timer.saveOnAnswer, // ✅ CRÍTICO: Salvar ao marcar resposta
    getTotalSessionTime: () => visualTime,
    getActiveStudyTime: timer.getActiveStudyTime,

    // Debug
    _debugState: timer._debugState
  };
};
