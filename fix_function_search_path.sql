-- =====================================================
-- CORREÇÃO DE SEARCH PATH MUTABLE NAS FUNÇÕES
-- =====================================================
-- Execute este arquivo no SQL Editor do Supabase para corrigir
-- os problemas de segurança relacionados ao search_path mutable

-- =====================================================
-- 1. CORRIGIR get_institutions_with_questions
-- =====================================================

CREATE OR REPLACE FUNCTION public.get_institutions_with_questions()
RETURNS TABLE(
  id UUID,
  name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN 
  RETURN QUERY 
  SELECT DISTINCT 
    el.id, 
    el.name 
  FROM exam_locations el 
  INNER JOIN questions q ON el.id = q.exam_location 
  ORDER BY el.name; 
END; 
$$;

-- =====================================================
-- 2. CORRIGIR get_categories_by_ids
-- =====================================================

CREATE OR REPLACE FUNCTION public.get_categories_by_ids(category_ids UUID[])
RETURNS TABLE(
  id UUID,
  name TEXT,
  type TEXT,
  parent_id UUID
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Validação de entrada
  IF category_ids IS NULL OR array_length(category_ids, 1) IS NULL THEN
    RETURN;
  END IF;

  -- ✅ AUMENTO: Limitar a 1000 IDs para suportar casos extremos
  IF array_length(category_ids, 1) > 1000 THEN
    RAISE EXCEPTION 'Máximo de 1000 IDs permitidos por consulta';
  END IF;

  -- Retornar categorias correspondentes aos IDs fornecidos
  RETURN QUERY
  SELECT 
    sc.id,
    sc.name,
    sc.type,
    sc.parent_id
  FROM study_categories sc
  WHERE sc.id = ANY(category_ids)
  ORDER BY sc.type, sc.name;
  
END;
$$;

-- =====================================================
-- 3. CORRIGIR get_user_sessions_optimized
-- =====================================================

CREATE OR REPLACE FUNCTION public.get_user_sessions_optimized(
  user_id_param UUID,
  page_number INTEGER DEFAULT 1,
  items_per_page INTEGER DEFAULT 20
)
RETURNS TABLE(
  id UUID,
  title TEXT,
  started_at TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  total_questions INTEGER,
  correct_answers BIGINT,
  answered_questions BIGINT,
  specialty_name TEXT,
  knowledge_domain TEXT,
  status TEXT,
  total_time_spent INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ss.id,
    ss.title,
    ss.started_at,
    ss.completed_at,
    ss.total_questions,
    -- Usar agregação otimizada em vez de subqueries
    COALESCE(ua_stats.correct_answers, 0) as correct_answers,
    COALESCE(ua_stats.answered_questions, 0) as answered_questions,
    sc.name as specialty_name,
    ss.knowledge_domain,
    ss.status,
    ss.total_time_spent
  FROM study_sessions ss
  LEFT JOIN study_categories sc ON ss.specialty_id = sc.id
  LEFT JOIN (
    -- Agregação otimizada em uma única query
    SELECT 
      session_id,
      COUNT(*) as answered_questions,
      COUNT(*) FILTER (WHERE is_correct = true) as correct_answers
    FROM user_answers
    GROUP BY session_id
  ) ua_stats ON ss.id = ua_stats.session_id
  WHERE ss.user_id = user_id_param
    AND ss.status IN ('completed', 'in_progress', 'abandoned')
  ORDER BY ss.started_at DESC
  LIMIT items_per_page
  OFFSET (page_number - 1) * items_per_page;
END;
$$;
