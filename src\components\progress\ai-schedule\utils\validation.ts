
export const isValidTimeRange = (startTime: string, endTime: string): boolean => {
  // ✅ VALIDAÇÃO RIGOROSA: Campos vazios ou apenas espaços não são permitidos
  if (!startTime || !endTime || startTime.trim() === '' || endTime.trim() === '') {
    return false;
  }

  // Validar formato HH:MM
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (!timeRegex.test(startTime) || !timeRegex.test(endTime)) {
    return false;
  }

  const start = new Date(`2000-01-01T${startTime}`);
  const end = new Date(`2000-01-01T${endTime}`);

  // Check if end time is after start time
  if (end <= start) return false;

  // Check if the difference is at least 15 minutes
  const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60);
  return diffMinutes >= 15;
};

export const validateForm = (data: any, userPreferences?: any) => {
  const errors: string[] = [];

  // Check if at least one day is selected
  const enabledDays = Object.values(data.availableDays).filter((day: any) => day.enabled);
  if (enabledDays.length === 0) {
    errors.push("Selecione pelo menos um dia da semana");
  }

  // Validate time ranges
  let hasInvalidTimeRange = false;
  let hasEmptyFields = false;

  enabledDays.forEach((dayConfig: any) => {
    dayConfig.periods.forEach((period: any) => {
      // ✅ VERIFICAR CAMPOS VAZIOS PRIMEIRO
      if (!period.startTime || !period.endTime ||
          period.startTime.trim() === '' || period.endTime.trim() === '') {
        hasEmptyFields = true;
      } else if (!isValidTimeRange(period.startTime, period.endTime)) {
        hasInvalidTimeRange = true;
      }
    });
  });

  if (hasEmptyFields) {
    errors.push("Preencha todos os horários de início e término para os dias selecionados");
  } else if (hasInvalidTimeRange) {
    errors.push("Verifique os horários. Cada período deve ter no mínimo 15 minutos");
  }

  // Validate schedule option
  if (data.scheduleOption === "existing" && !data.targetWeek) {
    errors.push("Selecione uma semana existente");
  }

  // Validate week count
  if (data.scheduleOption === "new") {
    const weekCountNumber = typeof data.weeksCount === 'number' ? data.weeksCount : parseInt(data.weeksCount);
    if (isNaN(weekCountNumber) || weekCountNumber < 1 || weekCountNumber > 50) {
      errors.push("O número de semanas deve estar entre 1 e 50");
    }
  }

  // Validate institution-based generation
  if (data.generationMode === "institution_based") {
    // ✅ CORREÇÃO: Verificar se há instituições nas preferências OU selecionadas manualmente
    const hasManualInstitutions = data.institutionIds && data.institutionIds.length > 0;
    const hasPreferenceInstitutions = userPreferences?.target_institutions &&
                                     userPreferences.target_institutions.length > 0 &&
                                     !userPreferences.target_institutions_unknown;

    if (!hasManualInstitutions && !hasPreferenceInstitutions) {
      errors.push("Configure suas instituições alvo nas preferências ou selecione instituições específicas");
    }

    // Validate year range if provided
    if (data.startYear && data.endYear && data.startYear > data.endYear) {
      errors.push("O ano inicial deve ser menor ou igual ao ano final");
    }
  }

  return { isValid: errors.length === 0, errors };
};
