import React, { useState, useEffect } from 'react';
import { useUser } from '@supabase/auth-helpers-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

export const useScheduleTutorial = () => {
  const user = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [tutorialCompleted, setTutorialCompleted] = useState<boolean | null>(null);



  // Buscar status do tutorial
  useEffect(() => {
    const fetchTutorialStatus = async () => {
      if (!user?.id) return;

      try {
        const { data, error } = await supabase
          .from('user_preferences')
          .select('schedule_tutorial_completed')
          .eq('user_id', user.id)
          .single();

        if (error) {
          return;
        }

        const completed = data?.schedule_tutorial_completed || false;
        setTutorialCompleted(completed);
      } catch (error) {
        // Erro silencioso
      }
    };

    fetchTutorialStatus();
  }, [user?.id]);

  // Marcar tutorial como completo
  const markTutorialCompleted = async () => {
    if (!user?.id || isLoading) return; // ✅ CORREÇÃO: Evitar múltiplas chamadas

    setIsLoading(true);

    try {
      // ✅ CORREÇÃO: Usar upsert em vez de update para evitar conflitos
      const { error } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          schedule_tutorial_completed: true,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id'
        });

      if (error) {
        toast.error('Erro ao salvar progresso do tutorial');
        return;
      }

      setTutorialCompleted(true);
      toast.success('Tutorial concluído!');
    } catch (error) {
      toast.error('Erro ao salvar progresso do tutorial');
    } finally {
      setIsLoading(false);
    }
  };

  return {
    tutorialCompleted,
    markTutorialCompleted,
    isLoading
  };
};
