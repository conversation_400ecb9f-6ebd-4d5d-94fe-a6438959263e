import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Brain, Target, Crosshair } from "lucide-react";
import { SessionStats } from "@/types/studySession";
import { supabase } from "@/integrations/supabase/client";
import { SkillTreeSection } from './SkillTreeSection';

/**
 * ✅ FUNÇÃO AUXILIAR: Chunking para IDs grandes
 * Divide array de IDs em chunks menores para evitar URLs muito longas
 */
const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

/**
 * ✅ FUNÇÃO AUXILIAR: Buscar categorias com chunking
 * Fallback para casos onde RPC falha ou não está disponível
 */
const fetchCategoriesWithChunking = async (categoryIds: string[], chunkSize: number = 50) => {
  const chunks = chunkArray(categoryIds, chunkSize);
  const allResults: any[] = [];



  for (const chunk of chunks) {
    const { data, error } = await supabase
      .from("study_categories")
      .select("id, name, type, parent_id")
      .in("id", chunk);

    if (error) {
      throw error;
    }

    if (data) {
      allResults.push(...data);
    }
  }

  return allResults;
};

interface SessionSkillTreeProps {
  stats: SessionStats;
}

export const SessionSkillTree = ({ stats }: SessionSkillTreeProps) => {
  const [categories, setCategories] = useState<Record<string, any>>({});

  useEffect(() => {
    const loadCategories = async () => {
      // Collect all category IDs from stats
      const categoryIds = new Set([
        ...Object.keys(stats.by_specialty || {}),
        ...Object.keys(stats.by_theme || {}),
        ...Object.keys(stats.by_focus || {})
      ]);

      if (categoryIds.size === 0) {
        console.log('⚠️ No categories found in stats');
        return;
      }

      console.log('📊 [SessionSkillTree] Loading categories...');

      // ✅ SOLUÇÃO: Usar RPC quando há muitos IDs para evitar URLs muito longas
      const categoryIdsArray = Array.from(categoryIds);
      let data, error;

      if (categoryIdsArray.length > 50) {


        try {
          const rpcResult = await supabase.rpc('get_categories_by_ids', {
            category_ids: categoryIdsArray
          });

          if (rpcResult.error) {

            // Fallback para chunking
            data = await fetchCategoriesWithChunking(categoryIdsArray);
            error = null;
          } else {
            data = rpcResult.data;
            error = rpcResult.error;
          }
        } catch (err) {

          // Fallback para chunking
          data = await fetchCategoriesWithChunking(categoryIdsArray);
          error = null;
        }
      } else {
        // Usar query normal para poucos IDs
        const result = await supabase
          .from('study_categories')
          .select('id, name, type, parent_id')
          .in('id', categoryIdsArray);
        data = result.data;
        error = result.error;
      }

      if (error) {
        console.error('❌ Error fetching categories:', error);
        return;
      }

      console.log('✅ [SessionSkillTree] Categories loaded:', data?.length || 0);

      const categoryMap = (data || []).reduce((acc, cat) => ({
        ...acc,
        [cat.id]: cat
      }), {});

      setCategories(categoryMap);
    };

    loadCategories();
  }, [stats]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Árvore de Habilidades da Sessão</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <SkillTreeSection
          title="Especialidades"
          data={stats.by_specialty}
          categories={categories}
          icon={<Brain className="h-4 w-4" />}
        />
        <SkillTreeSection
          title="Temas"
          data={stats.by_theme}
          categories={categories}
          icon={<Target className="h-4 w-4" />}
        />
        <SkillTreeSection
          title="Focos"
          data={stats.by_focus}
          categories={categories}
          icon={<Crosshair className="h-4 w-4" />}
        />
      </CardContent>
    </Card>
  );
};