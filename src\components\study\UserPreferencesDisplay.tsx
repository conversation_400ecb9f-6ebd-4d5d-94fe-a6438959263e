import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Building2,
  Settings,
  CheckCircle,
  AlertCircle,
  Shuffle
} from 'lucide-react';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import { useNavigate } from 'react-router-dom';

export const UserPreferencesDisplay: React.FC = () => {
  const { preferences, isLoading } = useStudyPreferences();
  const navigate = useNavigate();



  if (isLoading) {
    return (
      <Card className="border-2 border-gray-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="ml-2 text-gray-600">Carregando preferências...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!preferences?.preferences_completed) {
    return (
      <Card className="border-2 border-orange-200 bg-orange-50">
        <CardContent className="p-4 sm:p-6">
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-orange-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-orange-800">Preferências não configuradas</h3>
                <p className="text-sm text-orange-700">
                  Configure suas preferências para cronogramas mais personalizados ou gere um cronograma aleatório
                </p>
              </div>
            </div>

            <div className="space-y-3">
              {/* Botão de configurar */}
              <Button
                onClick={() => navigate('/settings')}
                size="sm"
                className="w-full bg-orange-600 hover:bg-orange-700 text-white"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configurar Preferências
              </Button>

              {/* Ou gerar aleatório */}
              <div className="space-y-2">
                <p className="text-xs font-medium text-orange-700 text-center">
                  Ou gere um cronograma sem configurar:
                </p>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full border-orange-300 text-orange-700 hover:bg-orange-100"
                  onClick={() => {
                    const event = new CustomEvent('generateRandomSchedule');
                    window.dispatchEvent(event);
                  }}
                >
                  <Shuffle className="h-4 w-4 mr-2" />
                  Gerar Cronograma Aleatório
                </Button>

                <p className="text-xs text-orange-600 text-center">
                  Distribui todos os temas de forma equilibrada
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-4">
      {/* Header compacto */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <h4 className="text-sm font-semibold text-gray-800">Suas Preferências</h4>
        </div>
        <Button
          onClick={() => navigate('/settings')}
          variant="ghost"
          size="sm"
          className="h-7 px-2 text-xs text-green-700 hover:bg-green-100"
        >
          <Settings className="h-3 w-3 mr-1" />
          Editar
        </Button>
      </div>

      {/* Apenas Instituições */}
      <div className="flex items-start gap-2 text-xs">
        <Building2 className="h-3 w-3 text-green-600 flex-shrink-0 mt-0.5" />
        <div className="min-w-0 flex-1">
          <p className="text-gray-500 mb-1">Instituições Alvo</p>
          {preferences.target_institutions_unknown ? (
            <p className="font-medium text-gray-700">Qualquer instituição</p>
          ) : preferences.target_institutions.length > 0 ? (
            <div className="space-y-1">
              <p className="font-medium text-gray-700">
                {preferences.target_institutions.slice(0, 5).map(inst => inst.name).join(', ')}
                {preferences.target_institutions.length > 5 && '...'}
              </p>
              {preferences.target_institutions.length > 5 && (
                <p className="text-gray-500">
                  e mais {preferences.target_institutions.length - 5} instituições
                </p>
              )}
            </div>
          ) : (
            <p className="font-medium text-gray-700">Nenhuma selecionada</p>
          )}
        </div>
      </div>

    </div>
  );
};
