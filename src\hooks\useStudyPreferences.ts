import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useOptimizedStudyPreferencesData } from '@/hooks/useConsolidatedUserPreferences';
import { useMedicalSpecialtyById } from '@/hooks/useMedicalSpecialties';

export interface StudyPreferences {
  target_specialty: string | null;
  target_specialty_name: string | null;
  study_months: number | null;
  preferences_completed: boolean;
  target_institutions_unknown: boolean;
  target_institutions: Array<{
    id: string;
    name: string;
  }>;
}

export interface StudyPreferencesFormData {
  target_specialty: string;
  specialty_unknown: boolean;
  study_months: number;
  target_institutions: string[];
  institutions_unknown: boolean;
}

export const useStudyPreferences = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // ✅ OTIMIZADO: Usar dados consolidados para user_preferences
  const {
    studyPreferencesData,
    isLoading: preferencesLoading,
    error: preferencesError
  } = useOptimizedStudyPreferencesData();

  // Query para buscar preferências completas (incluindo instituições)
  const {
    data: preferences,
    isLoading: institutionsLoading,
    error
  } = useQuery({
    queryKey: ['study-preferences-complete', user?.id], // ✅ CHAVE ÚNICA
    queryFn: async (): Promise<StudyPreferences | null> => {
      if (!user?.id || !studyPreferencesData) return null;

  

      // Usar dados consolidados para user_preferences
      const userPrefs = studyPreferencesData;

      // ✅ OTIMIZADO: Buscar nome da especialidade do cache
      let specialtyName = null;
      if (userPrefs.target_specialty) {
        // Buscar do cache do hook centralizado
        const specialties = queryClient.getQueryData(['medical-specialties']) as any[];
        const specialty = specialties?.find(s => s.id === userPrefs.target_specialty);
        specialtyName = specialty?.name || null;
      }

      // ✅ OTIMIZADO: Usar instituições dos dados consolidados (evita query duplicada)
      const mappedInstitutions = studyPreferencesData?.target_institutions || [];

      return {
        target_specialty: userPrefs.target_specialty,
        target_specialty_name: specialtyName,
        study_months: userPrefs.study_months,
        preferences_completed: userPrefs.preferences_completed || false,
        target_institutions_unknown: userPrefs.target_institutions_unknown || false,
        target_institutions: mappedInstitutions
      };
    },
    enabled: !!user?.id && !!studyPreferencesData,
    staleTime: 0, // Sempre buscar dados frescos
    gcTime: 1 * 60 * 1000, // 1 minuto
    refetchOnWindowFocus: true, // Refetch quando voltar para a aba
    refetchOnMount: true, // Sempre refetch ao montar
  });

  // Combinar loading states
  const isLoading = preferencesLoading || institutionsLoading;

  // Mutation para salvar preferências
  const savePreferences = useMutation({
    mutationFn: async (data: StudyPreferencesFormData) => {


      if (!user?.id) throw new Error('Usuário não autenticado');

      // Validar se todos os campos foram respondidos
      const hasSpecialty = data.specialty_unknown || (data.target_specialty && data.target_specialty.length > 0);
      const hasInstitutions = data.institutions_unknown || data.target_institutions.length > 0;
      const hasStudyTime = data.study_months && data.study_months > 0;

      if (!hasSpecialty || !hasInstitutions || !hasStudyTime) {
        throw new Error('Todos os campos devem ser preenchidos');
      }

      // Atualizar preferências usando upsert para garantir consistência
      const prefsUpdate = {
        user_id: user.id,
        target_specialty: data.specialty_unknown ? null : data.target_specialty,
        study_months: data.study_months,
        preferences_completed: true,
        target_institutions_unknown: data.institutions_unknown,
        updated_at: new Date().toISOString()
      };

      // ✅ LIMPEZA: Log removido - salvando user_preferences

      // Usar upsert para garantir que funciona sempre
      const { data: prefsResult, error: prefsError } = await supabase
        .from('user_preferences')
        .upsert(prefsUpdate, {
          onConflict: 'user_id'
        })
        .select();

      if (prefsError) {
        console.error('❌ [PREFERÊNCIAS] Erro ao salvar user_preferences:', prefsError);
        throw prefsError;
      }

      // ✅ LIMPEZA: Log removido - user_preferences salvo

      // Limpar instituições existentes
      // ✅ LIMPEZA: Log removido - limpando instituições

      const { data: deletedInstitutions, error: deleteError } = await supabase
        .from('user_target_institutions')
        .delete()
        .eq('user_id', user.id)
        .select();

      if (deleteError) {
        console.error('❌ [PREFERÊNCIAS] Erro ao limpar instituições:', deleteError);
        throw deleteError;
      }

      // ✅ LIMPEZA: Log removido - instituições limpas

      // Adicionar novas instituições (se não for "não sei")
      if (!data.institutions_unknown && data.target_institutions.length > 0) {
        const institutionsToInsert = data.target_institutions.map(institutionId => ({
          user_id: user.id,
          institution_id: institutionId
        }));

        // ✅ LIMPEZA: Log removido - inserindo instituições

        const { data: insertedInstitutions, error: insertError } = await supabase
          .from('user_target_institutions')
          .insert(institutionsToInsert)
          .select();

        if (insertError) {
          console.error('❌ [PREFERÊNCIAS] Erro ao inserir instituições:', insertError);
          throw insertError;
        }

        // ✅ LIMPEZA: Log removido - instituições inseridas
      } else {
        // ✅ LIMPEZA: Log removido - não inserindo instituições
      }

      // ✅ LIMPEZA: Log removido - salvamento completo
      return data;
    },
    onSuccess: async (data) => {
      // ✅ LIMPEZA: Logs removidos - onSuccess callback

      // ✅ CORREÇÃO: NÃO remover queries que contêm instituições
      console.log('🔍 [useStudyPreferences] onSuccess - preservando instituições');

      // Apenas invalidar queries básicas, sem remover dados das instituições
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['user-preferences'] }),
        queryClient.invalidateQueries({ queryKey: ['user-preferences-completed'] }),
        queryClient.invalidateQueries({ queryKey: ['user-profile'] }),
        queryClient.invalidateQueries({ queryKey: ['medical-specialties'] }),
      ]);

      // ❌ REMOVIDO: Não invalidar study-preferences-complete e consolidated-user-preferences
      // Isso estava causando perda das instituições

      // Forçar refetch imediato
      await queryClient.refetchQueries({
        queryKey: ['study-preferences-complete', user?.id],
        type: 'active'
      });

      toast({
        title: "Preferências salvas!",
        description: "Suas preferências de estudo foram salvas com sucesso.",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Erro ao salvar",
        description: "Não foi possível salvar suas preferências. Tente novamente.",
        variant: "destructive"
      });
    }
  });

  // ✅ CORREÇÃO: Query para instituições sempre habilitada
  const {
    data: availableInstitutions,
    isLoading: isLoadingInstitutions,
    error: institutionsError
  } = useQuery({
    queryKey: ['available-institutions'],
    queryFn: async () => {
      // Usar função SQL para buscar apenas instituições com questões
      const { data, error } = await supabase.rpc('get_institutions_with_questions');

      if (error) {
        throw error;
      }

      return data || [];
    },
    staleTime: 60 * 60 * 1000, // 1 hora (dados estáticos)
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
    retry: 3, // Tentar 3 vezes em caso de erro
  });

  return {
    preferences,
    isLoading,
    error,
    availableInstitutions,
    isLoadingInstitutions,
    institutionsError,
    savePreferences: savePreferences.mutateAsync,
    isSaving: savePreferences.isPending,
    hasCompletedPreferences: preferences?.preferences_completed || false
  };
};
