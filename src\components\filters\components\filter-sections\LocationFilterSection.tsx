
import { useMemo } from "react";
import { FilterItem } from "../../FilterItem";
import type { SelectedFilters } from "@/types/question";

interface LocationFilterSectionProps {
  locations: any[];
  selectedLocations: string[];
  onToggleLocation: (id: string) => void;
  selectedFilters: SelectedFilters;
  searchTerm: string;
}

export const LocationFilterSection = ({
  locations = [],
  selectedLocations = [],
  onToggleLocation,
  selectedFilters,
  searchTerm = ""
}: LocationFilterSectionProps) => {
  // Verificar se há filtros de categoria selecionados
  const hasCategoryFilters = (
    (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
    (selectedFilters.themes && selectedFilters.themes.length > 0) ||
    (selectedFilters.focuses && selectedFilters.focuses.length > 0)
  );

  // ✅ OTIMIZADO: Usar contagens dos metadados
  const getLocationCount = (location: any) => {
    if (!hasCategoryFilters) {
      // Se não há filtros de categoria, usar contagem total
      return location.count || 0;
    }

    // Se há filtros de categoria, calcular contagem baseada nos contadores detalhados
    let count = 0;

    // Verificar contagens por specialty
    if (selectedFilters.specialties?.length > 0) {
      selectedFilters.specialties.forEach(specialtyId => {
        count += location.specialty_counts?.[specialtyId] || 0;
      });
    }

    // Verificar contagens por theme
    if (selectedFilters.themes?.length > 0) {
      selectedFilters.themes.forEach(themeId => {
        count += location.theme_counts?.[themeId] || 0;
      });
    }

    // Verificar contagens por focus
    if (selectedFilters.focuses?.length > 0) {
      selectedFilters.focuses.forEach(focusId => {
        count += location.focus_counts?.[focusId] || 0;
      });
    }

    return count;
  };

  // Filter locations that have a count > 0
  const validLocations = useMemo(() => {
    return locations.filter(location => {
      const count = getLocationCount(location);
      return count > 0;
    });
  }, [locations, selectedFilters, hasCategoryFilters]);

  // Filter by search term
  const filteredLocations = useMemo(() => {
    return validLocations.filter(
      location => location.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [validLocations, searchTerm]);

  // Sort locations by count (highest first)
  const sortedLocations = useMemo(() => {
    return [...filteredLocations].sort((a, b) => {
      const countA = getLocationCount(a);
      const countB = getLocationCount(b);
      return countB - countA;
    });
  }, [filteredLocations, selectedFilters, hasCategoryFilters]);

  return (
    <div className="space-y-2">
      {sortedLocations.map(location => {
        const count = getLocationCount(location);
        return (
          <FilterItem
            key={location.id}
            item={{ ...location, type: "location" }}
            level={0}
            isExpanded={false}
            isSelected={selectedLocations.includes(location.id)}
            questionCount={{
              total: count,
              filtered: count
            }}
            hasChildren={false}
            onToggleExpand={() => {}}
            onToggleSelect={onToggleLocation}
            className={hasCategoryFilters ? "ring-2 ring-blue-200" : undefined} // Indicador visual
          />
        );
      })}
      {sortedLocations.length === 0 && (
        <div className="text-center text-gray-500 py-4">
          {hasCategoryFilters
            ? "Nenhuma instituição encontrada para as especialidades/temas/focos selecionados"
            : "Nenhuma instituição encontrada"
          }
        </div>
      )}
    </div>
  );
};
