import { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@supabase/auth-helpers-react';
import { useInstitutionPrevalence, PrevalenceFilters } from './useInstitutionPrevalence';
import { getWeekDates } from './study-schedule/useScheduleDates';
import type { StudyTopic, AIScheduleOptions } from '@/types/study-schedule';
import { useQueryClient } from '@tanstack/react-query';

export interface InstitutionScheduleOptions extends Omit<AIScheduleOptions, 'domain'> {
  institutionIds: string[];
  startYear?: number;
  endYear?: number;
  domain?: string;
  generationMode: 'random' | 'institution_based';
}

export const useInstitutionBasedSchedule = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const user = useUser();
  const queryClient = useQueryClient();
  const { calculatePrevalence } = useInstitutionPrevalence();

  const generateScheduleByInstitution = async (
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {
    // ✅ LIMPEZA: Remover logs desnecessários

    if (!user) {
      throw new Error('Usuário não autenticado');
    }

    setIsLoading(true);
    setError(null);

    try {
      onProgress?.(10);

      // Se modo aleatório, usar geração padrão
      if (options.generationMode === 'random') {
        // ✅ LIMPEZA: Remover log desnecessário
        return await generateRandomSchedule(options, onProgress);
      }

      // ✅ LIMPEZA: Remover log desnecessário

      // Calcular prevalência para as instituições selecionadas
      const prevalenceFilters: PrevalenceFilters = {
        institutionIds: options.institutionIds,
        startYear: options.startYear,
        endYear: options.endYear,
        domain: options.domain
      };

      // Validar se há instituições selecionadas
      if (!prevalenceFilters.institutionIds || prevalenceFilters.institutionIds.length === 0) {
        throw new Error('Nenhuma instituição foi selecionada. Por favor, selecione pelo menos uma instituição para gerar o cronograma.');
      }

      onProgress?.(30);

      const institutionStats = await calculatePrevalence(prevalenceFilters);

      if (institutionStats.length === 0) {
        throw new Error('Nenhuma estatística encontrada para as instituições selecionadas. Verifique se as instituições possuem questões no período e domínio especificados.');
      }

      onProgress?.(50);

      // Combinar estatísticas de todas as instituições
      const combinedStats = combineInstitutionStats(institutionStats);

      // Gerar cronograma baseado na prevalência
      const schedule = await generatePrevalenceBasedSchedule(
        combinedStats,
        institutionStats,
        options,
        onProgress
      );



      onProgress?.(100);
      return schedule;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro desconhecido';
      setError(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const generateRandomSchedule = async (
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {
    // Para modo aleatório, usar o sistema padrão existente
    // Retornar array vazio e deixar o sistema padrão lidar com isso
    onProgress?.(100);
    return [];
  };

  const combineInstitutionStats = (institutionStats: any[]) => {


    try {
      const combinedSpecialties = new Map<string, { specialty_id: string; name: string; count: number; percentage: number }>();
      const combinedThemes = new Map<string, { theme_id: string; name: string; count: number; percentage: number }>();
      const combinedFocuses = new Map<string, { focus_id: string; focus_name: string; specialty_id: string; theme_id: string; count: number; percentage: number }>();

      let totalQuestions = 0;

    // Somar estatísticas de todas as instituições
    institutionStats.forEach((institution, index) => {


      totalQuestions += institution.total_questions;

      // Processar especialidades
      if (institution.specialties && Array.isArray(institution.specialties)) {
        institution.specialties.forEach((specialty: any) => {
          const key = specialty.specialty_id;
          const current = combinedSpecialties.get(key) || {
            specialty_id: specialty.specialty_id,
            name: specialty.specialty_name,
            count: 0,
            percentage: 0
          };
          combinedSpecialties.set(key, {
            ...current,
            count: current.count + specialty.question_count
          });
        });
      }

      // Processar temas
      if (institution.themes && Array.isArray(institution.themes)) {
        institution.themes.forEach((theme: any) => {
          const key = theme.theme_id;
          const current = combinedThemes.get(key) || {
            theme_id: theme.theme_id,
            name: theme.theme_name,
            count: 0,
            percentage: 0
          };
          combinedThemes.set(key, {
            ...current,
            count: current.count + theme.question_count
          });
        });
      }

      // Processar focos
      if (institution.focuses && Array.isArray(institution.focuses)) {


        institution.focuses.forEach((focus: any) => {
          const key = focus.focus_id;
          const current = combinedFocuses.get(key) || {
            focus_id: focus.focus_id,
            focus_name: focus.focus_name || 'Foco sem nome',
            specialty_id: focus.specialty_id || '',
            theme_id: focus.theme_id || '',
            count: 0,
            percentage: 0
          };
          combinedFocuses.set(key, {
            ...current,
            count: current.count + focus.question_count
          });
        });
      }
    });

    // Recalcular percentuais
    combinedSpecialties.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    combinedThemes.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    combinedFocuses.forEach((value, key) => {
      value.percentage = (value.count / totalQuestions) * 100;
    });

    // ✅ ORDENAÇÃO POR PREVALÊNCIA: Ordenar por percentual (maior para menor)
    const sortedSpecialties = Array.from(combinedSpecialties.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);

    const sortedThemes = Array.from(combinedThemes.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);

    const sortedFocuses = Array.from(combinedFocuses.entries())
      .map(([id, data]) => ({ id, ...data }))
      .sort((a, b) => b.percentage - a.percentage);



      return {
        specialties: sortedSpecialties,
        themes: sortedThemes,
        focuses: sortedFocuses,
        totalQuestions
      };
    } catch (error) {
      throw error;
    }
  };

  const generatePrevalenceBasedSchedule = async (
    combinedStats: any,
    institutionStats: any[],
    options: InstitutionScheduleOptions,
    onProgress?: (progress: number) => void
  ): Promise<StudyTopic[]> => {


    const schedule: StudyTopic[] = [];
    const daysOfWeek = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

    // Calcular total de slots disponíveis
    const enabledDays = Object.entries(options.availableDays)
      .filter(([_, config]) => config.enabled);

    // ✅ CORREÇÃO: Expandir períodos baseado na duração do tópico
    const topicDurationMinutes = parseInt(options.topicDuration);
    const expandedEnabledDays = enabledDays.map(([day, config]) => {
      const expandedPeriods = [];

      for (const period of config.periods) {
        const startTime = period.startTime;
        const endTime = period.endTime;

        // Converter para minutos
        const startMinutes = startTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
        const endMinutes = endTime.split(':').map(Number).reduce((h, m) => h * 60 + m);
        const totalMinutes = endMinutes - startMinutes;

        // Calcular quantos tópicos cabem neste período
        const topicsInPeriod = Math.floor(totalMinutes / topicDurationMinutes);

        // Período expandido: ${day} ${startTime}-${endTime} = ${topicsInPeriod} slots

        // Criar slots individuais para cada tópico
        for (let i = 0; i < topicsInPeriod; i++) {
          const slotStartMinutes = startMinutes + (i * topicDurationMinutes);
          const slotStartHours = Math.floor(slotStartMinutes / 60);
          const slotStartMins = slotStartMinutes % 60;
          const slotStartTime = `${slotStartHours.toString().padStart(2, '0')}:${slotStartMins.toString().padStart(2, '0')}`;

          expandedPeriods.push({
            startTime: slotStartTime,
            endTime: period.endTime // Manter o endTime original para referência
          });
        }
      }

      return [day, { ...config, periods: expandedPeriods }];
    });

    const totalSlots = expandedEnabledDays.reduce((total, [_, config]) => {
      return total + config.periods.length;
    }, 0) * options.weeksCount;



    onProgress?.(70);

    // Distribuir tópicos baseado na prevalência
    let currentSlot = 0;

    // ✅ CORREÇÃO: Calcular o próximo week_number baseado nas semanas existentes
    const { data: existingWeeks } = await supabase
      .from('study_schedules')
      .select('week_number')
      .eq('user_id', user.id)
      .order('week_number', { ascending: false })
      .limit(1);

    const nextWeekNumber = existingWeeks && existingWeeks.length > 0 ?
      existingWeeks[0].week_number + 1 : 1;



    // ✅ NOVO: Calcular disponibilidade de focos vs slots necessários (será atualizado após filtro)
    let availableFocusesCount = combinedStats.focuses.length;
    const slotsNeeded = totalSlots;
    let willRepeatFocuses = availableFocusesCount < slotsNeeded;

    // ✅ BUSCAR FOCOS JÁ UTILIZADOS para evitar repetição
    // Buscar todas as semanas do usuário primeiro
    const { data: userSchedules, error: schedulesError } = await supabase
      .from('study_schedules')
      .select('id')
      .eq('user_id', user.id);

    if (schedulesError) {
      // Erro silencioso
    }

    const scheduleIds = userSchedules?.map(s => s.id) || [];


    // Buscar focos já utilizados em todas as semanas
    let existingTopics = [];
    let existingError = null;

    if (scheduleIds.length > 0) {

      const result = await supabase
        .from('study_schedule_items')
        .select('focus_id')
        .in('schedule_id', scheduleIds)
        .not('focus_id', 'is', null);

      existingTopics = result.data || [];
      existingError = result.error;
    } else {

    }

    if (existingError) {
      // Erro silencioso
    }

    const usedFocusIds = new Set(existingTopics?.map(item => item.focus_id) || []);


    // ✅ FILTRAR FOCOS: Remover focos já utilizados da lista
    const availableFocuses = combinedStats.focuses.filter(focus => !usedFocusIds.has(focus.focus_id));
    availableFocusesCount = availableFocuses.length;
    willRepeatFocuses = availableFocusesCount < slotsNeeded;

    // ✅ ALGORITMO BASEADO EM FOCOS: Seguir ordem de prevalência dos focos não utilizados

    const validTopics = [];

    // Iterar pelos focos disponíveis (não utilizados) ordenados por prevalência
    for (const focus of availableFocuses) {
      if (validTopics.length >= totalSlots) break;

      // ✅ USAR HIERARQUIA REAL: Buscar especialidade e tema reais deste foco
      const realSpecialtyId = focus.specialty_id;
      const realThemeId = focus.theme_id;

      // Buscar objetos completos da especialidade e tema
      const realSpecialty = combinedStats.specialties.find(s => s.specialty_id === realSpecialtyId);
      const realTheme = combinedStats.themes.find(t => t.theme_id === realThemeId);

      if (!realSpecialty || !realTheme) {
        continue;
      }



      validTopics.push({
        specialty: realSpecialty,
        theme: realTheme,
        focus
      });
    }

    // ✅ NOVO: Calcular slots válidos considerando dias passados na primeira semana
    const today = new Date();
    const currentDayOfWeek = today.getDay(); // 0 = domingo, 1 = segunda, etc.

    // Mapear dias da semana para números
    const dayNameToNumber = {
      'domingo': 0,
      'segunda-feira': 1,
      'terça-feira': 2,
      'quarta-feira': 3,
      'quinta-feira': 4,
      'sexta-feira': 5,
      'sábado': 6
    };

    // Calcular quantos slots são válidos na primeira semana
    let validSlotsInFirstWeek = 0;
    for (const [dayName] of expandedEnabledDays) {
      const dayNumber = dayNameToNumber[dayName as keyof typeof dayNameToNumber];
      // ✅ CORREÇÃO: Incluir o dia atual (>=) para permitir estudos no mesmo dia
      if (dayNumber >= currentDayOfWeek) {
        validSlotsInFirstWeek++;
      }
    }



    // ✅ CORREÇÃO: Calcular distribuição considerando slots perdidos na primeira semana
    const slotsLostInFirstWeek = expandedEnabledDays.length - validSlotsInFirstWeek;
    const totalValidSlots = totalSlots - slotsLostInFirstWeek;

    // ✅ REDISTRIBUIR: Manter o total de slots, redistribuindo os perdidos
    const slotsPerWeek = Math.floor(totalSlots / options.weeksCount);
    const extraSlots = totalSlots % options.weeksCount;

    // ✅ REUTILIZAR: Usar variáveis já declaradas acima
    // Calcular quantos slots pular na primeira semana (dias que já passaram)
    let slotsToSkipInFirstWeek = 0;
    for (const [dayName] of expandedEnabledDays) {
      const dayNumber = dayNameToNumber[dayName as keyof typeof dayNameToNumber];
      if (dayNumber < currentDayOfWeek) {
        slotsToSkipInFirstWeek++;
      } else {
        break; // Parar no primeiro dia futuro
      }
    }



    // ✅ NOVA LÓGICA: Distribuição inteligente considerando slots perdidos
    let currentSlotInWeek = 0;
    let currentWeekIndex = 0;
    let slotsUsedInCurrentWeek = 0;

    // Criar tópicos baseados na ordem de prevalência dos focos
    for (let i = 0; i < validTopics.length && i < totalSlots; i++) {
      const { specialty, theme, focus } = validTopics[i];

      // ✅ LÓGICA INTELIGENTE: Pular slots perdidos apenas na primeira semana
      if (currentWeekIndex === 0 && currentSlotInWeek < slotsToSkipInFirstWeek) {
        // Pular para o próximo slot válido na primeira semana
        currentSlotInWeek = slotsToSkipInFirstWeek;
      }

      // Verificar se precisamos mudar de semana
      if (slotsUsedInCurrentWeek >= slotsPerWeek) {
        currentWeekIndex++;
        currentSlotInWeek = 0;
        slotsUsedInCurrentWeek = 0;

        // Se chegamos ao limite de semanas, parar
        if (currentWeekIndex >= options.weeksCount) {
          break;
        }
      }

      const dayIndex = currentSlotInWeek % expandedEnabledDays.length;
      const [dayName, dayConfig] = expandedEnabledDays[dayIndex];
      const periodIndex = Math.floor(currentSlotInWeek / expandedEnabledDays.length) % dayConfig.periods.length;
      const period = dayConfig.periods[periodIndex];

      const topicWeekNumber = nextWeekNumber + currentWeekIndex;



      // ✅ NOVO: Calcular quais instituições contemplam este foco
      const focusInstitutions = institutionStats.filter(institution =>
        institution.focuses.some((f: any) => f.focus_id === focus.focus_id)
      ).map(institution => ({
        id: institution.institution_id,
        name: institution.institution_name,
        relevance: institution.focuses.find((f: any) => f.focus_id === focus.focus_id)?.question_count || 0,
        percentage: ((institution.focuses.find((f: any) => f.focus_id === focus.focus_id)?.question_count || 0) / institution.total_questions * 100).toFixed(1)
      })).sort((a, b) => b.relevance - a.relevance);

      schedule.push({
        id: crypto.randomUUID(),
        specialty: specialty.name,
        theme: theme.name,
        focus: focus.focus_name,
        specialtyId: specialty.specialty_id,
        themeId: theme.theme_id,
        focusId: focus.focus_id,
        difficulty: 'Médio',
        activity: `Estudo de ${specialty.name} - ${theme.name}`,
        startTime: period.startTime,
        duration: `${options.topicDuration} minutos`,
        day: dayName,
        weekNumber: topicWeekNumber, // ✅ CORREÇÃO: Usar weekNumber calculado
        is_manual: false,
        // ✅ NOVO: Informações das instituições
        institutions: focusInstitutions,
        focusPrevalence: focus.percentage.toFixed(2)
      });

      // ✅ INCREMENTAR: Atualizar contadores
      currentSlotInWeek++;
      slotsUsedInCurrentWeek++;
    }

    // Se ainda há slots disponíveis, usar focos restantes em ordem de prevalência
    if (schedule.length < totalSlots && validTopics.length < totalSlots) {
      // Focos insuficientes
    }

    // Calcular estatísticas de variedade
    const uniqueSpecialties = new Set(schedule.map(topic => topic.specialty));
    const uniqueThemes = new Set(schedule.map(topic => topic.theme));
    const uniqueFocuses = new Set(schedule.map(topic => topic.focus));

    // ✅ NOVO: Verificar distribuição por semana
    const weekDistribution = new Map();
    schedule.forEach(topic => {
      const count = weekDistribution.get(topic.weekNumber) || 0;
      weekDistribution.set(topic.weekNumber, count + 1);
    });






    onProgress?.(90);

    // ✅ ADICIONAR: Criar semanas e inserir tópicos no banco


    if (!user) {
      throw new Error('Usuário não autenticado');
    }

    // Criar semanas se necessário
    if (options.scheduleOption === "new") {

      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (!authUser) throw new Error('User not authenticated');

      // Verificar qual é o próximo número de semana disponível
      const { data: existingWeeks, error: checkError } = await supabase
        .from('study_schedules')
        .select('week_number, week_end_date')
        .eq('user_id', authUser.id)
        .order('week_number', { ascending: false })
        .limit(1);

      if (checkError) {
        throw new Error(`Erro ao verificar semanas: ${checkError.message}`);
      }

      const nextWeekNumber = existingWeeks && existingWeeks.length > 0 ?
        existingWeeks[0].week_number + 1 : 1;

      const weeksToCreate = [];

      // ✅ CORREÇÃO: Usar a mesma lógica da criação manual
      // Começar sempre no domingo da semana atual ou próxima semana livre
      let baseDate = new Date();

      // Se já existem semanas, começar após a última
      if (existingWeeks && existingWeeks.length > 0) {
        const lastWeek = existingWeeks[0]; // existingWeeks já está ordenado desc
        const lastEndDate = new Date(lastWeek.week_end_date + 'T12:00:00Z');
        baseDate = new Date(lastEndDate);
        baseDate.setUTCDate(lastEndDate.getUTCDate() + 1); // Próximo domingo
      }

      for (let i = 0; i < options.weeksCount; i++) {
        // Calcular a data base para esta semana específica
        const weekBaseDate = new Date(baseDate);
        if (i > 0) {
          weekBaseDate.setDate(baseDate.getDate() + (i * 7));
        }

        // ✅ CORREÇÃO: Usar getWeekDates para garantir domingo-sábado
        const { startDate: weekStart, endDate: weekEnd } = getWeekDates(weekBaseDate, false);

        const weekToCreate = {
          user_id: authUser.id,
          week_number: nextWeekNumber + i,
          week_start_date: weekStart.toISOString().split('T')[0],
          week_end_date: weekEnd.toISOString().split('T')[0],
          status: 'active'
        };



        weeksToCreate.push(weekToCreate);
      }



      const { data: createdWeeks, error: weeksError } = await supabase
        .from('study_schedules')
        .insert(weeksToCreate)
        .select('*');

      if (weeksError) {
        throw new Error(`Erro ao criar semanas: ${weeksError.message}`);
      }


    }

    // Buscar semanas alvo
    const { data: targetSchedules, error: targetSchedulesError } = await supabase
      .from('study_schedules')
      .select('*')
      .eq('user_id', user.id)
      .order('week_number', { ascending: true });

    if (targetSchedulesError) {
      throw new Error(`Erro ao buscar semanas: ${targetSchedulesError.message}`);
    }

    if (!targetSchedules || targetSchedules.length === 0) {
      throw new Error('Nenhuma semana encontrada para inserir os tópicos');
    }



    // ✅ SOLUÇÃO DEFINITIVA: Verificar se um dia já passou na primeira semana
    const isDayInPast = (dayOfWeek: string, weekNumber: number) => {
      if (weekNumber !== 1) return false; // Só verificar na primeira semana

      const today = new Date();
      const currentDayOfWeek = today.getDay(); // 0 = domingo, 1 = segunda, etc.

      // Mapear dias da semana para números
      const dayMap: { [key: string]: number } = {
        'domingo': 0, 'segunda-feira': 1, 'terça-feira': 2, 'quarta-feira': 3,
        'quinta-feira': 4, 'sexta-feira': 5, 'sábado': 6
      };

      const dayNumber = dayMap[dayOfWeek.toLowerCase()];
      if (dayNumber === undefined) return false;

      // ✅ LÓGICA SIMPLES: Se o dia da semana é menor que o dia atual, já passou
      const isPast = dayNumber < currentDayOfWeek;

      // ✅ LIMPEZA: Log removido - operação rotineira

      return isPast;
    };

    // ✅ FILTRAR: Remover tópicos de dias passados na primeira semana
    const filteredSchedule = schedule.filter(topic => {
      const dayHasPassed = isDayInPast(topic.day, topic.weekNumber);

      if (dayHasPassed) {
        // ✅ LIMPEZA: Log removido - operação rotineira
        return false;
      }

      return true;
    });



    // Inserir tópicos no banco (usando schedule filtrado)
    const itemsToInsert = filteredSchedule.map(topic => {
      // ✅ CORREÇÃO: Usar a última semana criada (maior week_number) para novos tópicos
      const targetSchedule = targetSchedules.find(s => s.week_number === topic.weekNumber) ||
                             targetSchedules.sort((a, b) => b.week_number - a.week_number)[0];



      return {
        schedule_id: targetSchedule.id,
        day_of_week: topic.day.toLowerCase(),
        topic: `${topic.specialty} - ${topic.theme}`,
        specialty_name: topic.specialty,
        specialty_id: topic.specialtyId,
        theme_name: topic.theme,
        theme_id: topic.themeId,
        focus_name: topic.focus,
        focus_id: topic.focusId,
        difficulty: topic.difficulty,
        activity_description: topic.activity,
        start_time: topic.startTime,
        duration: topic.duration,
        type: 'study',
        activity_type: 'study',
        week_number: topic.weekNumber,
        study_status: 'pending',
        // ✅ NOVO: Salvar dados das instituições no metadata
        metadata: {
          institutions: topic.institutions || [],
          focusPrevalence: topic.focusPrevalence,
          isPersonalized: true,
          generationMode: 'institution_based'
        }
      };
    });



    const { data: insertedItems, error: insertError } = await supabase
      .from('study_schedule_items')
      .insert(itemsToInsert)
      .select('*');

    if (insertError) {
      throw new Error(`Erro ao inserir tópicos: ${insertError.message}`);
    }

    // ✅ NOVO: Forçar invalidação completa do cache após inserção

    // Invalidar TODOS os caches relacionados
    await queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey[0];
        return key === 'consolidated-schedule-data' ||
               key === 'processed-schedule-data' ||
               key === 'schedule' ||
               key === 'study_schedules' ||
               key === 'study_schedule_items';
      },
      refetchType: 'all'
    });

    // Aguardar um pouco e forçar refetch
    setTimeout(async () => {
      await queryClient.refetchQueries({
        queryKey: ['consolidated-schedule-data', user.id]
      });
  
    }, 100);

    // ✅ NOVO: Adicionar informações de repetição ao schedule para uso posterior
    schedule.forEach(topic => {
      (topic as any).focusRepetitionWarning = willRepeatFocuses;
      (topic as any).availableFocusesCount = availableFocusesCount;
      (topic as any).totalSlotsCount = slotsNeeded;
    });

    return schedule;
  };

  return {
    generateScheduleByInstitution,
    isLoading,
    error
  };
};
