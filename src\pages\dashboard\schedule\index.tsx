
import React, { useEffect, useState } from "react";
import { useOptimizedSchedule } from "@/hooks/study-schedule/useOptimizedSchedule";
import { StudySchedule } from "@/components/progress/StudySchedule";
import { TopicSourceDialog } from "@/components/study/schedule/TopicSourceDialog";
import { PageHeader } from "@/components/ui/page-header";
import { FixScheduleDatesButton } from "@/components/progress/FixScheduleDatesButton";

export default function SchedulePage() {
  console.log('🏁🏁🏁 [SchedulePage] COMPONENTE INICIADO - TESTE CACHE');
  console.log('🏁 [SchedulePage] Componente iniciado, chamando useOptimizedSchedule...');

  const {
    weeklySchedule,
    isLoading,
    loadInitialSchedule,
    handleUpdateTopic,
    handleMarkStudied,
    handleDeleteWeek,
    handleDeleteAllWeeks,
    handleDeleteTopic
  } = useOptimizedSchedule();

  console.log('📊 [SchedulePage] Resultado do useOptimizedSchedule:', {
    weeklySchedule: !!weeklySchedule,
    isLoading,
    handleMarkStudied: typeof handleMarkStudied
  });
  
  const [topicSourceDialogOpen, setTopicSourceDialogOpen] = useState(false);
  const [selectedDayInfo, setSelectedDayInfo] = useState<{
    day: string,
    weekNumber: number
  } | null>(null);

  useEffect(() => {
    loadInitialSchedule();
  }, [loadInitialSchedule]);

  // ✅ DEBUG: Verificar se as funções estão definidas
  useEffect(() => {
    console.log('🔧 [SchedulePage] Verificando funções do useOptimizedSchedule:', {
      handleMarkStudied: typeof handleMarkStudied,
      handleUpdateTopic: typeof handleUpdateTopic,
      handleDeleteTopic: typeof handleDeleteTopic
    });
  }, [handleMarkStudied, handleUpdateTopic, handleDeleteTopic]);

  const handleAddTopic = (day: string, weekNumber: number, source: 'platform' | 'manual') => {
    if (source === 'platform') {
      console.log("Opening topic selector for platform content");
      // Navigate to question selection or handle platform-specific topic addition
    } else {
      console.log("Opening manual topic form");
      // Handle manual topic creation
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <PageHeader
          title="Cronograma de Estudos"
          description="Organize seus estudos e acompanhe seu progresso"
          icon="calendar"
        />
        
        <FixScheduleDatesButton />
      </div>
      
      {isLoading ? (
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#58CC02]"></div>
        </div>
      ) : weeklySchedule && weeklySchedule.recommendations.length > 0 ? (
        <StudySchedule
          weeklyPlan={weeklySchedule.recommendations}
          onUpdateTopic={handleUpdateTopic}
          onMarkStudied={(topicId) => {
            console.log('🎯 [SchedulePage] onMarkStudied wrapper chamado com topicId:', topicId);
            console.log('🎯 [SchedulePage] handleMarkStudied type:', typeof handleMarkStudied);
            const result = handleMarkStudied(topicId);
            console.log('🎯 [SchedulePage] handleMarkStudied result:', result);
            return result;
          }}
          onDeleteWeek={handleDeleteWeek}
          onDeleteAllWeeks={handleDeleteAllWeeks}
          onDeleteTopic={handleDeleteTopic}
        />
      ) : (
        <div className="text-center p-12 bg-white rounded-xl shadow">
          <h3 className="text-xl font-semibold mb-4">Nenhum cronograma encontrado</h3>
          <p className="text-gray-600 mb-6">
            Você ainda não possui um cronograma de estudos criado. 
            Gere um novo cronograma para começar a organizar seus estudos.
          </p>
          <button
            className="px-4 py-2 bg-[#58CC02] text-white rounded-md font-semibold hover:bg-[#58CC02]/90 focus:outline-none focus:ring-2 focus:ring-[#58CC02]/50"
            onClick={() => { /* Navigate to schedule generation */ }}
          >
            Gerar Cronograma
          </button>
        </div>
      )}

      <TopicSourceDialog
        open={topicSourceDialogOpen}
        onOpenChange={setTopicSourceDialogOpen}
        onSelectSource={(source) => {
          if (selectedDayInfo) {
            handleAddTopic(selectedDayInfo.day, selectedDayInfo.weekNumber, source);
          }
          setTopicSourceDialogOpen(false);
        }}
      />
    </div>
  );
}
