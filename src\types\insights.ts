/**
 * Sistema de Insights de Focos Personalizados
 * Matriz Temperatura × Frequência para sugestões de estudo
 */

export type TemperatureCategory = 'frio' | 'morno' | 'quente' | 'vulcanico';

export interface TemperatureConfig {
  label: string;
  icon: string;
  emoji: string;
  bgColor: string;
  textColor: string;
  borderColor: string;
  description: string;
}

export interface FocusFrequencyData {
  focus_id: string;
  focus_name: string;
  specialty_name: string;
  theme_name: string;
  frequency: number; // Total de ocorrências nos últimos X anos
  last_occurrence_year: number; // Ano da última ocorrência
  years_since_last: number; // Anos desde a última ocorrência
  total_questions: number; // Total de questões disponíveis
}

export interface FocusInsight {
  focus_id: string;
  focus_name: string;
  specialty_name: string;
  theme_name: string;
  frequency: number;
  years_since_last: number;
  last_occurrence_year: number;
  total_questions: number;
  temperature: TemperatureCategory;
  config: TemperatureConfig;
  tooltip: string;
  detailedDescription: string;
}

export interface InsightFilters {
  years_range: number; // X anos para análise (padrão: 5)
  recency_threshold: number; // Limite de recência em anos (padrão: 3)
  frequency_threshold: number; // Limite de frequência (padrão: 2)
  exclude_focus_ids?: string[]; // Focos para excluir (estudos do dia)
  institution_ids: string[]; // Instituições do usuário
}

export interface InsightResponse {
  suggested_focus: FocusInsight | null;
  total_analyzed: number;
  categories_count: {
    frio: number;
    morno: number;
    quente: number;
    vulcanico: number;
  };
  excluded_from_today: number;
}

// Configurações das categorias de temperatura
export const TEMPERATURE_CONFIGS: Record<TemperatureCategory, TemperatureConfig> = {
  frio: {
    label: 'Frio',
    icon: '🧊',
    emoji: '🧊',
    bgColor: 'bg-blue-50',
    textColor: 'text-blue-700',
    borderColor: 'border-blue-200',
    description: 'Tema congelado no histórico. Revisão rápida e pontual.'
  },
  morno: {
    label: 'Morno',
    icon: '♨️',
    emoji: '♨️',
    bgColor: 'bg-yellow-50',
    textColor: 'text-yellow-700',
    borderColor: 'border-yellow-200',
    description: 'Tema raro, mas com retorno recente. Revisão pontual recomendada.'
  },
  quente: {
    label: 'Quente',
    icon: '🔥',
    emoji: '🔥',
    bgColor: 'bg-orange-50',
    textColor: 'text-orange-700',
    borderColor: 'border-orange-200',
    description: 'Histórico consistente, mas sem aparições recentes. Mantenha no radar.'
  },
  vulcanico: {
    label: 'Vulcânico',
    icon: '🌋',
    emoji: '🌋',
    bgColor: 'bg-red-50',
    textColor: 'text-red-700',
    borderColor: 'border-red-200',
    description: 'Tópico em erupção: prioridade máxima no cronograma!'
  }
};
