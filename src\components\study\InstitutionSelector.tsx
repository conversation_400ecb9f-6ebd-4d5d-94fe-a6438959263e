import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Search, X, Building2, Calendar, Filter } from 'lucide-react';
import { useInstitutionPrevalence } from '@/hooks/useInstitutionPrevalence';
import { useToast } from '@/hooks/use-toast';

export interface InstitutionSelectorProps {
  selectedInstitutions: string[];
  onInstitutionsChange: (institutionIds: string[]) => void;
  startYear?: number;
  endYear?: number;
  onYearRangeChange?: (startYear?: number, endYear?: number) => void;
  generationMode: 'random' | 'institution_based';
  onGenerationModeChange: (mode: 'random' | 'institution_based') => void;
}

export const InstitutionSelector: React.FC<InstitutionSelectorProps> = ({
  selectedInstitutions,
  onInstitutionsChange,
  startYear,
  endYear,
  onYearRangeChange,
  generationMode,
  onGenerationModeChange
}) => {
  const [institutions, setInstitutions] = useState<{ id: string; name: string }[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { getInstitutions } = useInstitutionPrevalence();
  const { toast } = useToast();

  useEffect(() => {
    loadInstitutions();
  }, []);

  const loadInstitutions = async () => {
    setIsLoading(true);
    try {
      const data = await getInstitutions();
      setInstitutions(data);
    } catch (error) {
      toast({
        title: "Erro",
        description: "Não foi possível carregar as instituições",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const filteredInstitutions = institutions.filter(institution =>
    institution.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleInstitutionToggle = (institutionId: string) => {
    const newSelection = selectedInstitutions.includes(institutionId)
      ? selectedInstitutions.filter(id => id !== institutionId)
      : [...selectedInstitutions, institutionId];
    
    onInstitutionsChange(newSelection);
  };

  const handleSelectAll = () => {
    if (selectedInstitutions.length === filteredInstitutions.length) {
      onInstitutionsChange([]);
    } else {
      onInstitutionsChange(filteredInstitutions.map(inst => inst.id));
    }
  };

  const removeInstitution = (institutionId: string) => {
    onInstitutionsChange(selectedInstitutions.filter(id => id !== institutionId));
  };

  const getSelectedInstitutionNames = () => {
    return institutions
      .filter(inst => selectedInstitutions.includes(inst.id))
      .map(inst => inst.name);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Filtro por Instituição
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Modo de Geração */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Modo de Geração</Label>
          <div className="flex gap-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="random"
                checked={generationMode === 'random'}
                onCheckedChange={() => onGenerationModeChange('random')}
              />
              <Label htmlFor="random" className="text-sm">
                Aleatório (padrão)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="institution"
                checked={generationMode === 'institution_based'}
                onCheckedChange={() => onGenerationModeChange('institution_based')}
              />
              <Label htmlFor="institution" className="text-sm">
                Baseado em Instituições
              </Label>
            </div>
          </div>
        </div>

        {/* Seleção de Instituições - só aparece se modo baseado em instituições */}
        {generationMode === 'institution_based' && (
          <>
            {/* Filtro de Anos */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startYear" className="text-sm font-medium">
                  Ano Inicial
                </Label>
                <Input
                  id="startYear"
                  type="number"
                  placeholder="Ex: 2020"
                  value={startYear || ''}
                  onChange={(e) => onYearRangeChange?.(
                    e.target.value ? parseInt(e.target.value) : undefined,
                    endYear
                  )}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endYear" className="text-sm font-medium">
                  Ano Final
                </Label>
                <Input
                  id="endYear"
                  type="number"
                  placeholder="Ex: 2024"
                  value={endYear || ''}
                  onChange={(e) => onYearRangeChange?.(
                    startYear,
                    e.target.value ? parseInt(e.target.value) : undefined
                  )}
                />
              </div>
            </div>

            {/* Busca de Instituições */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Selecionar Instituições</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar instituições..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Instituições Selecionadas */}
            {selectedInstitutions.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Instituições Selecionadas ({selectedInstitutions.length})
                </Label>
                <div className="flex flex-wrap gap-2">
                  {getSelectedInstitutionNames().map((name, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      <Building2 className="h-3 w-3" />
                      {name}
                      <X
                        className="h-3 w-3 cursor-pointer hover:text-red-500"
                        onClick={() => removeInstitution(selectedInstitutions[index])}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Lista de Instituições */}
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label className="text-sm font-medium">
                  Instituições Disponíveis ({filteredInstitutions.length})
                </Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  disabled={isLoading}
                >
                  {selectedInstitutions.length === filteredInstitutions.length 
                    ? 'Desmarcar Todas' 
                    : 'Selecionar Todas'
                  }
                </Button>
              </div>
              
              <div className="max-h-60 overflow-y-auto border rounded-md p-2 space-y-2">
                {isLoading ? (
                  <div className="text-center py-4 text-gray-500">
                    Carregando instituições...
                  </div>
                ) : filteredInstitutions.length === 0 ? (
                  <div className="text-center py-4 text-gray-500">
                    Nenhuma instituição encontrada
                  </div>
                ) : (
                  filteredInstitutions.map((institution) => (
                    <div
                      key={institution.id}
                      className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded"
                    >
                      <Checkbox
                        id={institution.id}
                        checked={selectedInstitutions.includes(institution.id)}
                        onCheckedChange={() => handleInstitutionToggle(institution.id)}
                      />
                      <Label
                        htmlFor={institution.id}
                        className="flex-1 text-sm cursor-pointer"
                      >
                        {institution.name}
                      </Label>
                    </div>
                  ))
                )}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};
