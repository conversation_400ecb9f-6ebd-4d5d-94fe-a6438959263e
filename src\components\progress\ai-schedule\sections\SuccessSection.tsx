import React from 'react';
import { Check<PERSON>ir<PERSON>, Clock, BookOpen, Target, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface GenerationStats {
  totalTopics: number;
  totalWeeks: number;
  specialties: string[];
  totalHours: number;
  generationTime: number;
  domain: string;
}

interface SuccessSectionProps {
  stats?: GenerationStats;
  onClose: () => void;
}

export const SuccessSection: React.FC<SuccessSectionProps> = ({
  stats,
  onClose
}) => {
  return (
    <div className="space-y-6 text-center">
      {/* Success Icon */}
      <div className="flex justify-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="w-8 h-8 text-green-600" />
        </div>
      </div>

      {/* Success Message */}
      <div>
        <h2 className="text-2xl font-bold text-green-600 mb-2">
          Cronograma Gerado com Sucesso!
        </h2>
        <p className="text-gray-600">
          Seu cronograma de estudos foi criado e está pronto para uso.
        </p>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-2 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <BookOpen className="w-6 h-6 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">{stats.totalTopics}</div>
              <div className="text-sm text-gray-600">Tópicos Criados</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Calendar className="w-6 h-6 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-purple-600">{stats.totalWeeks}</div>
              <div className="text-sm text-gray-600">Semanas Geradas</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Clock className="w-6 h-6 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-600">{stats.totalHours}h</div>
              <div className="text-sm text-gray-600">Horas de Estudo</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4 text-center">
              <Target className="w-6 h-6 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">{stats.specialties?.length || 0}</div>
              <div className="text-sm text-gray-600">Especialidades</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Specialties */}
      {stats?.specialties && stats.specialties.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold mb-3">Especialidades Incluídas</h3>
          <div className="flex flex-wrap gap-2 justify-center">
            {stats.specialties.map((specialty, index) => (
              <Badge key={index} variant="secondary">
                {specialty}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Generation Info */}
      {stats && (
        <div className="text-sm text-gray-500 space-y-1">
          <p>Domínio: <span className="font-medium">{stats.domain}</span></p>
          <p>Tempo de geração: <span className="font-medium">{stats.generationTime.toFixed(1)}s</span></p>
        </div>
      )}

      {/* Action Button */}
      <Button 
        onClick={onClose}
        className="w-full"
        size="lg"
      >
        Fechar e Ver Cronograma
      </Button>
    </div>
  );
};
