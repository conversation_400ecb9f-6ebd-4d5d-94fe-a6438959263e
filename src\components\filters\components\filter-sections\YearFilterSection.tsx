
import { useState } from "react";
import { FilterItem } from "../../FilterItem";
import { useSingletonYearCounts } from "@/hooks/useSingletonHierarchicalCounts";
import type { SelectedFilters } from "@/types/question";
import { cn } from "@/lib/utils";

interface YearFilterSectionProps {
  years: { year: number; count: number }[];
  selectedYears: string[];
  onToggleYear: (year: string) => void;
  questionCounts: {
    totalCounts: {[key: string]: number};
    filteredCounts: {[key: string]: number};
  };
  hasActiveFilters: boolean;
  selectedFilters: SelectedFilters;
  searchTerm: string;
}

export const YearFilterSection = ({
  years,
  selectedYears,
  onToggleYear,
  questionCounts,
  hasActiveFilters,
  selectedFilters,
  searchTerm
}: YearFilterSectionProps) => {
  // Verificar se há filtros de categoria ou localização selecionados
  const hasHierarchicalFilters = (
    (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
    (selectedFilters.themes && selectedFilters.themes.length > 0) ||
    (selectedFilters.focuses && selectedFilters.focuses.length > 0) ||
    (selectedFilters.locations && selectedFilters.locations.length > 0)
  );

  // ✅ SINGLETON: Usar cache singleton para eliminar 100% das duplicações
  const { data: hierarchicalCounts, isLoading: isLoadingHierarchical } = useSingletonYearCounts(selectedFilters);

  const getYearCount = (yearObj: any) => {
    const yearStr = yearObj.year.toString();

    if (hasHierarchicalFilters && hierarchicalCounts) {
      // Se há filtros hierárquicos, usar contagens filtradas
      return hierarchicalCounts[yearStr] || 0;
    }

    // Se não há filtros hierárquicos, usar contagem total do metadata
    return yearObj.count || 0;
  };

  // Filter years that have counts > 0 (usando contagem hierárquica se aplicável)
  const validYears = years.filter(yearObj => {
    return getYearCount(yearObj) > 0;
  });

  // Filter years based on search term
  const filteredYears = validYears.filter(yearObj => {
    const yearStr = yearObj.year.toString();
    return yearStr.includes(searchTerm);
  });

  // Sort years in descending order (most recent first)
  const sortedYears = [...filteredYears].sort((a, b) => b.year - a.year);

  // Mostrar indicador de carregamento se estiver carregando contagens hierárquicas
  if (hasHierarchicalFilters && isLoadingHierarchical) {
    return (
      <div className="space-y-2">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center justify-between p-3 rounded-lg bg-gray-100 animate-pulse">
            <div className="flex items-center gap-3">
              <div className="w-5 h-5 rounded bg-gray-300"></div>
              <div className="h-4 w-16 bg-gray-300 rounded"></div>
            </div>
            <div className="h-6 w-12 bg-gray-300 rounded-full"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <>
      {sortedYears.map(yearObj => {
        const yearStr = yearObj.year.toString();
        const countToUse = getYearCount(yearObj);
        const isSelected = selectedYears.includes(yearStr);

        // Create a compatible item object for FilterItem
        const yearItem = {
          id: yearStr,
          name: yearStr,
          type: "year"
        };

        return (
          <FilterItem
            key={yearStr}
            item={yearItem}
            level={0}
            isExpanded={false}
            isSelected={isSelected}
            questionCount={{
              total: countToUse,
              filtered: countToUse
            }}
            hasChildren={false}
            onToggleExpand={() => {}}
            onToggleSelect={(id) => onToggleYear(id)}
            className={hasHierarchicalFilters ? "ring-2 ring-green-200" : undefined} // Indicador visual
          />
        );
      })}
      {sortedYears.length === 0 && !isLoadingHierarchical && (
        <div className="text-center text-gray-500 py-4">
          {hasHierarchicalFilters
            ? "Nenhum ano encontrado para os filtros selecionados"
            : "Nenhum ano encontrado"
          }
        </div>
      )}
    </>
  );
};
