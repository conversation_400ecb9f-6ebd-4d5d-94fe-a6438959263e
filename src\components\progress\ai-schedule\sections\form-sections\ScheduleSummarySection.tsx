
import React from "react";
import { Info } from "lucide-react";
import { calculateTotalHours } from "../../utils/calculations";
import type { DayConfig, AIScheduleFormData } from "../../types";
import { UseFormReturn } from "react-hook-form";

interface ScheduleSummaryProps {
  form: UseFormReturn<AIScheduleFormData>;
}

export const ScheduleSummarySection = ({ form }: ScheduleSummaryProps) => {
  const availableDays = form.watch('availableDays');
  const scheduleOption = form.watch('scheduleOption');
  const weeksCount = form.watch('weeksCount');
  const topicDuration = form.watch('topicDuration');
  const domain = form.watch('domain');
  const totalHours = calculateTotalHours(availableDays);
  
  const topicsPerWeek = Math.round(totalHours * 60 / parseInt(topicDuration));
  const totalWeeks = scheduleOption === "new" ? weeksCount : 1;

  // ✅ NOVA FUNÇÃO: Formatar horas sem decimais desnecessários
  const formatHours = (hours: number): string => {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);

    if (minutes === 0) {
      return `${wholeHours}h`;
    } else {
      return `${wholeHours}h ${minutes}min`;
    }
  };

  return (
    <div className="bg-white border border-slate-200 rounded-lg p-4 shadow-sm">
      {/* Header */}
      <div className="flex items-center gap-2 mb-4">
        <div className="p-1.5 rounded-full bg-blue-100">
          <Info className="w-4 h-4 text-blue-600" />
        </div>
        <h3 className="text-sm font-semibold text-gray-800">Resumo do Cronograma</h3>
      </div>

      {/* Grid de informações */}
      <div className="grid grid-cols-2 gap-4 text-xs">
        {/* Horas por semana */}
        <div className="bg-blue-50 rounded-lg p-3 text-center">
          <p className="text-blue-600 font-medium mb-1">Por Semana</p>
          <p className="text-lg font-bold text-blue-700">{formatHours(totalHours)}</p>
          <p className="text-blue-600">~{topicsPerWeek} temas</p>
        </div>

        {/* Duração dos temas */}
        <div className="bg-green-50 rounded-lg p-3 text-center">
          <p className="text-green-600 font-medium mb-1">Por Tema</p>
          <p className="text-lg font-bold text-green-700">{topicDuration}min</p>
          <p className="text-green-600">duração</p>
        </div>

        {/* Total de semanas (se aplicável) */}
        {totalWeeks > 1 && (
          <div className="bg-purple-50 rounded-lg p-3 text-center col-span-2">
            <p className="text-purple-600 font-medium mb-1">Total do Cronograma</p>
            <p className="text-lg font-bold text-purple-700">
              {formatHours(totalHours * totalWeeks)} em {totalWeeks} semanas
            </p>
          </div>
        )}
      </div>

      {/* Domínio (se aplicável) */}
      {domain && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600 font-medium">Domínio:</span>
            <span className="font-semibold text-gray-800 capitalize">{domain}</span>
          </div>
        </div>
      )}
    </div>
  );
};
