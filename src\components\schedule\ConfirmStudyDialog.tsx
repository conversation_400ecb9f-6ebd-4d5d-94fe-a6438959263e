import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { BookOpen } from 'lucide-react';
import type { StudyTopic } from '@/types/study-schedule';

interface ConfirmStudyDialogProps {
  topic: StudyTopic | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

const ConfirmStudyDialog: React.FC<ConfirmStudyDialogProps> = ({
  topic,
  open,
  onOpenChange,
  onConfirm
}) => {
  if (!topic) return null;

  const handleConfirm = () => {
    onConfirm();
    onOpenChange(false);
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md">
        <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-hackathon-yellow">
          <div className="flex items-center gap-3">
            <div className="bg-white p-2 rounded-full border-2 border-black">
              <BookOpen className="h-5 w-5 text-black" />
            </div>
            <AlertDialogTitle className="text-xl font-bold text-black">
              Confirmar estudo
            </AlertDialogTitle>
          </div>
        </AlertDialogHeader>

        <div className="p-4 sm:p-6">
          <AlertDialogDescription className="text-base text-gray-700 mb-4">
            Deseja marcar{' '}
            <span className="font-semibold text-black">
              {topic.specialtyName || topic.specialty} &gt; {topic.themeName || topic.theme} &gt; {topic.focusName || topic.focus}
            </span>{' '}
            como estudado?
          </AlertDialogDescription>

          <AlertDialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-3 mt-6">
            <AlertDialogCancel className="w-full sm:w-auto border-2 border-black text-black font-semibold bg-white hover:bg-gray-50 rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all">
              Cancelar
            </AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleConfirm}
              className="w-full sm:w-auto bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
            >
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default ConfirmStudyDialog;
