
import type { DaySchedule, StudyTopic, AIScheduleOptions } from '@/types/study-schedule';

export interface RevisionInfo {
  date: Date;
  formattedDate: string;
  weekNumber: number;
  scheduleId: string;
  dayOfWeek: string;
}

export interface StudyResult {
  success: boolean;
  message: string;
  nextRevision?: {
    date: string;
    dayOfWeek: string;
    revisionNumber: number;
    daysUntil: number;
  };
  isLastRevision?: boolean;
  // ✅ NOVAS PROPRIEDADES para criação de semanas
  needsWeeks?: boolean;
  weeksNeeded?: number;
  revisionDate?: string;
  topicId?: string;
  revisionNumber?: number;
}

export interface GenerationStats {
  topicsCreated: number;
  specialties: string[];
  timeSpent: number;
}

export interface WeeklySchedule {
  recommendations: DaySchedule[];
  currentScheduleId?: string;
}
