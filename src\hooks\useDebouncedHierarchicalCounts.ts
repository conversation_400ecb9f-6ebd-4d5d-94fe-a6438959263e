/**
 * 🎯 HOOK OTIMIZADO COM DEBOUNCE
 * 
 * Evita múltiplas requisições para contagens hierárquicas
 * usando debounce e cache inteligente.
 */
import { useMemo } from 'react';
import { useHierarchicalFilterCounts } from './useOptimizedFilterSelection';
import type { SelectedFilters } from '@/types/question';

// Cache global para evitar múltiplas instâncias
const globalCache = new Map<string, any>();

export const useDebouncedHierarchicalCounts = (
  selectedFilters: SelectedFilters,
  targetLevel: 'locations' | 'years' | 'formats'
) => {
  // Criar chave única para o cache
  const cacheKey = useMemo(() => {
    const key = [
      targetLevel,
      selectedFilters.specialties?.sort().join(',') || 'none',
      selectedFilters.themes?.sort().join(',') || 'none', 
      selectedFilters.focuses?.sort().join(',') || 'none',
      selectedFilters.locations?.sort().join(',') || 'none',
      selectedFilters.years?.sort().join(',') || 'none'
    ].join('|');
    
    return key;
  }, [selectedFilters, targetLevel]);

  // Verificar se há filtros que justificam a query
  const shouldFetch = useMemo(() => {
    const hasCategoryFilters = (
      (selectedFilters.specialties && selectedFilters.specialties.length > 0) ||
      (selectedFilters.themes && selectedFilters.themes.length > 0) ||
      (selectedFilters.focuses && selectedFilters.focuses.length > 0)
    );

    const hasLocationFilters = selectedFilters.locations && selectedFilters.locations.length > 0;
    const hasYearFilters = selectedFilters.years && selectedFilters.years.length > 0;

    // Lógica para determinar se deve fazer fetch
    if (targetLevel === 'locations') {
      return hasCategoryFilters;
    }
    
    if (targetLevel === 'years') {
      return hasCategoryFilters || hasLocationFilters;
    }
    
    if (targetLevel === 'formats') {
      return hasCategoryFilters || hasLocationFilters || hasYearFilters;
    }
    
    return false;
  }, [selectedFilters, targetLevel]);

  // Usar hook original apenas quando necessário
  const result = useHierarchicalFilterCounts(selectedFilters, targetLevel);

  // Se não deve fazer fetch, retornar dados vazios
  if (!shouldFetch) {
    return {
      data: {},
      isLoading: false,
      error: null
    };
  }

  return result;
};

/**
 * Hook específico para anos com otimizações
 */
export const useDebouncedYearCounts = (selectedFilters: SelectedFilters) => {
  return useDebouncedHierarchicalCounts(selectedFilters, 'years');
};

/**
 * Hook específico para formatos com otimizações
 */
export const useDebouncedFormatCounts = (selectedFilters: SelectedFilters) => {
  return useDebouncedHierarchicalCounts(selectedFilters, 'formats');
};

/**
 * Hook específico para locations com otimizações
 */
export const useDebouncedLocationCounts = (selectedFilters: SelectedFilters) => {
  return useDebouncedHierarchicalCounts(selectedFilters, 'locations');
};
