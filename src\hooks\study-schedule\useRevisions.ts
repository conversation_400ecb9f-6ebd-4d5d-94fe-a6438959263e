
import { supabase } from "@/integrations/supabase/client";
import type { RevisionInfo } from "./types";
import { getWeekBounds } from "@/utils/dateUtils";

/**
 * Calculates the next revision date and the week it falls in
 */
export const calculateRevisionDateAndWeek = async (
  currentDate: Date,
  daysToAdd: number,
  schedules: any[]
): Promise<RevisionInfo | null> => {
  const normalizeDate = (date: Date | string) => {
    const d = new Date(date);
    d.setHours(0, 0, 0, 0);
    return d;
  };

  const nextDate = normalizeDate(currentDate);
  // ✅ CORREÇÃO: Exatamente daysToAdd dias (hoje 15 + 3 dias = dia 18)
  nextDate.setDate(nextDate.getDate() + daysToAdd);



  const sortedSchedules = [...schedules].sort((a, b) => 
    new Date(a.week_start_date).getTime() - new Date(b.week_start_date).getTime()
  );
  


  let targetSchedule = sortedSchedules.find(s => {
    const weekStart = normalizeDate(s.week_start_date);
    const weekEnd = normalizeDate(s.week_end_date);
    weekEnd.setHours(23, 59, 59);
    
    return nextDate >= weekStart && nextDate <= weekEnd;
  });

  if (!targetSchedule) {

    
    const lastWeek = sortedSchedules[sortedSchedules.length - 1];
    const lastWeekEndDate = normalizeDate(lastWeek.week_end_date);
    lastWeekEndDate.setHours(23, 59, 59);
    
    // If revision date is later than our latest available week, return null
    // The calling function will need to handle this case
    if (nextDate > lastWeekEndDate) {
  
      return null;
    }
  }

  if (!targetSchedule) {
    console.error('❌ Could not find a week for the revision date');
    return null;
  }

  const dayOfWeekNames = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
  const dayOfWeek = dayOfWeekNames[nextDate.getDay()];

  return {
    date: nextDate,
    formattedDate: nextDate.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit', year: '2-digit' }),
    weekNumber: targetSchedule.week_number,
    scheduleId: targetSchedule.id,
    dayOfWeek: dayOfWeek
  };
};

/**
 * ✅ NOVA FUNÇÃO: Calcular quantas semanas são necessárias para uma data de revisão
 */
export const calculateWeeksNeeded = async (
  revisionDate: Date,
  userId: string
): Promise<{ weeksNeeded: number; revisionDateFormatted: string } | null> => {
  try {
    // Buscar última semana existente
    const { data: lastWeek } = await supabase
      .from('study_schedules')
      .select('week_number, week_end_date')
      .eq('user_id', userId)
      .order('week_number', { ascending: false })
      .limit(1)
      .single();

    if (!lastWeek) {
      // Se não há semanas, calcular a partir de hoje
      const today = new Date();
      const weeksNeeded = Math.ceil((revisionDate.getTime() - today.getTime()) / (7 * 24 * 60 * 60 * 1000));
      return {
        weeksNeeded: Math.max(1, weeksNeeded),
        revisionDateFormatted: revisionDate.toLocaleDateString('pt-BR')
      };
    }

    // Calcular quantas semanas são necessárias após a última
    const lastWeekEnd = new Date(lastWeek.week_end_date + 'T23:59:59');
    const daysDifference = Math.ceil((revisionDate.getTime() - lastWeekEnd.getTime()) / (24 * 60 * 60 * 1000));

    if (daysDifference <= 0) {
      return null; // Revisão cabe nas semanas existentes
    }

    const weeksNeeded = Math.ceil(daysDifference / 7);

    return {
      weeksNeeded,
      revisionDateFormatted: revisionDate.toLocaleDateString('pt-BR')
    };
  } catch (error) {
    console.error('❌ Erro ao calcular semanas necessárias:', error);
    return null;
  }
};

/**
 * ✅ NOVA FUNÇÃO: Criar semanas automaticamente
 */
export const createWeeksForRevision = async (
  weeksCount: number,
  userId: string
): Promise<boolean> => {
  try {
    // Buscar última semana para continuar a sequência
    const { data: lastWeek } = await supabase
      .from('study_schedules')
      .select('week_number, week_end_date')
      .eq('user_id', userId)
      .order('week_number', { ascending: false })
      .limit(1)
      .single();

    const nextWeekNumber = lastWeek ? lastWeek.week_number + 1 : 1;

    // Calcular data base para as novas semanas
    let baseDate = new Date();
    if (lastWeek) {
      const lastEndDate = new Date(lastWeek.week_end_date + 'T12:00:00Z');
      baseDate = new Date(lastEndDate);
      baseDate.setUTCDate(lastEndDate.getUTCDate() + 1); // Próximo domingo
    }

    const weeksToCreate = [];

    for (let i = 0; i < weeksCount; i++) {
      const weekBaseDate = new Date(baseDate);
      if (i > 0) {
        weekBaseDate.setDate(baseDate.getDate() + (i * 7));
      }

      const { startDate: weekStart, endDate: weekEnd } = getWeekBounds(weekBaseDate);

      weeksToCreate.push({
        user_id: userId,
        week_number: nextWeekNumber + i,
        week_start_date: weekStart.toISOString().split('T')[0],
        week_end_date: weekEnd.toISOString().split('T')[0],
        status: 'active'
      });
    }

    const { error } = await supabase
      .from('study_schedules')
      .insert(weeksToCreate);

    if (error) {
      console.error('❌ Erro ao criar semanas:', error);
      return false;
    }

    // ✅ LIMPEZA: Log removido - operação de criação de semanas
    return true;
  } catch (error) {
    console.error('❌ Erro ao criar semanas para revisão:', error);
    return false;
  }
};

/**
 * Creates a revision item based on an original study item
 */
export const createRevisionItem = async (
  originalItem: any,
  revisionDate: Date,
  revisionNumber: number,
  parentId: string,
  revisionChain: string[],
  schedules: any[]
) => {
  try {


    const revisionInfo = await calculateRevisionDateAndWeek(revisionDate, 0, schedules);


    if (!revisionInfo) {
      // ✅ LIMPEZA: Log removido - erro já tratado no retorno
      return null;
    }

    const newItem = {
      schedule_id: revisionInfo.scheduleId,
      day_of_week: revisionInfo.dayOfWeek,
      topic: originalItem.topic,
      duration: originalItem.duration,
      priority: originalItem.priority,
      type: 'revision',
      specialty_id: originalItem.specialty_id,
      theme_id: originalItem.theme_id,
      focus_id: originalItem.focus_id,
      difficulty: originalItem.difficulty,
      activity_type: originalItem.activity_type,
      start_time: originalItem.start_time,
      specialty_name: originalItem.specialty_name,
      theme_name: originalItem.theme_name,
      focus_name: originalItem.focus_name,
      activity_description: `Revisão ${revisionNumber} - ${originalItem.activity_description}`,
      week_number: revisionInfo.weekNumber,
      study_status: 'pending',
      revision_number: revisionNumber,
      parent_item_id: parentId,
      revision_chain: revisionChain,
      is_manual: originalItem.is_manual
    };

    const { error } = await supabase
      .from('study_schedule_items')
      .insert(newItem);

    if (error) throw error;


    return newItem;
  } catch (error) {
    console.error('❌ Error creating revision:', error);
    throw error;
  }
};
