import { supabase } from '@/integrations/supabase/client';

/**
 * 🔧 RESET DO TIMER DA SESSÃO
 * 
 * Corrige sessões com timer incorreto definindo actual_study_start_time
 */

export const resetSessionTimer = async (sessionId: string): Promise<boolean> => {
  try {
    // Buscar dados da sessão
    const { data: session, error: fetchError } = await supabase
      .from('study_sessions')
      .select('question_times, started_at')
      .eq('id', sessionId)
      .single();

    if (fetchError || !session) {
      return false;
    }

    // Calcular quando realmente começou a estudar
    // Usar o tempo atual menos a soma dos tempos das questões
    const questionTimes = session.question_times as Record<string, number> || {};
    const totalQuestionTime = Object.values(questionTimes).reduce((sum, time) => sum + time, 0);
    
    // Definir início real do estudo como agora menos o tempo total das questões
    const actualStudyStartTime = new Date(Date.now() - (totalQuestionTime * 1000));

    // Atualizar no banco
    const { error: updateError } = await supabase
      .from('study_sessions')
      .update({
        actual_study_start_time: actualStudyStartTime.toISOString(),
        total_time_spent: totalQuestionTime // Corrigir também o total
      })
      .eq('id', sessionId);

    if (updateError) {
      return false;
    }

    // Limpar localStorage para forçar reload
    const storageKey = `robust_timer_${sessionId}`;
    localStorage.removeItem(storageKey);

    return true;
  } catch (error) {
    return false;
  }
};

// Expor no console para debug
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).resetSessionTimer = resetSessionTimer;
}
