
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { ensureUserId } from "@/utils/ensureUserId";

interface Folder {
  id: string;
  user_id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export const useFolders = () => {
  const queryClient = useQueryClient();

  const { data: folders, isLoading } = useQuery({
    queryKey: ['folders'],
    queryFn: async () => {
      const userId = await ensureUserId();

      if (!userId) {
        console.error('No user found');
        return [];
      }

      const { data, error } = await supabase
        .from('pedbook_notes_folders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error loading folders:', error);
        toast.error('Erro ao carregar pastas');
        throw error;
      }

      console.log('Folders loaded:', data);
      return data as Folder[];
    },
  });

  const createFolder = useMutation({
    mutationFn: async (name: string) => {
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      if (userError) {
        toast.error('Erro ao identificar usuário');
        throw userError;
      }

      const { data, error } = await supabase
        .from('pedbook_notes_folders')
        .insert([{
          name,
          user_id: userData.user.id,
        }])
        .select()
        .single();

      if (error) {
        toast.error('Erro ao criar pasta');
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['folders'] });
      toast.success('Pasta criada com sucesso');
    },
  });

  const deleteFolder = useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('pedbook_notes_folders')
        .delete()
        .eq('id', id);

      if (error) {
        toast.error('Erro ao deletar pasta');
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['folders'] });
      toast.success('Pasta deletada com sucesso');
    },
  });

  return {
    folders: folders || [],
    isLoading,
    createFolder,
    deleteFolder,
  };
};
