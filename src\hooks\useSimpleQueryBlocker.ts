import { useQueryClient } from '@tanstack/react-query';
import { useEffect } from 'react';

/**
 * 🎯 BLOQUEADOR SIMPLES DE QUERIES DUPLICADAS
 * 
 * Abordagem minimalista para eliminar apenas as duplicações mais óbvias
 * Sem complexidade desnecessária
 */

export const useSimpleQueryBlocker = () => {
  const queryClient = useQueryClient();

  useEffect(() => {
    // ✅ BLOQUEAR: Queries duplicadas de study_sessions para a mesma sessão
    const originalFetch = queryClient.fetchQuery;
    
    queryClient.fetchQuery = async (options: any) => {
      const queryKey = options.queryKey || [];
      const keyString = JSON.stringify(queryKey);
      
      // ✅ DETECTAR: Queries duplicadas de study_sessions
      if (keyString.includes('study_sessions') && keyString.includes('select')) {
        // Verificar se já temos dados consolidados no cache
        const consolidatedKey = ['consolidated-session-data'];
        const consolidatedData = queryClient.getQueryData(consolidatedKey);
        
        if (consolidatedData) {
          console.log('🚫 [QueryBlocker] Bloqueando query duplicada:', keyString);
          return Promise.resolve({ data: consolidatedData });
        }
      }
      
      // ✅ DETECTAR: Queries duplicadas de session_events
      if (keyString.includes('session_events')) {
        const consolidatedKey = ['consolidated-session-data'];
        const consolidatedData = queryClient.getQueryData(consolidatedKey);
        
        if (consolidatedData) {
          console.log('🚫 [QueryBlocker] Bloqueando session_events duplicado:', keyString);
          return Promise.resolve({ data: (consolidatedData as any)?.sessionEvents || [] });
        }
      }
      
      // ✅ DETECTAR: Queries duplicadas de user_answers
      if (keyString.includes('user_answers')) {
        const consolidatedKey = ['consolidated-session-data'];
        const consolidatedData = queryClient.getQueryData(consolidatedKey);
        
        if (consolidatedData) {
          console.log('🚫 [QueryBlocker] Bloqueando user_answers duplicado:', keyString);
          return Promise.resolve({ data: (consolidatedData as any)?.userAnswers || [] });
        }
      }
      
      // Permitir query original se não há dados consolidados
      return originalFetch.call(queryClient, options);
    };
    
    // Cleanup
    return () => {
      queryClient.fetchQuery = originalFetch;
    };
  }, [queryClient]);
};

/**
 * Hook para usar em componentes que fazem queries duplicadas
 */
export const useBlockDuplicateQueries = (sessionId: string | null) => {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    if (!sessionId) return;
    
    // ✅ PRÉ-POPULAR: Cache com dados consolidados se disponível
    const consolidatedKey = ['consolidated-session-data', sessionId];
    const consolidatedData = queryClient.getQueryData(consolidatedKey);
    
    if (consolidatedData) {
      // Definir dados nos caches das queries individuais
      const sessionKey = ['study_sessions', sessionId];
      const eventsKey = ['session_events', sessionId];
      const answersKey = ['user_answers', sessionId];
      
      queryClient.setQueryData(sessionKey, (consolidatedData as any)?.session);
      queryClient.setQueryData(eventsKey, (consolidatedData as any)?.sessionEvents);
      queryClient.setQueryData(answersKey, (consolidatedData as any)?.userAnswers);
      
      console.log('✅ [QueryBlocker] Pré-populando caches para sessão:', sessionId);
    }
  }, [sessionId, queryClient]);
};
