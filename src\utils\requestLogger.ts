/**
 * 🔍 SISTEMA DE LOGS PARA DIAGNÓSTICO DE DUPLICAÇÕES
 * 
 * Monitora e registra todas as chamadas para identificar duplicações
 */

interface RequestLog {
  timestamp: number;
  method: string;
  url: string;
  stackTrace: string;
  component: string;
}

class RequestLogger {
  private logs: RequestLog[] = [];
  private duplicateThreshold = 100; // ms para considerar duplicação

  /**
   * Registrar uma chamada de API
   */
  logRequest(method: string, url: string, component?: string) {
    const timestamp = Date.now();
    const stackTrace = new Error().stack || '';
    
    const log: RequestLog = {
      timestamp,
      method,
      url,
      stackTrace,
      component: component || this.extractComponentFromStack(stackTrace)
    };

    this.logs.push(log);
    
    // Detectar duplicações em tempo real
    this.detectDuplicates(log);
    
    // Limpar logs antigos (manter apenas últimos 50)
    if (this.logs.length > 50) {
      this.logs = this.logs.slice(-50);
    }
  }

  /**
   * Detectar duplicações em tempo real
   */
  private detectDuplicates(currentLog: RequestLog) {
    const recentLogs = this.logs.filter(log => 
      currentLog.timestamp - log.timestamp < this.duplicateThreshold
    );

    const duplicates = recentLogs.filter(log => 
      log.url === currentLog.url && 
      log.method === currentLog.method &&
      log.timestamp !== currentLog.timestamp
    );

    if (duplicates.length > 0) {
      // Duplicação detectada
    }
  }

  /**
   * Extrair componente do stack trace
   */
  private extractComponentFromStack(stackTrace: string): string {
    const lines = stackTrace.split('\n');
    
    for (const line of lines) {
      // Procurar por nomes de componentes React
      const match = line.match(/at (\w+(?:Hook|Component|Provider)?)/);
      if (match && !line.includes('node_modules')) {
        return match[1];
      }
    }
    
    return 'Unknown';
  }

  /**
   * Obter relatório de duplicações
   */
  getDuplicateReport(): any {
    const urlCounts: Record<string, number> = {};
    const componentCounts: Record<string, number> = {};
    
    this.logs.forEach(log => {
      const key = `${log.method} ${log.url}`;
      urlCounts[key] = (urlCounts[key] || 0) + 1;
      componentCounts[log.component] = (componentCounts[log.component] || 0) + 1;
    });

    const duplicates = Object.entries(urlCounts)
      .filter(([_, count]) => count > 1)
      .sort(([_, a], [__, b]) => b - a);

    return {
      totalRequests: this.logs.length,
      duplicates,
      topComponents: Object.entries(componentCounts)
        .sort(([_, a], [__, b]) => b - a)
        .slice(0, 10)
    };
  }

  /**
   * Limpar logs
   */
  clear() {
    this.logs = [];
  }
}

// Instância singleton
export const requestLogger = new RequestLogger();

/**
 * Hook para monitorar requests em componentes React
 */
export const useRequestLogger = (componentName: string) => {
  const logRequest = (method: string, url: string) => {
    requestLogger.logRequest(method, url, componentName);
  };

  return { logRequest };
};

/**
 * Interceptor para Supabase
 */
export const setupSupabaseLogger = () => {
  // Interceptar fetch global
  const originalFetch = window.fetch;
  
  window.fetch = async (...args) => {
    const [url, options] = args;
    const method = options?.method || 'GET';
    
    if (typeof url === 'string' && url.includes('supabase')) {
      requestLogger.logRequest(method, url);
    }
    
    return originalFetch(...args);
  };
};

// Configurar automaticamente
if (typeof window !== 'undefined') {
  setupSupabaseLogger();
  
  // Expor no console para debug
  (window as any).requestLogger = requestLogger;
}
