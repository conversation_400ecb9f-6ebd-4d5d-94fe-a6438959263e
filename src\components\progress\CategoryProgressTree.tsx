import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useUserStatistics } from "@/hooks/useUserStatistics";
import { supabase } from "@/integrations/supabase/client";
import { ChevronRight, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";

/**
 * ✅ FUNÇÃO AUXILIAR: Chunking para IDs grandes
 * Divide array de IDs em chunks menores para evitar URLs muito longas
 */
const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

/**
 * ✅ FUNÇÃO AUXILIAR: Buscar categorias com chunking
 * Fallback para casos onde RPC falha ou não está disponível
 */
const fetchCategoriesWithChunking = async (categoryIds: string[], chunkSize: number = 50) => {
  const chunks = chunkArray(categoryIds, chunkSize);
  const allResults: any[] = [];



  for (const chunk of chunks) {
    const { data, error } = await supabase
      .from("study_categories")
      .select("id, name, type, parent_id")
      .in("id", chunk);

    if (error) {
      throw error;
    }

    if (data) {
      allResults.push(...data);
    }
  }

  return allResults;
};

interface CategoryData {
  id: string;
  name: string;
  correct: number;
  total: number;
  children?: Record<string, CategoryData>;
}

interface HierarchicalStats {
  specialties: Record<string, CategoryData>;
  themes: Record<string, CategoryData>;
  focuses: Record<string, CategoryData>;
}

export const CategoryProgressTree = () => {
  const { data: stats, isLoading } = useUserStatistics();
  const [categoryNames, setCategoryNames] = useState<Record<string, string>>({});
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set());
  const [hierarchicalData, setHierarchicalData] = useState<HierarchicalStats | null>(null);

  useEffect(() => {
    const loadCategoryNames = async () => {
      if (!stats) return;

      const categoryIds = [
        ...Object.keys(stats.by_specialty || {}),
        ...Object.keys(stats.by_theme || {}),
        ...Object.keys(stats.by_focus || {})
      ];

      if (categoryIds.length === 0) return;

      // ✅ SOLUÇÃO: Usar RPC quando há muitos IDs para evitar URLs muito longas
      let categories;
      if (categoryIds.length > 50) {


        try {
          const { data: rpcData, error: rpcError } = await supabase.rpc('get_categories_by_ids', {
            category_ids: categoryIds
          });

          if (rpcError) {

            // Fallback para chunking
            categories = await fetchCategoriesWithChunking(categoryIds);
          } else {
            categories = rpcData;
          }
        } catch (error) {

          // Fallback para chunking
          categories = await fetchCategoriesWithChunking(categoryIds);
        }
      } else {
        // Usar query normal para poucos IDs
        const { data } = await supabase
          .from('study_categories')
          .select('id, name, type, parent_id')
          .in('id', categoryIds);
        categories = data;
      }

      if (categories) {
        const names = categories.reduce((acc, cat) => ({
          ...acc,
          [cat.id]: cat.name
        }), {});
        setCategoryNames(names);

        // Organize data hierarchically
        const hierarchical: HierarchicalStats = {
          specialties: {},
          themes: {},
          focuses: {}
        };

        // Process specialties
        Object.entries(stats.by_specialty || {}).forEach(([id, data]) => {
          hierarchical.specialties[id] = {
            id,
            name: names[id] || 'Unknown',
            correct: data.correct,
            total: data.total,
            children: {}
          };
        });

        // Process themes and link to specialties
        Object.entries(stats.by_theme || {}).forEach(([id, data]) => {
          const theme = categories.find(c => c.id === id);
          if (theme && theme.parent_id && hierarchical.specialties[theme.parent_id]) {
            hierarchical.specialties[theme.parent_id].children![id] = {
              id,
              name: names[id] || 'Unknown',
              correct: data.correct,
              total: data.total,
              children: {}
            };
          }
        });

        // Process focuses and link to themes
        Object.entries(stats.by_focus || {}).forEach(([id, data]) => {
          const focus = categories.find(c => c.id === id);
          if (focus && focus.parent_id) {
            Object.values(hierarchical.specialties).forEach(specialty => {
              if (specialty.children![focus.parent_id]) {
                specialty.children![focus.parent_id].children![id] = {
                  id,
                  name: names[id] || 'Unknown',
                  correct: data.correct,
                  total: data.total
                };
              }
            });
          }
        });

        setHierarchicalData(hierarchical);
      }
    };

    loadCategoryNames();
  }, [stats]);

  const toggleCategory = (id: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const renderCategory = (category: CategoryData, level: number = 0) => {
    const hasChildren = category.children && Object.keys(category.children).length > 0;
    const isExpanded = expandedCategories.has(category.id);
    const accuracy = ((category.correct / category.total) * 100).toFixed(1);

    return (
      <div key={category.id} className="space-y-2">
        <div className="flex items-center gap-2">
          {hasChildren && (
            <Button
              variant="ghost"
              size="sm"
              className="p-1 h-6 w-6"
              onClick={() => toggleCategory(category.id)}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
          <div className="flex-1">
            <div className="flex justify-between text-sm">
              <span className="font-medium">{category.name}</span>
              <span className="text-gray-600">
                {category.correct} de {category.total} ({accuracy}%)
              </span>
            </div>
            <Progress value={Number(accuracy)} className="h-2 mt-1" />
          </div>
        </div>

        {isExpanded && hasChildren && (
          <div className="ml-6 space-y-4">
            {Object.values(category.children!).map(child => renderCategory(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-48">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  if (!stats || !hierarchicalData) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Progresso por Categoria</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {Object.values(hierarchicalData.specialties).map(specialty => 
          renderCategory(specialty)
        )}
      </CardContent>
    </Card>
  );
};