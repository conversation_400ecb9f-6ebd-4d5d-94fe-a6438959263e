import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Target,
  Building2,
  Calendar,
  ArrowRight,
  CheckCircle,
  X,
  Search,
  Clock,
  ChevronLeft,
  ChevronRight,
  GraduationCap,
  Users,
  BookOpen
} from 'lucide-react';
import { useStudyPreferences, StudyPreferencesFormData } from '@/hooks/useStudyPreferences';
import { useAuth } from '@/contexts/AuthContext';
import { useMedicalSpecialties } from '@/hooks/useMedicalSpecialties';

export default function StudyPreferences() {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    preferences,
    availableInstitutions,
    isLoadingInstitutions,
    savePreferences,
    isSaving
  } = useStudyPreferences();

  // Buscar especialidades médicas
  const { data: medicalSpecialties, isLoading: isLoadingSpecialties } = useMedicalSpecialties();

  // Não redirecionar automaticamente - deixar usuário escolher onde quer editar

  const [formData, setFormData] = useState<StudyPreferencesFormData>({
    target_specialty: '',
    specialty_unknown: false,
    study_months: 6,
    target_institutions: [],
    institutions_unknown: false
  });

  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  // Filtrar instituições baseado na busca
  const filteredInstitutions = availableInstitutions?.filter(institution =>
    institution.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // Paginação
  const totalPages = Math.ceil(filteredInstitutions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedInstitutions = filteredInstitutions.slice(startIndex, endIndex);

  // Reset página quando busca muda
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm]);

  // Carregar preferências existentes - com dependência mais específica
  React.useEffect(() => {
    if (preferences) {
      setFormData(prev => {
        const newData = {
          target_specialty: preferences.target_specialty || '',
          specialty_unknown: preferences.specialty_unknown || false, // ✅ CORREÇÃO: Usar valor explícito
          study_months: preferences.study_months || 6,
          target_institutions: preferences.target_institutions.map(inst => inst.id),
          institutions_unknown: preferences.target_institutions_unknown
        };

        // Só atualizar se realmente mudou
        if (JSON.stringify(prev) !== JSON.stringify(newData)) {
          return newData;
        }
        return prev;
      });
    }
  }, [preferences?.target_specialty, preferences?.target_specialty_name, preferences?.study_months, preferences?.target_institutions_unknown, preferences?.target_institutions]);

  const handleSpecialtyChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      target_specialty: value,
      specialty_unknown: false
    }));
  };

  const handleSpecialtyUnknownChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      specialty_unknown: checked,
      target_specialty: checked ? '' : prev.target_specialty
    }));
  };

  const handleInstitutionToggle = (institutionId: string) => {
    if (formData.institutions_unknown) return;

    setFormData(prev => ({
      ...prev,
      target_institutions: prev.target_institutions.includes(institutionId)
        ? prev.target_institutions.filter(id => id !== institutionId)
        : [...prev.target_institutions, institutionId]
    }));
  };

  const handleInstitutionsUnknownChange = (checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      institutions_unknown: checked,
      target_institutions: checked ? [] : prev.target_institutions
    }));
  };

  const handleStudyMonthsChange = (value: number[]) => {
    setFormData(prev => ({
      ...prev,
      study_months: value[0]
    }));
  };

  const removeInstitution = (institutionId: string) => {
    setFormData(prev => ({
      ...prev,
      target_institutions: prev.target_institutions.filter(id => id !== institutionId)
    }));
  };

  const getSelectedInstitutionNames = () => {
    return availableInstitutions
      ?.filter(inst => formData.target_institutions.includes(inst.id))
      .map(inst => inst.name) || [];
  };

  const isFormValid = () => {
    const hasSpecialty = formData.specialty_unknown || formData.target_specialty.length > 0;
    const hasInstitutions = formData.institutions_unknown || formData.target_institutions.length > 0;
    const hasStudyTime = formData.study_months > 0;
    return hasSpecialty && hasInstitutions && hasStudyTime;
  };

  const handleSubmit = async () => {
    if (!isFormValid()) return;

    try {
      await savePreferences(formData);
      navigate('/plataformadeestudos');
    } catch (error) {
      console.error('Erro ao salvar preferências:', error);
    }
  };

  if (!user) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-2 sm:p-4">
      <div className="w-[95%] sm:container sm:max-w-4xl mx-auto">
        {/* Header - Compacto para mobile */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-4 sm:mb-8"
        >
          <div className="flex justify-center mb-3 sm:mb-6">
            <div className="bg-white border-2 border-black px-3 py-2 sm:px-6 sm:py-3 shadow-lg relative rounded-lg">
              <span className="font-bold text-xl sm:text-3xl tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                MedEvo
              </span>
              <div className="absolute -right-1 -top-1 sm:-right-2 sm:-top-2">
                <span className="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded-full border border-black font-bold shadow-sm">
                  beta
                </span>
              </div>
            </div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="space-y-2 sm:space-y-4"
          >
            <h1 className="text-2xl sm:text-4xl font-bold text-gray-900 mb-2 sm:mb-3 px-2">
              🎯 Personalize sua Jornada
            </h1>
            <p className="text-gray-600 text-sm sm:text-xl max-w-2xl mx-auto leading-relaxed px-2">
              Configure seu perfil para um cronograma personalizado
            </p>

            {/* Progress indicator - Menor no mobile */}
            <div className="flex items-center justify-center gap-2 mt-3 sm:mt-6">
              <div className="flex items-center gap-2 bg-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-full border-2 border-gray-200 shadow-sm">
                <GraduationCap className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
                <span className="text-xs sm:text-sm font-medium text-gray-700">Configuração</span>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Form Cards - Responsivo */}
        <div className="grid gap-3 sm:gap-6">
          {/* Especialidade Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="border-2 border-gray-200 shadow-lg sm:shadow-xl bg-white hover:shadow-xl sm:hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-100 p-3 sm:p-6">
                <CardTitle className="flex items-center gap-2 sm:gap-3">
                  <div className="p-2 sm:p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg sm:rounded-xl shadow-lg">
                    <Target className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg sm:text-2xl font-bold text-gray-900 truncate">Especialidade</h3>
                    <p className="text-gray-600 font-normal text-sm sm:text-base hidden sm:block">Escolha a especialidade que você pretende seguir</p>
                    <p className="text-gray-600 font-normal text-xs sm:hidden">Sua especialidade alvo</p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3 sm:p-6 space-y-3 sm:space-y-4">
                <div className="space-y-3 sm:space-y-4">
                  <div className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <Checkbox
                      id="specialty-unknown"
                      checked={formData.specialty_unknown}
                      onCheckedChange={handleSpecialtyUnknownChange}
                      className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                    />
                    <Label htmlFor="specialty-unknown" className="text-xs sm:text-sm font-medium text-gray-700 cursor-pointer">
                      🤔 Ainda não sei / Decidindo
                    </Label>
                  </div>

                  {!formData.specialty_unknown && (
                    <div className="space-y-2 sm:space-y-3">
                      <Label htmlFor="specialty" className="text-xs sm:text-sm font-semibold text-gray-700">
                        Selecione sua especialidade
                      </Label>
                      <Select
                        key={`specialty-${formData.target_specialty}-${preferences?.target_specialty_name}`}
                        value={formData.target_specialty}
                        onValueChange={handleSpecialtyChange}
                      >
                        <SelectTrigger className="w-full h-10 sm:h-12 border-2 border-gray-300 focus:border-blue-500 bg-white text-sm sm:text-base">
                          <SelectValue placeholder="🩺 Escolha uma especialidade..." />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {isLoadingSpecialties ? (
                            <SelectItem value="loading" disabled>Carregando especialidades...</SelectItem>
                          ) : (
                            medicalSpecialties?.map((specialty) => (
                              <SelectItem key={specialty.id} value={specialty.id} className="cursor-pointer text-sm">
                                {specialty.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Instituições Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="border-2 border-gray-200 shadow-lg sm:shadow-xl bg-white hover:shadow-xl sm:hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-100 p-3 sm:p-6">
                <CardTitle className="flex items-center gap-2 sm:gap-3">
                  <div className="p-2 sm:p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg sm:rounded-xl shadow-lg">
                    <Building2 className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg sm:text-2xl font-bold text-gray-900 truncate">Instituições</h3>
                    <p className="text-gray-600 font-normal text-sm sm:text-base hidden sm:block">Selecione onde você pretende fazer residência</p>
                    <p className="text-gray-600 font-normal text-xs sm:hidden">Suas instituições alvo</p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3 sm:p-6 space-y-3 sm:space-y-4">

                <div className="space-y-3 sm:space-y-4">
                  <div className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 bg-gray-50 rounded-lg border border-gray-200">
                    <Checkbox
                      id="institutions-unknown"
                      checked={formData.institutions_unknown}
                      onCheckedChange={handleInstitutionsUnknownChange}
                      className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600"
                    />
                    <Label htmlFor="institutions-unknown" className="text-xs sm:text-sm font-medium text-gray-700 cursor-pointer">
                      🏥 Qualquer instituição
                    </Label>
                  </div>

                  {!formData.institutions_unknown && (
                    <>
                      {/* Instituições Selecionadas */}
                      {formData.target_institutions.length > 0 && (
                        <div className="space-y-2 sm:space-y-3">
                          <Label className="text-xs sm:text-sm font-semibold text-gray-700">
                            🎯 Selecionadas ({formData.target_institutions.length})
                          </Label>
                          <div className="flex flex-wrap gap-1 sm:gap-2 p-2 sm:p-3 bg-green-50 rounded-lg border border-green-200 max-h-24 sm:max-h-32 overflow-y-auto">
                            {getSelectedInstitutionNames().map((name, index) => (
                              <Badge
                                key={index}
                                variant="secondary"
                                className="flex items-center gap-1 sm:gap-2 bg-white border border-green-300 text-green-800 hover:bg-green-100 transition-colors text-xs sm:text-sm"
                              >
                                <Building2 className="h-2 w-2 sm:h-3 sm:w-3" />
                                <span className="truncate max-w-[120px] sm:max-w-none">{name}</span>
                                <X
                                  className="h-2 w-2 sm:h-3 sm:w-3 cursor-pointer hover:text-red-500 transition-colors flex-shrink-0"
                                  onClick={() => removeInstitution(formData.target_institutions[index])}
                                />
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Busca de Instituições */}
                      <div className="space-y-2 sm:space-y-3">
                        <Label className="text-xs sm:text-sm font-semibold text-gray-700">🔍 Buscar</Label>
                        <div className="relative">
                          <Search className="absolute left-2 sm:left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                          <Input
                            placeholder="Nome da instituição..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-8 sm:pl-12 h-9 sm:h-12 border-2 border-gray-300 focus:border-green-500 bg-white rounded-lg text-sm sm:text-base"
                          />
                        </div>
                      </div>

                      {/* Lista de Instituições */}
                      <div className="max-h-48 sm:max-h-64 overflow-y-auto border-2 border-gray-200 rounded-lg bg-gray-50">
                        <div className="p-2 sm:p-4 space-y-1 sm:space-y-2">
                          {isLoadingInstitutions ? (
                            <div className="text-center py-6 sm:py-8 text-gray-500">
                              <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-green-500 mx-auto mb-2"></div>
                              <p className="text-sm sm:text-base">Carregando...</p>
                            </div>
                          ) : filteredInstitutions.length === 0 ? (
                            <div className="text-center py-6 sm:py-8 text-gray-500">
                              <Building2 className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 text-gray-300" />
                              <p className="text-sm sm:text-base">Nenhuma instituição encontrada</p>
                              <p className="text-xs sm:text-sm">Tente ajustar sua busca</p>
                            </div>
                          ) : (
                            paginatedInstitutions.map((institution) => (
                              <div
                                key={institution.id}
                                className="flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 hover:bg-white rounded-lg cursor-pointer border border-transparent hover:border-green-200 transition-all"
                                onClick={() => handleInstitutionToggle(institution.id)}
                              >
                                <Checkbox
                                  checked={formData.target_institutions.includes(institution.id)}
                                  onChange={() => {}} // Controlled by parent click
                                  className="data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600 flex-shrink-0"
                                />
                                <Label className="flex-1 text-xs sm:text-sm cursor-pointer font-medium text-gray-700 leading-tight">
                                  {institution.name}
                                </Label>
                              </div>
                            ))
                          )}
                        </div>
                      </div>

                        {/* Paginação - Responsiva */}
                        {totalPages > 1 && (
                          <div className="flex items-center justify-between px-2 sm:px-4 py-2 sm:py-3 border-t-2 border-gray-200 bg-white rounded-b-lg">
                            <div className="text-xs sm:text-sm text-gray-600">
                              <span className="hidden sm:inline">Página {currentPage} de {totalPages} • </span>
                              <span className="sm:hidden">{currentPage}/{totalPages} • </span>
                              {filteredInstitutions.length} inst.
                            </div>
                            <div className="flex items-center gap-1 sm:gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                                disabled={currentPage === 1}
                                className="h-7 w-7 sm:h-9 sm:w-9 p-0 border-2 border-green-300 hover:bg-green-50"
                              >
                                <ChevronLeft className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                              <span className="text-xs sm:text-sm text-gray-700 px-2 sm:px-3 py-0.5 sm:py-1 bg-green-50 rounded font-medium">
                                {currentPage}
                              </span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                                disabled={currentPage === totalPages}
                                className="h-7 w-7 sm:h-9 sm:w-9 p-0 border-2 border-green-300 hover:bg-green-50"
                              >
                                <ChevronRight className="h-3 w-3 sm:h-4 sm:w-4" />
                              </Button>
                            </div>
                          </div>
                        )}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Tempo de Estudo Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <Card className="border-2 border-gray-200 shadow-lg sm:shadow-xl bg-white hover:shadow-xl sm:hover:shadow-2xl transition-all duration-300">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b border-gray-100 p-3 sm:p-6">
                <CardTitle className="flex items-center gap-2 sm:gap-3">
                  <div className="p-2 sm:p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg sm:rounded-xl shadow-lg">
                    <Clock className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="text-lg sm:text-2xl font-bold text-gray-900 truncate">Tempo de Estudo</h3>
                    <p className="text-gray-600 font-normal text-sm sm:text-base hidden sm:block">Defina seu cronograma de preparação</p>
                    <p className="text-gray-600 font-normal text-xs sm:hidden">Duração dos estudos</p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-3 sm:p-6 space-y-4 sm:space-y-6">

                <div className="space-y-4 sm:space-y-6">
                  <div className="text-center bg-gradient-to-r from-purple-50 to-pink-50 p-3 sm:p-6 rounded-xl border border-purple-200">
                    <div className="flex items-center justify-center gap-1 sm:gap-2 mb-1 sm:mb-2">
                      <Calendar className="h-4 w-4 sm:h-6 sm:w-6 text-purple-600" />
                      <span className="text-2xl sm:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                        {formData.study_months}
                      </span>
                      <span className="text-sm sm:text-xl text-gray-600 font-medium">
                        {formData.study_months === 1 ? 'mês' : 'meses'}
                      </span>
                    </div>
                    <p className="text-xs sm:text-sm text-gray-600">
                      Tempo total para preparação
                    </p>
                  </div>

                  <div className="px-1 sm:px-2">
                    <Slider
                      value={[formData.study_months]}
                      onValueChange={handleStudyMonthsChange}
                      max={12}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs sm:text-sm text-gray-500 mt-1 sm:mt-2 px-1">
                      <span className="flex items-center gap-1">
                        <Clock className="h-2 w-2 sm:h-3 sm:w-3" />
                        1 mês
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-2 w-2 sm:h-3 sm:w-3" />
                        12 meses
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Submit Button - Responsivo */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="pt-2 sm:pt-4"
          >
            <Button
              onClick={handleSubmit}
              disabled={!isFormValid() || isSaving}
              className="w-full h-12 sm:h-16 text-base sm:text-xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white border-2 border-gray-300 shadow-lg sm:shadow-2xl hover:shadow-xl sm:hover:shadow-3xl transition-all duration-300 rounded-xl"
            >
              {isSaving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 sm:h-6 sm:w-6 border-b-2 border-white mr-2 sm:mr-3"></div>
                  <span className="hidden sm:inline">Salvando suas preferências...</span>
                  <span className="sm:hidden">Salvando...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 sm:h-6 sm:w-6 mr-2 sm:mr-3" />
                  <span className="hidden sm:inline">🚀 Finalizar Configuração</span>
                  <span className="sm:hidden">🚀 Finalizar</span>
                  <ArrowRight className="h-4 w-4 sm:h-6 sm:w-6 ml-2 sm:ml-3" />
                </>
              )}
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
