/**
 * 🎯 HOOK UNIFICADO PARA METADADOS
 * 
 * Este hook substitui todos os hooks duplicados:
 * - useStaticLocations
 * - useStaticSpecialties  
 * - useStaticThemes
 * - useStaticFocuses
 * 
 * Usa useQuestionMetadata como fonte única de verdade.
 */
import { useMemo } from 'react';
import { useQuestionMetadata } from './useQuestionMetadata';

/**
 * Hook para acessar apenas locations dos metadados
 * Substitui useStaticLocations
 */
export const useUnifiedLocations = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  return {
    data: metadata?.locations || [],
    isLoading,
    error
  };
};

/**
 * Hook para acessar apenas specialties dos metadados
 * Substitui useStaticSpecialties
 */
export const useUnifiedSpecialties = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  return {
    data: metadata?.specialties || [],
    isLoading,
    error
  };
};

/**
 * Hook para acessar apenas themes dos metadados
 * Substitui useStaticThemes
 */
export const useUnifiedThemes = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  return {
    data: metadata?.themes || [],
    isLoading,
    error
  };
};

/**
 * Hook para acessar apenas focuses dos metadados
 * Substitui useStaticFocuses
 */
export const useUnifiedFocuses = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  return {
    data: metadata?.focuses || [],
    isLoading,
    error
  };
};

/**
 * Hook para acessar apenas years dos metadados
 */
export const useUnifiedYears = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  return {
    data: metadata?.years || [],
    isLoading,
    error
  };
};

/**
 * Hook para acessar question formats estáticos
 */
export const useUnifiedQuestionFormats = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  return {
    data: metadata?.question_formats || [],
    isLoading,
    error
  };
};

/**
 * Hook para acessar question types estáticos
 */
export const useUnifiedQuestionTypes = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  return {
    data: metadata?.question_types || [],
    isLoading,
    error
  };
};

/**
 * Hook para resolver nomes de categorias a partir de IDs
 * Substitui useCategoryResolver
 */
export const useUnifiedCategoryResolver = () => {
  const { data: metadata, isLoading } = useQuestionMetadata();
  
  const resolver = useMemo(() => {
    if (!metadata) return null;
    
    // Criar mapas para resolução rápida
    const specialtyMap = new Map(metadata.specialties.map(s => [s.id, s]));
    const themeMap = new Map(metadata.themes.map(t => [t.id, t]));
    const focusMap = new Map(metadata.focuses.map(f => [f.id, f]));
    const locationMap = new Map(metadata.locations.map(l => [l.id, l]));
    
    return {
      getSpecialty: (id: string) => specialtyMap.get(id),
      getTheme: (id: string) => themeMap.get(id),
      getFocus: (id: string) => focusMap.get(id),
      getLocation: (id: string) => locationMap.get(id),
      
      // Funções de conveniência
      getSpecialtyName: (id: string) => specialtyMap.get(id)?.name || 'Desconhecido',
      getThemeName: (id: string) => themeMap.get(id)?.name || 'Desconhecido',
      getFocusName: (id: string) => focusMap.get(id)?.name || 'Desconhecido',
      getLocationName: (id: string) => locationMap.get(id)?.name || 'Desconhecido',
    };
  }, [metadata]);
  
  return {
    resolver,
    isLoading,
    isReady: !isLoading && !!resolver
  };
};

/**
 * Hook para acessar todos os metadados organizados
 * Para casos que precisam de múltiplos tipos
 */
export const useUnifiedAllMetadata = () => {
  const { data: metadata, isLoading, error } = useQuestionMetadata();
  
  const organized = useMemo(() => {
    if (!metadata) return null;
    
    return {
      categories: {
        specialties: metadata.specialties,
        themes: metadata.themes,
        focuses: metadata.focuses,
      },
      filters: {
        locations: metadata.locations,
        years: metadata.years,
        question_formats: metadata.question_formats || [],
        question_types: metadata.question_types || [],
      },
      // Hierarquia organizada para facilitar navegação
      hierarchy: organizeHierarchy(metadata)
    };
  }, [metadata]);
  
  return {
    data: organized,
    isLoading,
    error,
    isReady: !isLoading && !!organized
  };
};

/**
 * Função auxiliar para organizar hierarquia
 */
function organizeHierarchy(metadata: any) {
  const specialtiesWithChildren = metadata.specialties.map((specialty: any) => ({
    ...specialty,
    themes: metadata.themes
      .filter((theme: any) => theme.specialty_id === specialty.id)
      .map((theme: any) => ({
        ...theme,
        focuses: metadata.focuses.filter((focus: any) => focus.theme_id === theme.id)
      }))
  }));
  
  return specialtiesWithChildren;
}
