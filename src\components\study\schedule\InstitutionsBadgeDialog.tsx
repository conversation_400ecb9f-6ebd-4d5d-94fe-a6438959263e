import React from 'react';
import { Di<PERSON>, Di<PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Building2, TrendingUp, Info } from 'lucide-react';

interface Institution {
  id: string;
  name: string;
  relevance: number;
  percentage: string;
}

interface InstitutionsBadgeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  focus: string;
  institutions: Institution[];
  focusPrevalence?: string;
}

export const InstitutionsBadgeDialog: React.FC<InstitutionsBadgeDialogProps> = ({
  open,
  onOpenChange,
  focus,
  institutions,
  focusPrevalence
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-orange-100">
              <Building2 className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <DialogTitle className="text-lg">Instituições que Contemplam este Foco</DialogTitle>
              <p className="text-sm text-gray-600 mt-1">
                Veja quais das suas instituições de preferência abordam este tópico
              </p>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Informações do Foco */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-semibold text-blue-800">Foco de Estudo</h4>
                  <p className="text-sm text-blue-700 break-words">
                    <strong>{focus}</strong>
                  </p>
                  {focusPrevalence && (
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4 text-blue-600" />
                      <span className="text-xs text-blue-600 font-medium">
                        Prevalência geral: {focusPrevalence}%
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Lista de Instituições */}
          {institutions.length > 0 ? (
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-800 flex items-center gap-2">
                <Building2 className="w-4 h-4" />
                Suas Instituições ({institutions.length})
              </h4>
              
              <div className="space-y-3">
                {institutions.map((institution, index) => (
                  <Card key={institution.id} className="border-gray-200 hover:border-orange-300 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-bold text-orange-600">
                                {index + 1}
                              </span>
                            </div>
                            <h5 className="font-semibold text-gray-800">
                              {institution.name}
                            </h5>
                          </div>
                          
                          <div className="ml-11 space-y-2">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                                {institution.percentage}% das questões
                              </Badge>
                              <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                                {institution.relevance} questões
                              </Badge>
                            </div>
                            
                            {/* Barra de relevância */}
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-gradient-to-r from-orange-400 to-orange-600 h-2 rounded-full transition-all duration-300"
                                style={{ 
                                  width: `${Math.min(100, parseFloat(institution.percentage) * 10)}%` 
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <Card className="border-gray-200 bg-gray-50">
              <CardContent className="p-6 text-center">
                <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <h4 className="font-semibold text-gray-600 mb-2">
                  Nenhuma Instituição Encontrada
                </h4>
                <p className="text-sm text-gray-500">
                  Este foco não foi encontrado nas suas instituições de preferência.
                </p>
              </CardContent>
            </Card>
          )}

          {/* Informação Adicional */}
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <TrendingUp className="w-5 h-5 text-green-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <h4 className="font-semibold text-green-800">Por que isso é importante?</h4>
                  <p className="text-sm text-green-700">
                    Estudar focos que aparecem com maior frequência nas suas instituições de preferência 
                    aumenta suas chances de sucesso. Os percentuais mostram a relevância de cada tópico 
                    baseado no histórico de questões.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
};
