import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { BrainCircuit, CheckCircle, Clock, BookOpen, Target, Calendar, ChevronDown, ChevronUp, Users, Lightbulb, Focus, AlertTriangle, Sparkles, Heart } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScheduleForm } from './sections/ScheduleForm';
import { useForm } from 'react-hook-form';
import type { AIScheduleFormData } from './types';
import { validateForm } from './utils/validation';
import { toast } from 'sonner';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import { useLocation } from 'react-router-dom';

interface AIScheduleDialogSimpleProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: AIScheduleFormData) => void;
  isLoading: boolean;
  isComplete?: boolean;
  generationStats?: any;
  existingWeeks?: number[];
}

type DialogMode = 'form' | 'progress' | 'success';

export const AIScheduleDialogSimple: React.FC<AIScheduleDialogSimpleProps> = ({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  isComplete = false,
  generationStats,
  existingWeeks = []
}) => {
  const location = useLocation();
  const [dialogMode, setDialogMode] = useState<DialogMode>('form');
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState('');
  const [customWeeks, setCustomWeeks] = useState(false);

  // ✅ NOVOS ESTADOS: Validação
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // ✅ NOVO: Acessar preferências do usuário
  const { preferences } = useStudyPreferences();

  // ✅ DETECTAR MODO TUTORIAL GUIADO
  const urlParams = new URLSearchParams(location.search);
  const isGuided = urlParams.get('guided') === 'true';
  const fromTutorial = urlParams.get('from') === 'tutorial';



  // Estados para controlar seções colapsáveis
  const [showSpecialties, setShowSpecialties] = useState(true);
  const [showThemes, setShowThemes] = useState(false);
  const [showFocuses, setShowFocuses] = useState(false);

  // Ref para controlar o progresso simulado
  const progressIntervalRef = React.useRef<NodeJS.Timeout | null>(null);

  const form = useForm<AIScheduleFormData>({
    defaultValues: {
      scheduleOption: 'new',
      weeksCount: 1,
      topicDuration: '30',
      generationMode: 'institution_based', // ✅ CORREÇÃO: Definir modo padrão
      institutionIds: [],
      startYear: undefined,
      endYear: undefined,
      availableDays: {
        'Domingo': { enabled: false, periods: [{ startTime: '', endTime: '' }] },
        'Segunda-feira': { enabled: true, periods: [{ startTime: '08:00', endTime: '12:00' }] },
        'Terça-feira': { enabled: false, periods: [{ startTime: '', endTime: '' }] },
        'Quarta-feira': { enabled: true, periods: [{ startTime: '08:00', endTime: '12:00' }] },
        'Quinta-feira': { enabled: false, periods: [{ startTime: '', endTime: '' }] },
        'Sexta-feira': { enabled: true, periods: [{ startTime: '08:00', endTime: '12:00' }] },
        'Sábado': { enabled: false, periods: [{ startTime: '', endTime: '' }] }
      }
    }
  });

  // ✅ SISTEMA SIMPLES: Gerenciar estados do dialog
  useEffect(() => {
    if (isComplete && dialogMode !== 'success') {
      // Parar progresso simulado
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }

      setDialogMode('success');
      setProgress(100);
      setProgressMessage('Cronograma concluído!');
    } else if (isLoading && dialogMode !== 'progress') {
      setDialogMode('progress');
      setProgress(10);
      setProgressMessage('Iniciando geração...');
      // Iniciar progresso simulado
      startProgressSimulation();
    } else if (!isLoading && !isComplete && dialogMode !== 'form') {
      // Parar progresso simulado
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      setDialogMode('form');
      setProgress(0);
      setProgressMessage('');
    }
  }, [isLoading, isComplete, dialogMode]);

  // Reset quando dialog fecha
  useEffect(() => {
    if (!open) {
      // Parar progresso simulado
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      setDialogMode('form');
      setProgress(0);
      setProgressMessage('');
    }
  }, [open]);

  // Função para simular progresso
  const startProgressSimulation = () => {
    // Limpar qualquer intervalo anterior
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }

    let currentProgress = 10;
    const messages = [
      'Analisando especialidades...',
      'Selecionando temas...',
      'Organizando focos...',
      'Criando cronograma...',
      'Distribuindo tópicos...',
      'Finalizando geração...'
    ];
    let messageIndex = 0;

    progressIntervalRef.current = setInterval(() => {
      if (currentProgress < 90) {
        // Incremento variável para parecer mais natural
        const increment = Math.random() * 15 + 5; // Entre 5 e 20
        currentProgress = Math.min(currentProgress + increment, 90);
        setProgress(currentProgress);
        // Atualizar mensagem ocasionalmente
        if (Math.random() > 0.7 && messageIndex < messages.length - 1) {
          messageIndex++;
          setProgressMessage(messages[messageIndex]);
        }
      }
    }, 800); // A cada 800ms
  };

  // ✅ NOVA VALIDAÇÃO EM TEMPO REAL
  useEffect(() => {
    // Validação inicial
    const initialValidation = validateForm(form.getValues(), preferences);
    setValidationErrors(initialValidation.errors);

    // Validação contínua
    const subscription = form.watch((formData) => {
      const validation = validateForm(formData, preferences);
      setValidationErrors(validation.errors);
    });

    return () => subscription.unsubscribe();
  }, [form, preferences]);

  // Limpar intervalo quando componente desmonta
  React.useEffect(() => {
    return () => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  const handleSubmit = (data: AIScheduleFormData) => {
    // ✅ VALIDAÇÃO RIGOROSA: Verificar antes de prosseguir
    const validation = validateForm(data, preferences);

    if (!validation.isValid) {
      // Mostrar primeiro erro como toast
      if (validation.errors.length > 0) {
        toast.error(validation.errors[0]);
      }

      // Atualizar erros para mostrar na UI
      setValidationErrors(validation.errors);
      return;
    }

    onSubmit(data);
  };

  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false);
    }
  };

  const renderContent = () => {
    switch (dialogMode) {
      case 'success':
        return (
          <div className="space-y-6">
            {/* Success Header */}
            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                  isGuided && fromTutorial
                    ? 'bg-gradient-to-r from-purple-100 to-indigo-100 border-2 border-purple-300'
                    : 'bg-green-100'
                }`}>
                  {isGuided && fromTutorial ? (
                    <Sparkles className="w-8 h-8 text-purple-600" />
                  ) : (
                    <CheckCircle className="w-8 h-8 text-green-600" />
                  )}
                </div>
              </div>

              {isGuided && fromTutorial ? (
                <>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                    🎉 Parabéns! Seu Cronograma Está Pronto!
                  </h2>
                  <p className="text-gray-600 mb-4">
                    Você acabou de criar seu primeiro cronograma personalizado!
                  </p>
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border-2 border-purple-200 rounded-lg p-4 mb-6">
                    <h3 className="font-bold text-purple-800 mb-2">🚀 O que você conseguiu:</h3>
                    <div className="text-sm text-purple-700 space-y-1 mb-4">
                      <div>✅ Cronograma baseado nas suas <strong>instituições alvo</strong></div>
                      <div>✅ Horários adaptados à sua <strong>disponibilidade</strong></div>
                      <div>✅ Tópicos organizados por <strong>prioridade</strong></div>
                      <div>✅ Sistema de <strong>revisões automáticas</strong></div>
                    </div>

                    <div className="bg-white border border-purple-300 rounded-lg p-3">
                      <h4 className="font-bold text-purple-800 mb-2 flex items-center gap-2">
                        <BookOpen className="h-4 w-4" />
                        📚 Como usar no dia a dia:
                      </h4>
                      <div className="text-sm text-purple-700 space-y-1">
                        <div>• <strong>Na página inicial</strong>: Temas aparecerão nos dias programados</div>
                        <div>• <strong>Um clique</strong>: Pratique questões ou marque como estudado</div>
                        <div>• <strong>Revisões automáticas</strong>: Sistema marca próxima revisão sozinho</div>
                        <div>• <strong>Sem complicação</strong>: Apenas siga o cronograma criado!</div>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <h2 className="text-2xl font-bold text-green-600 mb-2">
                    Cronograma Gerado com Sucesso!
                  </h2>
                  <p className="text-gray-600 mb-6">
                    Seu cronograma de estudos foi criado e está pronto para uso.
                  </p>
                </>
              )}
            </div>

            {/* Main Statistics */}
            {generationStats && (
              <div className="grid grid-cols-2 gap-3 sm:gap-4 mb-6">
                <Card>
                  <CardContent className="p-3 sm:p-4 text-center">
                    <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xl sm:text-2xl font-bold text-blue-600">{generationStats.totalTopics}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Tópicos Criados</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-3 sm:p-4 text-center">
                    <Calendar className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xl sm:text-2xl font-bold text-purple-600">{generationStats.totalWeeks}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Semanas Geradas</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-3 sm:p-4 text-center">
                    <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-xl sm:text-2xl font-bold text-orange-600">{generationStats.generationTime.toFixed(1)}s</div>
                    <div className="text-xs sm:text-sm text-gray-600">Tempo de Geração</div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-3 sm:p-4 text-center">
                    <Target className="w-5 h-5 sm:w-6 sm:h-6 text-green-600 mx-auto mb-1 sm:mb-2" />
                    <div className="text-lg sm:text-2xl font-bold text-green-600 capitalize">{generationStats.domain}</div>
                    <div className="text-xs sm:text-sm text-gray-600">Domínio</div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* ✅ NOVO: Aviso de repetição de focos */}
            {generationStats?.focusRepetitionWarning && (
              <Card className="border-orange-200 bg-orange-50">
                <CardContent className="p-4">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="w-5 h-5 text-orange-600 mt-0.5 flex-shrink-0" />
                    <div className="space-y-2">
                      <h4 className="font-semibold text-orange-800">Aviso: Repetição de Focos</h4>
                      <p className="text-sm text-orange-700">
                        Devido à quantidade de cronograma solicitada ({generationStats.totalSlotsCount} slots)
                        e focos disponíveis ({generationStats.availableFocusesCount} únicos),
                        alguns focos serão repetidos para preencher todo o cronograma.
                      </p>
                      <div className="text-xs text-orange-600 bg-orange-100 p-2 rounded">
                        💡 <strong>Dica:</strong> Para maior variedade, considere reduzir o número de semanas
                        ou períodos de estudo, ou adicionar mais instituições às suas preferências.
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Detailed Information */}
            {generationStats && (
              <div className="space-y-4">
                {/* Especialidades */}
                {generationStats.specialties && generationStats.specialties.length > 0 && (
                  <Collapsible open={showSpecialties} onOpenChange={setShowSpecialties}>
                    <Card>
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors p-4 sm:p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 sm:gap-3">
                              <Users className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600" />
                              <CardTitle className="text-base sm:text-lg">
                                Especialidades ({generationStats.specialties.length})
                              </CardTitle>
                            </div>
                            {showSpecialties ? <ChevronUp className="w-4 h-4 sm:w-5 sm:h-5" /> : <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5" />}
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <CardContent className="pt-0 px-4 sm:px-6">
                          <div className="flex flex-wrap gap-1.5 sm:gap-2">
                            {generationStats.specialties.map((specialty: string, index: number) => (
                              <Badge key={index} variant="secondary" className="bg-blue-100 text-blue-800 text-xs sm:text-sm">
                                {specialty}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Card>
                  </Collapsible>
                )}

                {/* Temas */}
                {generationStats.themes && generationStats.themes.length > 0 && (
                  <Collapsible open={showThemes} onOpenChange={setShowThemes}>
                    <Card>
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors p-4 sm:p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 sm:gap-3">
                              <Lightbulb className="w-4 h-4 sm:w-5 sm:h-5 text-green-600" />
                              <CardTitle className="text-base sm:text-lg">
                                Temas ({generationStats.themes.length})
                              </CardTitle>
                            </div>
                            {showThemes ? <ChevronUp className="w-4 h-4 sm:w-5 sm:h-5" /> : <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5" />}
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <CardContent className="pt-0 px-4 sm:px-6">
                          <div className="flex flex-wrap gap-1.5 sm:gap-2">
                            {generationStats.themes.map((theme: string, index: number) => (
                              <Badge key={index} variant="secondary" className="bg-green-100 text-green-800 text-xs sm:text-sm">
                                {theme}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Card>
                  </Collapsible>
                )}

                {/* Focos */}
                {generationStats.focuses && generationStats.focuses.length > 0 && (
                  <Collapsible open={showFocuses} onOpenChange={setShowFocuses}>
                    <Card>
                      <CollapsibleTrigger asChild>
                        <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors p-4 sm:p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 sm:gap-3">
                              <Focus className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600" />
                              <CardTitle className="text-base sm:text-lg">
                                Focos ({generationStats.focuses.length})
                              </CardTitle>
                            </div>
                            {showFocuses ? <ChevronUp className="w-4 h-4 sm:w-5 sm:h-5" /> : <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5" />}
                          </div>
                        </CardHeader>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <CardContent className="pt-0 px-4 sm:px-6">
                          <div className="flex flex-wrap gap-1.5 sm:gap-2 max-h-32 sm:max-h-40 overflow-y-auto pr-2">
                            {generationStats.focuses.map((focus: string, index: number) => (
                              <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-800 text-xs">
                                {focus}
                              </Badge>
                            ))}
                          </div>
                        </CardContent>
                      </CollapsibleContent>
                    </Card>
                  </Collapsible>
                )}
              </div>
            )}

            {/* Action Button */}
            <div className="pt-4 border-t">
              {isGuided && fromTutorial ? (
                <div className="space-y-3">
                  <Button
                    onClick={handleClose}
                    className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:opacity-90 text-white font-bold py-4 text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                    size="lg"
                  >
                    <Sparkles className="w-5 h-5 mr-2" />
                    🎯 Ver Meu Cronograma Personalizado!
                  </Button>
                  <div className="text-center space-y-2">
                    <p className="text-sm text-gray-600 font-medium">
                      🎉 <strong>Bem-vindo ao MedEvo!</strong> Agora você pode estudar de forma organizada e eficiente!
                    </p>
                    <p className="text-xs text-gray-500">
                      Seus temas aparecerão na página inicial nos dias programados. Bons estudos! 📚✨
                    </p>
                  </div>
                </div>
              ) : (
                <Button
                  onClick={handleClose}
                  className="w-full bg-green-600 hover:bg-green-700 text-white text-sm sm:text-base"
                  size="lg"
                >
                  <CheckCircle className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  <span className="hidden sm:inline">Fechar e Ver Cronograma</span>
                  <span className="sm:hidden">Ver Cronograma</span>
                </Button>
              )}
            </div>
          </div>
        );

      case 'progress':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Gerando Cronograma</h3>
              <p className="text-gray-600">Por favor, aguarde enquanto a IA cria seu cronograma personalizado...</p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{progressMessage}</span>
                <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>

            <div className="text-center text-sm text-gray-500">
              ⚠️ Não feche esta janela durante a geração
            </div>
          </div>
        );

      default:
        return (
          <ScheduleForm
            form={form}
            onSubmit={handleSubmit}
            customWeeks={customWeeks}
            setCustomWeeks={setCustomWeeks}
            existingWeeks={existingWeeks}
            isLoading={isLoading}
            validationErrors={validationErrors}
          />
        );
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl w-[95vw] sm:w-full max-h-[90dvh] overflow-y-auto p-4 sm:p-6">
        {/* ✅ LIMPEZA: Header especial do tutorial removido - agora usamos tutorial na página */}

        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-blue-100">
              <BrainCircuit className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <DialogTitle>Personalizar com IA</DialogTitle>
              <DialogDescription>
                Configure suas preferências e a IA criará um cronograma personalizado
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {renderContent()}
      </DialogContent>
    </Dialog>
  );
};
