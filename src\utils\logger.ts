// Sistema de logging inteligente para Dr. Will
// Evita spam de logs e mantém apenas os mais importantes

interface LogEntry {
  message: string;
  timestamp: number;
  level: 'info' | 'warn' | 'error';
}

class SmartLogger {
  private logs: LogEntry[] = [];
  private maxLogs = 50; // Manter apenas os últimos 50 logs
  private isDevelopment = import.meta.env.DEV;

  // Throttling para evitar spam
  private lastLogTime = new Map<string, number>();
  private throttleMs = 1000; // 1 segundo entre logs similares

  private shouldLog(message: string): boolean {
    // ✅ SISTEMA INTELIGENTE: Apenas logs críticos em produção
    if (!this.isDevelopment) {
      return message.includes('ERROR') ||
             message.includes('CRITICAL') ||
             message.includes('FAILED') ||
             message.includes('❌') ||
             message.includes('🚨');
    }

    // Em desenvolvimento, permitir mais logs mas filtrar spam
    return !message.includes('refresh') &&
           !message.includes('loading') &&
           !message.includes('update') &&
           !message.includes('render');
  }

  private addLog(message: string, level: 'info' | 'warn' | 'error') {
    this.logs.push({
      message,
      timestamp: Date.now(),
      level
    });

    // Manter apenas os logs mais recentes
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }
  }

  info(message: string) {
    if (this.shouldLog(message)) {
      this.addLog(message, 'info');
    }
  }

  warn(message: string) {
    if (this.shouldLog(message)) {
      console.warn(`⚠️ ${message}`);
      this.addLog(message, 'warn');
    }
  }

  error(message: string, error?: any) {
    // Erros sempre são logados
    console.error(`❌ ${message}`, error || '');
    this.addLog(message, 'error');
  }

  // ✅ NOVOS MÉTODOS INTELIGENTES
  dev(message: string, ...args: any[]) {
    if (this.isDevelopment) {
      console.log(`🔧 [DEV] ${message}`, ...args);
    }
  }

  debug(message: string, ...args: any[]) {
    if (this.isDevelopment && this.shouldLog(message)) {
      console.log(`🐛 [DEBUG] ${message}`, ...args);
      this.addLog(message, 'info');
    }
  }

  success(message: string) {
    if (this.isDevelopment) {
      console.log(`✅ ${message}`);
    }
  }

  loading(message: string) {
    // Silencioso - loading states são muito verbosos
  }

  // Limpar logs antigos
  clearOldLogs() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    this.logs = this.logs.filter(log => log.timestamp > oneHourAgo);
    this.lastLogTime.clear();
  }

  // Obter logs para debug
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  // Estatísticas de logs
  getStats() {
    const now = Date.now();
    const lastMinute = this.logs.filter(log => now - log.timestamp < 60000);
    const errors = this.logs.filter(log => log.level === 'error');
    
    return {
      totalLogs: this.logs.length,
      logsLastMinute: lastMinute.length,
      totalErrors: errors.length,
      lastError: errors[errors.length - 1]?.message || 'None'
    };
  }
}

// Instância global
export const logger = new SmartLogger();

// Limpar logs antigos a cada 30 minutos
setInterval(() => {
  logger.clearOldLogs();
}, 30 * 60 * 1000);

// Logs específicos para Dr. Will
export const drWillLogger = {
  threadLoaded: (threadId: string, messageCount: number) => {
    // ✅ LIMPEZA: Log removido - thread loaded
  },

  threadCreated: (threadId: string, title: string) => {
    // ✅ LIMPEZA: Log removido - thread created
  },

  messageSaved: (messageId: string, isUser: boolean) => {
    // ✅ LIMPEZA: Log removido - message saved
  },

  contextLoaded: (threadId: string, historyLength: number) =>
    logger.info(`[CONTEXT] Loaded ${historyLength} messages for AI context from thread ${threadId.slice(0, 8)}...`),

  cacheHit: (key: string) =>
    logger.info(`[CACHE] Cache hit for ${key}`),

  cacheMiss: (key: string) =>
    logger.info(`[CACHE] Cache miss for ${key}, loading from database`),

  error: (operation: string, error: any) =>
    logger.error(`[DR_WILL] Error in ${operation}`, error),

  performance: (operation: string, duration: number) => {
    if (duration > 1000) {
      logger.warn(`[PERFORMANCE] ${operation} took ${duration}ms (slow)`);
    } else {
      logger.info(`[PERFORMANCE] ${operation} completed in ${duration}ms`);
    }
  },

  // Métodos que estavam faltando
  success: (message: string) => logger.success(message),
  info: (message: string) => logger.info(message),
  warn: (message: string) => logger.warn(message)
};
