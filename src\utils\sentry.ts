import * as Sentry from '@sentry/react';
import { BrowserTracing } from '@sentry/tracing';

// Configuração do Sentry
export const initSentry = () => {
  // Verificar se DSN é válido antes de inicializar
  const sentryDsn = import.meta.env.VITE_SENTRY_DSN;
  const isValidDsn = sentryDsn && !sentryDsn.includes('your-sentry-dsn') && sentryDsn.startsWith('https://');

  // Só inicializar em produção ou quando explicitamente habilitado E com DSN válido
  if ((import.meta.env.PROD || import.meta.env.VITE_ENABLE_SENTRY === 'true') && isValidDsn) {
    Sentry.init({
      dsn: sentryDsn,
      environment: import.meta.env.MODE,

      // Performance Monitoring
      integrations: [
        new BrowserTracing({
          // Capturar navegação entre rotas automaticamente
        }),
      ],

      // Performance
      tracesSampleRate: import.meta.env.PROD ? 0.1 : 1.0, // 10% em produção, 100% em dev

      // Session Replay (opcional)
      replaysSessionSampleRate: 0.1, // 10% das sessões
      replaysOnErrorSampleRate: 1.0, // 100% quando há erro

      // Filtros para reduzir ruído
      beforeSend(event, hint) {
        // Filtrar erros conhecidos e irrelevantes
        const error = hint.originalException;

        // Ignorar erros de extensões do browser
        if (error && error.toString().includes('Extension')) {
          return null;
        }

        // Ignorar erros de rede temporários
        if (error && error.toString().includes('NetworkError')) {
          return null;
        }

        // Ignorar erros de CORS de terceiros
        if (error && error.toString().includes('CORS')) {
          return null;
        }

        return event;
      },

      // Tags globais
      initialScope: {
        tags: {
          component: 'studywise-frontend',
          version: import.meta.env.VITE_APP_VERSION || '1.0.0',
        },
      },
    });

    // Sentry initialized for error tracking
  } else {
    // Sentry disabled: invalid DSN or development mode
    // ✅ LIMPEZA: Warning removido - configuração de Sentry
  }
};

// Utilitários para logging estruturado
export const logError = (error: Error, context?: Record<string, any>) => {
  if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_SENTRY === 'true') {
    Sentry.withScope((scope) => {
      if (context) {
        Object.keys(context).forEach(key => {
          scope.setContext(key, context[key]);
        });
      }
      Sentry.captureException(error);
    });
  }
};

export const logMessage = (message: string, level: 'info' | 'warning' | 'error' = 'info', extra?: Record<string, any>) => {
  if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_SENTRY === 'true') {
    Sentry.withScope((scope) => {
      if (extra) {
        Object.keys(extra).forEach(key => {
          scope.setContext(key, extra[key]);
        });
      }
      Sentry.captureMessage(message, level);
    });
  }
};

// Wrapper para capturar erros de performance
export const measurePerformance = async <T>(
  name: string,
  operation: () => Promise<T>,
  context?: Record<string, any>
): Promise<T> => {
  const startTime = performance.now();

  try {
    const result = await operation();
    const duration = performance.now() - startTime;

    // Log performance se for muito lento
    if (duration > 1000) { // > 1 segundo
      logMessage(`Slow operation: ${name}`, 'warning', {
        duration: `${duration.toFixed(2)}ms`,
        ...context,
      });
    }

    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    logError(error as Error, {
      operation: name,
      duration: `${duration.toFixed(2)}ms`,
      ...context,
    });
    throw error;
  }
};

// Hook para capturar erros de componentes React
export const useSentryErrorBoundary = () => {
  return Sentry.withErrorBoundary;
};

// Configurar contexto do usuário
export const setUserContext = (user: {
  id: string;
  email?: string;
  formation_area?: string;
  is_student?: boolean;
}) => {
  if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_SENTRY === 'true') {
    Sentry.setUser({
      id: user.id,
      email: user.email,
      formation_area: user.formation_area,
      is_student: user.is_student,
    });
  }
};

// Limpar contexto do usuário (logout)
export const clearUserContext = () => {
  if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_SENTRY === 'true') {
    Sentry.setUser(null);
  }
};

// Capturar breadcrumbs customizados
export const addBreadcrumb = (message: string, category: string, data?: Record<string, any>) => {
  if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_SENTRY === 'true') {
    Sentry.addBreadcrumb({
      message,
      category,
      data,
      level: 'info',
      timestamp: Date.now() / 1000,
    });
  }
};

// Métricas de negócio customizadas
export const trackBusinessMetric = (metric: string, value: number, tags?: Record<string, string>) => {
  if (import.meta.env.PROD || import.meta.env.VITE_ENABLE_SENTRY === 'true') {
    Sentry.withScope((scope) => {
      if (tags) {
        Object.keys(tags).forEach(key => {
          scope.setTag(key, tags[key]);
        });
      }
      scope.setTag('metric_type', 'business');
      Sentry.captureMessage(`${metric}: ${value}`, 'info');
    });
  }
};

// Wrapper para HOC do Sentry
export const withSentryErrorBoundary = Sentry.withErrorBoundary;

// Re-exportar componentes úteis do Sentry
export const SentryErrorBoundary = Sentry.ErrorBoundary;
export const SentryProfiler = Sentry.Profiler;
