import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Sparkles } from "lucide-react";

interface SubmitButtonProps {
  isLoading: boolean;
  hasValidationErrors?: boolean;
}

export const SubmitButton = ({ isLoading, hasValidationErrors = false }: SubmitButtonProps) => {
  return (
    <div className="pt-4 pb-2">
      <Button
        type="submit"
        className={`w-full h-12 sm:h-14 text-base sm:text-lg font-bold text-white shadow-lg border-b-2 rounded-xl transition-all ${
          hasValidationErrors
            ? 'bg-gray-400 border-gray-600 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 border-blue-800'
        }`}
        disabled={isLoading || hasValidationErrors}
      >
        {isLoading ? (
          <>
            <Loader2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 animate-spin" />
            <span className="hidden sm:inline">Gerando cronograma...</span>
            <span className="sm:hidden">Gerando...</span>
          </>
        ) : hasValidationErrors ? (
          <>
            <span className="hidden sm:inline">Preencha todos os campos obrigatórios</span>
            <span className="sm:hidden">Campos obrigatórios</span>
          </>
        ) : (
          <>
            <Sparkles className="w-5 h-5 sm:w-6 sm:h-6 mr-2" />
            <span className="hidden sm:inline">Gerar cronograma com IA</span>
            <span className="sm:hidden">Gerar com IA</span>
          </>
        )}
      </Button>
    </div>
  );
};
