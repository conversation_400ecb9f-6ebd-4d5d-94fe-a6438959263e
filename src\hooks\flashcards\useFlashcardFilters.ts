import { useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { SelectedFilters } from "@/types/flashcard";
import { selectedFiltersToJson } from "@/components/filters/types";

const BATCH_SIZE = 100;

interface FetchCardsResponse {
  cards: Array<{
    id: string;
    back: string;
    back_image: string;
    created_at: string;
    current_state: string;
    extrafocus_id: string;
    focus_id: string;
    front: string;
    front_image: string;
    specialty_id: string;
    theme_id: string;
    updated_at: string;
    user_id: string;
    flashcards_session_cards: Array<any>;
    flashcards_reviews: Array<any>;
  }>;
  total: number;
}

export const useFlashcardFilters = () => {
  const processBatch = async (cards: string[], currentState: string) => {
    try {
      const { error } = await supabase.rpc('update_cards_state', {
        p_card_ids: cards,
        p_new_state: currentState
      });

      if (error) throw error;
    } catch (error) {
      throw error;
    }
  };

  const updateCardsState = async (cards: string[], newState: string) => {
    try {
      for (let i = 0; i < cards.length; i += BATCH_SIZE) {
        const batch = cards.slice(i, i + BATCH_SIZE);
        await processBatch(batch, newState);
      }
    } catch (error) {
      throw error;
    }
  };

  const fetchFilteredCards = useCallback(async (
    user_id: string,
    filters: SelectedFilters,
    page = 1,
    pageSize = 100
  ): Promise<FetchCardsResponse> => {
    try {
      // Otimizado: Usar função RPC para buscar cards filtrados de forma eficiente
      const specialties = filters.specialties.length > 0 ? filters.specialties : null;
      const themes = filters.themes.length > 0 ? filters.themes : null;
      const focuses = filters.focuses.length > 0 ? filters.focuses : null;
      const extrafocuses = filters.extrafocuses.length > 0 ? filters.extrafocuses : null;

      const { data: cardIds, error: rpcError } = await supabase.rpc('get_filtered_flashcards', {
        p_user_id: user_id,
        p_specialties: specialties,
        p_themes: themes,
        p_focuses: focuses,
        p_extrafocuses: extrafocuses,
        p_limit: pageSize
      });

      if (rpcError) throw rpcError;

      // Buscar dados completos dos cards encontrados
      let query = supabase
        .from('flashcards_cards')
        .select('*', { count: 'exact' })
        .in('id', cardIds?.map(c => c.card_id) || []);

      // Filtros removidos - a função RPC já fez a filtragem
      const { data: cards, error: queryError, count } = await query;

      if (queryError) throw queryError;

      return {
        cards: cards || [],
        total: cardIds?.length || 0
      };
    } catch (error) {
      throw error;
    }
  }, []);

  const createSession = useCallback(async (
    user_id: string,
    filters: SelectedFilters,
    cards: string[]
  ) => {
    try {
      const { data: session, error: sessionError } = await supabase
        .from('flashcards_sessions')
        .insert({
          user_id,
          filters: selectedFiltersToJson(filters),
          status: 'in_progress',
          cards,
          total_cards: cards.length
        })
        .select()
        .single();

      if (sessionError) throw sessionError;

      // Atualizar estado dos cartões de forma síncrona
      try {
        await updateCardsState(cards, 'reviewing');
      } catch (error) {
        toast.error("Erro ao atualizar cartões", {
          description: "A sessão foi criada mas houve um erro ao atualizar os cartões"
        });
        // Não fazemos throw aqui para não impedir o retorno da sessão
      }

      return session;
    } catch (error: any) {
      throw error;
    }
  }, []);

  return {
    createSession,
    fetchFilteredCards,
    updateCardsState
  };
};
