
/**
 * 🎯 HOOK OTIMIZADO - FONTE ÚNICA DE METADADOS
 *
 * Este hook é a ÚNICA fonte de metadados para filtros.
 * Elimina requisições duplicadas e centraliza todos os dados.
 *
 * ✅ Dados incluídos:
 * - specialties, themes, focuses (com hierarquia)
 * - locations (exam_locations)
 * - years (com contagens)
 * - question_formats (estáticos)
 * - question_types (estáticos)
 */
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { QuestionMetadata } from '@/types/question';
import { useDomain } from '@/hooks/useDomain';

// Dados estáticos para question_formats e question_types
const STATIC_QUESTION_FORMATS = [
  { id: 'ALTERNATIVAS', name: '📝 Alternativas', description: 'Questões de múltipla escolha' },
  { id: 'VERDADEIRO_FALSO', name: '✅ Verdadeiro ou Falso', description: '<PERSON><PERSON><PERSON> de <PERSON> ou F' },
  { id: 'DISSERTATIVA', name: '📄 Dissertativa', description: 'Questões abertas/texto livre' }
];

const STATIC_QUESTION_TYPES = [
  { id: 'teorica-1', name: 'Teórica I' },
  { id: 'teorica-2', name: 'Teórica II' },
  { id: 'teorico-pratica', name: 'Teórico-Prática' }
];

export const useQuestionMetadata = () => {
  const { domain, isReady } = useDomain();

  return useQuery({
    queryKey: ['unified-question-metadata', domain],
    queryFn: async () => {
  

      // Skip fetching if domain isn't ready
      if (!isReady || !domain) {
        return {
          specialties: [],
          themes: [],
          focuses: [],
          locations: [],
          years: [],
          question_formats: STATIC_QUESTION_FORMATS,
          question_types: STATIC_QUESTION_TYPES
        };
      }

      try {
        // 🎯 ÚNICA REQUISIÇÃO: RPC que retorna TODOS os dados necessários
        const { data, error } = await supabase.rpc('get_questions_metadata', {
          domain_filter: domain
        });

        if (error) {
          console.error('❌ [useQuestionMetadata] Erro no RPC:', error);
          throw error;
        }

        if (!data) {
          console.warn('⚠️ [useQuestionMetadata] RPC retornou dados vazios');
          return {
            specialties: [],
            themes: [],
            focuses: [],
            locations: [],
            years: [],
            question_formats: STATIC_QUESTION_FORMATS,
            question_types: STATIC_QUESTION_TYPES
          };
        }

        // ✅ OTIMIZAÇÃO: Estruturar dados de forma consistente
        const optimizedData = {
          specialties: (data.specialties || []).map(specialty => ({
            id: specialty.id,
            name: specialty.name,
            type: specialty.type || 'specialty',
            count: specialty.count || 0
          })),
          themes: (data.themes || []).map(theme => ({
            id: theme.id,
            name: theme.name,
            type: theme.type || 'theme',
            count: theme.count || 0,
            parent_id: theme.parent_id || theme.specialty_id,
            specialty_id: theme.specialty_id
          })),
          focuses: (data.focuses || []).map(focus => ({
            id: focus.id,
            name: focus.name,
            type: focus.type || 'focus',
            count: focus.count || 0,
            parent_id: focus.parent_id || focus.theme_id,
            theme_id: focus.theme_id
          })),
          locations: (data.locations || []).map(location => ({
            id: location.id,
            name: location.name,
            count: location.count || 0,
            // Manter contadores detalhados para funcionalidades avançadas
            specialty_counts: location.specialty_counts || {},
            theme_counts: location.theme_counts || {},
            focus_counts: location.focus_counts || {},
            year_counts: location.year_counts || {}
          })),
          years: (data.years || []).map(year => ({
            year: year.year,
            count: year.count || 0
          })),
          // 📊 DADOS ESTÁTICOS: Incluir no retorno para consistência
          question_formats: STATIC_QUESTION_FORMATS,
          question_types: STATIC_QUESTION_TYPES
        };



        return optimizedData as QuestionMetadata;
      } catch (error) {
        console.error('❌ [useQuestionMetadata] Erro ao carregar metadados:', error);
        throw error;
      }
    },
    enabled: isReady && !!domain,
    staleTime: 24 * 60 * 60 * 1000, // 24 horas - metadados mudam raramente
    gcTime: 7 * 24 * 60 * 60 * 1000, // 7 dias - manter cache por muito tempo
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchInterval: false,
    retry: 2,
    retryOnMount: false,
    // 🎯 SELECTOR: Garantir formato consistente
    select: (data) => {
      // Validar estrutura dos dados
      if (!data) return data;

      return {
        ...data,
        // Garantir que arrays existem mesmo se vazios
        specialties: data.specialties || [],
        themes: data.themes || [],
        focuses: data.focuses || [],
        locations: data.locations || [],
        years: data.years || [],
        question_formats: data.question_formats || STATIC_QUESTION_FORMATS,
        question_types: data.question_types || STATIC_QUESTION_TYPES
      };
    }
  });
};
