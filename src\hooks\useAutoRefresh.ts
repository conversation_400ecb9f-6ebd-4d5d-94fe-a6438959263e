import { useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface RefreshOptions {
  invalidateQueries?: string[];
  refetchQueries?: string[];
  delay?: number;
}

export const useAutoRefresh = () => {
  const queryClient = useQueryClient();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const triggerRefresh = useCallback(async (options: RefreshOptions = {}) => {
    const {
      invalidateQueries = [],
      refetchQueries = [],
      delay = 100 // Reduzir delay para ser mais responsivo
    } = options;

    // Limpar timeout anterior se existir
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Aguardar um pouco para garantir que a operação foi concluída
    timeoutRef.current = setTimeout(async () => {
      try {
        console.log('🔄 [useAutoRefresh] Triggering refresh...');

        // Invalidar queries específicas APENAS
        for (const queryKey of invalidateQueries) {
          await queryClient.invalidateQueries({ queryKey: [queryKey] });
          console.log(`✅ [useAutoRefresh] Invalidated query: ${queryKey}`);
        }

        // Refetch queries específicas APENAS
        for (const queryKey of refetchQueries) {
          await queryClient.refetchQueries({ queryKey: [queryKey] });
          console.log(`✅ [useAutoRefresh] Refetched query: ${queryKey}`);
        }

        // NÃO invalidar queries padrão automaticamente
      } catch (error) {
        // Error during refresh
      }
    }, delay);
  }, [queryClient]);

  const refreshSchedule = useCallback(() => {
    triggerRefresh({
      invalidateQueries: ['schedule'],
      delay: 50 // Muito mais rápido
    });
  }, [triggerRefresh]);

  const refreshQuestions = useCallback(() => {
    triggerRefresh({
      invalidateQueries: ['questions'],
      delay: 50
    });
  }, [triggerRefresh]);

  const refreshFlashcards = useCallback(() => {
    triggerRefresh({
      invalidateQueries: ['flashcards'],
      delay: 50
    });
  }, [triggerRefresh]);

  const refreshAll = useCallback(() => {
    triggerRefresh({
      invalidateQueries: [
        'schedule',
        'weeklySchedule',
        'studyTopics',
        'questions',
        'userStatistics',
        'progress',
        'flashcards'
      ],
      delay: 500
    });
  }, [triggerRefresh]);

  return {
    triggerRefresh,
    refreshSchedule,
    refreshQuestions,
    refreshFlashcards,
    refreshAll
  };
};

// Hook específico para cronograma
export const useScheduleRefresh = () => {
  const queryClient = useQueryClient();

  const refreshScheduleData = useCallback(() => {
    // Invalidar apenas a query do cronograma atual
    queryClient.invalidateQueries({ queryKey: ['schedule'] });
    // ✅ LIMPEZA: Log removido - refresh de dados rotineiro
  }, [queryClient]);

  return {
    refreshAfterAdd: refreshScheduleData,
    refreshAfterUpdate: refreshScheduleData,
    refreshAfterDelete: refreshScheduleData
  };
};

// Hook para operações em tempo real
export const useRealTimeUpdates = () => {
  const queryClient = useQueryClient();

  const updateQueryData = useCallback((queryKey: string, updater: (oldData: any) => any) => {
    queryClient.setQueryData([queryKey], updater);

  }, [queryClient]);

  const addToQueryData = useCallback((queryKey: string, newItem: any) => {
    queryClient.setQueryData([queryKey], (oldData: any) => {
      if (!oldData) return [newItem];
      if (Array.isArray(oldData)) {
        return [...oldData, newItem];
      }
      return oldData;
    });

  }, [queryClient]);

  const removeFromQueryData = useCallback((queryKey: string, itemId: string) => {
    queryClient.setQueryData([queryKey], (oldData: any) => {
      if (!oldData || !Array.isArray(oldData)) return oldData;
      return oldData.filter((item: any) => item.id !== itemId);
    });
    console.log(`➖ [useRealTimeUpdates] Removed item from query: ${queryKey}`);
  }, [queryClient]);

  const updateItemInQueryData = useCallback((queryKey: string, itemId: string, updates: any) => {
    queryClient.setQueryData([queryKey], (oldData: any) => {
      if (!oldData || !Array.isArray(oldData)) return oldData;
      return oldData.map((item: any) =>
        item.id === itemId ? { ...item, ...updates } : item
      );
    });
    console.log(`🔄 [useRealTimeUpdates] Updated item in query: ${queryKey}`);
  }, [queryClient]);

  return {
    updateQueryData,
    addToQueryData,
    removeFromQueryData,
    updateItemInQueryData
  };
};
