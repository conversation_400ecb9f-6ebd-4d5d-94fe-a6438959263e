
import React from "react";
import { FilterItem } from "@/components/filters/components/FilterItem";
import type { SelectedFilters } from "@/components/filters/types";

interface QuestionTypeFilterSectionProps {
  selectedTypes: string[];
  onToggleType: (type: string) => void;
  searchTerm?: string;
  selectedFilters: SelectedFilters;
  questionCounts?: {
    totalCounts: {[key: string]: number};
    filteredCounts: {[key: string]: number};
  };
}

export const QuestionTypeFilterSection = ({ 
  selectedTypes, 
  onToggleType, 
  searchTerm = "",
  selectedFilters,
  questionCounts
}: QuestionTypeFilterSectionProps) => {
  // ✅ OTIMIZADO: Define question types com contagens dinâmicas
  const questionTypes = [
    {
      id: 'teorica-1',
      name: 'Teórica I',
      count: questionCounts?.totalCounts?.['teorica-1'] || 1000 // Valor padrão para mostrar
    },
    {
      id: 'teorica-2',
      name: 'Teórica II',
      count: questionCounts?.totalCounts?.['teorica-2'] || 1000 // Valor padrão para mostrar
    },
    {
      id: 'teorico-pratica',
      name: 'Teórico-Prática',
      count: questionCounts?.totalCounts?.['teorico-pratica'] || 1000 // Valor padrão para mostrar
    }
  ];

  // Filter question types based on search term
  const filteredTypes = questionTypes.filter(type => 
    type.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Check if any filters are active
  const hasActiveFilters = Object.values(selectedFilters).some(filters => filters.length > 0);

  return (
    <div className="space-y-2">
      {filteredTypes.length === 0 ? (
        <div className="text-center py-4 text-gray-500">
          Nenhum tipo de prova encontrado
        </div>
      ) : (
        <div className="space-y-1">
          {filteredTypes.map(type => {
            const typeItem = {
              id: type.id,
              name: type.name,
              type: "question_type"
            };

            return (
              <FilterItem
                key={type.id}
                item={typeItem}
                level={0}
                isExpanded={false}
                isSelected={selectedTypes.includes(type.id)}
                questionCount={{
                  total: type.count,
                  filtered: type.count
                }}
                hasChildren={false}
                onToggleExpand={() => {}}
                onToggleSelect={() => onToggleType(type.id)}
              />
            );
          })}
        </div>
      )}
    </div>
  );
};
