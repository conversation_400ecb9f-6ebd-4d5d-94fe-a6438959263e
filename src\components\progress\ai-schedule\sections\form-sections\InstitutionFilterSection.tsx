import React from "react";
import { UseFormReturn } from "react-hook-form";
import { Building2, <PERSON>, <PERSON><PERSON>, Setting<PERSON> } from "lucide-react";
import { UserPreferencesDisplay } from "@/components/study/UserPreferencesDisplay";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useStudyPreferences } from "@/hooks/useStudyPreferences";
import type { AIScheduleFormData } from "../../types";

interface InstitutionFilterSectionProps {
  form: UseFormReturn<AIScheduleFormData>;
}

export const InstitutionFilterSection = ({ form }: InstitutionFilterSectionProps) => {
  const { preferences } = useStudyPreferences();
  const generationMode = form.watch('generationMode') || 'institution_based';

  // Definir modo padrão baseado nas preferências do usuário
  React.useEffect(() => {
    if (preferences?.preferences_completed && preferences.target_institutions?.length > 0 && !preferences.target_institutions_unknown) {
      // Usuário tem preferências configuradas - usar modo baseado em preferências por padrão
      form.setValue('generationMode', 'institution_based');
    } else {
      // Usuário não tem preferências - usar modo aleatório por padrão
      form.setValue('generationMode', 'random');
    }
  }, [preferences, form]);

  return (
    <div className="space-y-6">
      {/* Seção de Preferências do Usuário */}
      <div>
        <UserPreferencesDisplay />
      </div>

      {/* Seção de Modo de Geração */}
      <div className="bg-white border border-slate-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-4">
          <div className="p-1.5 rounded-full bg-blue-100">
            <Building2 className="w-4 h-4 text-blue-600" />
          </div>
          <h3 className="text-sm font-semibold text-gray-800">Modo de Geração</h3>
        </div>

        <RadioGroup
          value={generationMode}
          onValueChange={(value) => {
            form.setValue('generationMode', value as 'institution_based' | 'random');

            // Limpar campos específicos quando mudar o modo
            if (value === 'random') {
              form.setValue('institutionIds', []);
              form.setValue('startYear', undefined);
              form.setValue('endYear', undefined);
            }
          }}
          className="space-y-2"
        >
          {/* Modo baseado em preferências */}
          <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
            <RadioGroupItem value="institution_based" id="preference-mode" />
            <div className="flex-1">
              <Label htmlFor="preference-mode" className="flex items-center gap-2 text-sm font-medium cursor-pointer">
                <Brain className="h-4 w-4 text-green-600" />
                Inteligente
                {preferences?.preferences_completed && preferences.target_institutions?.length > 0 && !preferences.target_institutions_unknown && (
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">Recomendado</span>
                )}
              </Label>
              <p className="text-xs text-gray-600 mt-0.5">
                Prioriza temas das suas instituições alvo
                {preferences?.preferences_completed && preferences.target_institutions?.length > 0 && !preferences.target_institutions_unknown && (
                  <span className="text-green-600 font-medium"> • {preferences.target_institutions.length} instituições</span>
                )}
              </p>
            </div>
          </div>

          {/* Modo aleatório */}
          <div className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
            <RadioGroupItem value="random" id="random-mode" />
            <div className="flex-1">
              <Label htmlFor="random-mode" className="flex items-center gap-2 text-sm font-medium cursor-pointer">
                <Shuffle className="h-4 w-4 text-gray-600" />
                Aleatório
              </Label>
              <p className="text-xs text-gray-600 mt-0.5">
                Distribui todos os temas de forma equilibrada
              </p>
            </div>
          </div>
        </RadioGroup>
      </div>
    </div>
  );
};
