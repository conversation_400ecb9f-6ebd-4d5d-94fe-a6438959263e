/**
 * ✅ SISTEMA DE LOGGING INTELIGENTE
 * Logs condicionais baseados no ambiente
 * Apenas logs essenciais em produção
 */

const isDev = import.meta.env.DEV;
const isProd = import.meta.env.PROD;

/**
 * Log apenas em desenvolvimento
 */
export const devLog = (...args: any[]) => {
  if (isDev) {
    console.log('🔧 [DEV]', ...args);
  }
};

/**
 * Log de debug com filtros inteligentes
 */
export const debugLog = (message: string, ...args: any[]) => {
  if (isDev && !isSpamMessage(message)) {
    console.log(`🐛 [DEBUG] ${message}`, ...args);
  }
};

/**
 * Log de sucesso apenas em dev
 */
export const successLog = (message: string, ...args: any[]) => {
  if (isDev) {
    console.log(`✅ ${message}`, ...args);
  }
};

/**
 * Log de warning sempre (mas filtrado)
 */
export const warnLog = (message: string, ...args: any[]) => {
  if (!isSpamMessage(message)) {
    console.warn(`⚠️ ${message}`, ...args);
  }
};

/**
 * Log de erro sempre
 */
export const errorLog = (message: string, ...args: any[]) => {
  console.error(`❌ ${message}`, ...args);
};

/**
 * Log crítico sempre
 */
export const criticalLog = (message: string, ...args: any[]) => {
  console.error(`🚨 [CRITICAL] ${message}`, ...args);
};

/**
 * Log de performance apenas em dev
 */
export const perfLog = (message: string, startTime: number) => {
  if (isDev) {
    const duration = Date.now() - startTime;
    console.log(`⚡ [PERF] ${message}: ${duration}ms`);
  }
};

/**
 * Verificar se é uma mensagem spam
 */
const isSpamMessage = (message: string): boolean => {
  const spamKeywords = [
    'refresh',
    'loading',
    'update',
    'render',
    'query',
    'fetch',
    'cache',
    'invalidate',
    'revalidate'
  ];
  
  return spamKeywords.some(keyword => 
    message.toLowerCase().includes(keyword)
  );
};

/**
 * Log condicional baseado em condição
 */
export const conditionalLog = (condition: boolean, message: string, ...args: any[]) => {
  if (condition && isDev) {
    console.log(`🔍 [CONDITIONAL] ${message}`, ...args);
  }
};

/**
 * Log de timer/cronômetro apenas em dev
 */
export const timerLog = (message: string, ...args: any[]) => {
  if (isDev) {
    console.log(`⏱️ [TIMER] ${message}`, ...args);
  }
};

/**
 * Log de API apenas em dev
 */
export const apiLog = (message: string, ...args: any[]) => {
  if (isDev) {
    console.log(`🌐 [API] ${message}`, ...args);
  }
};

/**
 * Log de validação apenas em dev
 */
export const validationLog = (message: string, ...args: any[]) => {
  if (isDev) {
    console.log(`✔️ [VALIDATION] ${message}`, ...args);
  }
};

/**
 * Desabilitar todos os logs (para testes)
 */
export const disableAllLogs = () => {
  if (isDev) {
    console.log('🔇 [LOGGER] Todos os logs desabilitados');
  }
  
  // Sobrescrever console methods
  const noop = () => {};
  console.log = noop;
  console.warn = noop;
  console.info = noop;
  // Manter apenas console.error para erros críticos
};

/**
 * Estatísticas de ambiente
 */
export const logEnvironmentInfo = () => {
  if (isDev) {
    console.log('🌍 [ENV] Environment Info:', {
      isDev,
      isProd,
      nodeEnv: import.meta.env.MODE,
      timestamp: new Date().toISOString()
    });
  }
};

// Auto-log de inicialização apenas em dev
if (isDev) {
  console.log('🚀 [LOGGER] Sistema de logging inteligente ativado');
}
