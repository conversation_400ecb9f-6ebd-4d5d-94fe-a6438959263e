import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Sparkles,
  Calendar,
  Clock,
  CheckCircle,
  Flame,
  Star,
  Trophy,
  Brain
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useStreakSystem, isDayActive } from '@/hooks/useOptimizedStreakStats';
import { useOptimizedTodayStats } from '@/hooks/useConsolidatedDashboardStats';
import { useInsights } from '@/hooks/useInsights';
import { useStudySchedule } from '@/hooks/study-schedule/useStudySchedule';
import { extractAllScheduledFocusIds } from '@/utils/todayStudiesFilter';
import { useInsightStudy } from '@/hooks/useInsightStudy';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import InsightCard from '@/components/insights/InsightCard';
import { StudyOptionsDialog } from '@/components/study/StudyOptionsDialog';
import { format, startOfWeek, addDays, isToday } from 'date-fns';
import { ptBR } from 'date-fns/locale';

const UnifiedBanner: React.FC = () => {
  const { user } = useAuth();
  const [currentTime, setCurrentTime] = useState(new Date());

  // ✅ OTIMIZADO: Usar hook consolidado para eliminar duplicações
  const {
    questionsAnswered,
    timeStudied,
    isLoading: statsLoading
  } = useOptimizedTodayStats();

  const {
    currentStreak,
    maxStreak,
    weekActivities,
    isLoading: streakLoading
  } = useStreakSystem();

  const { weeklySchedule } = useStudySchedule();

  // ✅ NOVO: Extrair focus_ids de hoje + próximas 2 semanas para excluir dos insights
  // TODO: Implementar busca das próximas semanas quando disponível
  const scheduledFocusIds = extractAllScheduledFocusIds(weeklySchedule, []);

  const {
    insights,
    isLoading: insightsLoading
  } = useInsights(scheduledFocusIds);

  // ✅ NOVO: Hook para preferências do usuário
  const { preferences } = useStudyPreferences();

  // ✅ NOVO: Hook para gerenciar estudos de insights
  const {
    isDialogOpen,
    selectedInsight,
    maxQuestions,
    availableYears,
    availableInstitutions,
    openStudyDialog,
    closeStudyDialog,
    startStudy
  } = useInsightStudy();

  // Atualizar horário a cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);
    return () => clearInterval(interval);
  }, []);

  // Saudação baseada no horário
  const getGreeting = () => {
    const hour = currentTime.getHours();
    if (hour < 12) return 'Bom dia';
    if (hour < 18) return 'Boa tarde';
    return 'Boa noite';
  };

  // Nome do usuário
  const userName = user?.user_metadata?.name || 
    user?.user_metadata?.full_name || 
    user?.email?.split('@')[0] || 
    'Estudante';

  // Data formatada
  const formattedDate = currentTime.toLocaleDateString('pt-BR', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long' 
  });

  // Gerar dias da semana (domingo a sábado)
  const today = new Date();
  const startDate = startOfWeek(today, { weekStartsOn: 0 }); // Domingo
  
  const weekDays = Array.from({ length: 7 }, (_, index) => {
    const date = addDays(startDate, index);
    const shortName = format(date, 'EEEEE', { locale: ptBR }).toUpperCase(); // D, S, T, Q, Q, S, S
    const isActive = isDayActive(date, weekActivities);
    const isCurrentDay = isToday(date);
    
    return {
      key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`,
      shortName,
      isActive,
      isCurrentDay
    };
  });

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-gradient-to-r from-blue-50 via-white to-purple-50 rounded-xl border border-gray-200 shadow-lg p-5"
    >


      {/* Conteúdo Principal Unificado */}
      <div className="bg-white/60 backdrop-blur-sm rounded-lg border border-gray-200/50 p-4">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 items-center">

          {/* Stats Melhoradas */}
          <div className="lg:col-span-1">
            <div className="bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg border border-gray-200 p-3">
              <div className="flex items-center justify-center gap-4">
                <div className="text-center">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                      <CheckCircle className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-xl font-bold text-green-700">
                      {statsLoading ? '...' : questionsAnswered}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 font-medium">questões hoje</div>
                </div>

                <div className="w-px h-10 bg-gray-300"></div>

                <div className="text-center">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                      <Clock className="h-3 w-3 text-white" />
                    </div>
                    <span className="text-xl font-bold text-blue-700">
                      {statsLoading ? '...' : `${timeStudied}m`}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 font-medium">estudado hoje</div>
                </div>
              </div>
            </div>
          </div>

          {/* Insight Melhorado */}
          <div className="lg:col-span-2">
            <div className="bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg border border-purple-200 p-3">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg flex items-center justify-center">
                  <Brain className="h-3 w-3 text-white" />
                </div>
                <span className="text-sm font-semibold text-purple-700">Insight do Dia</span>
              </div>
              <InsightCard
                insight={insights?.suggested_focus || null}
                isLoading={insightsLoading}
                simplified={true}
                className="border-0 shadow-none bg-transparent p-0"
                onStudyClick={openStudyDialog}
              />
            </div>
          </div>

          {/* Streak Melhorado */}
          <div className="lg:col-span-1">
            <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg border border-orange-200 p-3">
              {/* Header do Streak - Uma linha */}
              <div className="flex items-center justify-center gap-3 mb-3">
                <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                  <Flame className="h-4 w-4 text-white" />
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-xl font-bold text-orange-600">
                    {streakLoading ? '...' : currentStreak}
                  </span>
                  <span className="text-xs text-orange-500 font-medium">
                    {currentStreak === 1 ? 'dia' : 'dias'}
                  </span>
                  {maxStreak > 0 && (
                    <>
                      <span className="text-gray-400 mx-1">•</span>
                      <Trophy className="h-3 w-3 text-amber-600" />
                      <span className="text-xs text-amber-700 font-semibold">
                        {maxStreak}
                      </span>
                    </>
                  )}
                </div>
              </div>

              {/* Progresso da Semana */}
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-1">
                  {weekDays.map((day) => (
                    <div key={day.key} className="flex flex-col items-center gap-1">
                      <div className={`text-xs font-medium transition-colors duration-200 ${
                        day.isCurrentDay
                          ? 'text-blue-600 font-bold'
                          : 'text-gray-500'
                      }`}>
                        {day.shortName}
                      </div>
                      <div
                        className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                          day.isActive
                            ? 'bg-orange-500 border-orange-600 text-white'
                            : day.isCurrentDay
                              ? 'bg-blue-500 border-blue-600 text-white'
                              : 'bg-gray-100 border-gray-300 text-gray-400'
                        }`}
                        title={`${day.shortName} - ${day.isActive ? 'Estudou' : day.isCurrentDay ? 'Hoje' : 'Não estudou'}`}
                      >
                        {day.isActive ? (
                          <CheckCircle className="h-2.5 w-2.5" />
                        ) : day.isCurrentDay ? (
                          <Star className="h-2.5 w-2.5" />
                        ) : (
                          <div className="w-1.5 h-1.5 rounded-full bg-current opacity-50"></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

            </div>
          </div>

        </div>
      </div>


      {/* Dialog de Estudos do Insight */}
      <StudyOptionsDialog
        open={isDialogOpen}
        onOpenChange={closeStudyDialog}
        maxQuestions={maxQuestions}
        minQuestions={1}
        availableYears={availableYears}
        availableInstitutions={availableInstitutions}
        onStartStudy={startStudy}
        totalTopics={1}
        specialtyId={undefined}
        themeId={undefined}
        focusId={selectedInsight?.focus_id}
        insightData={selectedInsight ? {
          focus_name: selectedInsight.focus_name,
          specialty_name: selectedInsight.specialty_name,
          theme_name: selectedInsight.theme_name,
          temperature: selectedInsight.temperature
        } : undefined}
      />
    </motion.div>
  );
};

export default UnifiedBanner;
