import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface MedicalSpecialty {
  id: string;
  name: string;
}

/**
 * 🎯 HOOK CENTRALIZADO PARA ESPECIALIDADES MÉDICAS
 * 
 * Substitui todas as queries duplicadas de medical_specialties
 * Usado em: StudyPreferences, StudyPreferencesSection, useStudyPreferences
 */
export const useMedicalSpecialties = () => {
  return useQuery({
    queryKey: ['medical-specialties'],
    queryFn: async (): Promise<MedicalSpecialty[]> => {
      const { data, error } = await supabase
        .from('medical_specialties')
        .select('id, name')
        .order('name');

      if (error) {
        console.error('Erro ao buscar especialidades:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 60 * 60 * 1000, // 1 hora (dados estáticos)
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
  });
};

/**
 * Hook para buscar uma especialidade específica por ID
 * Usa o cache do hook principal para evitar queries extras
 */
export const useMedicalSpecialtyById = (specialtyId: string | null) => {
  const { data: specialties, isLoading, error } = useMedicalSpecialties();

  const specialty = specialties?.find(s => s.id === specialtyId);

  return {
    data: specialty || null,
    isLoading,
    error
  };
};

/**
 * 🎯 HOOK CENTRALIZADO PARA STUDY CATEGORIES
 *
 * Substitui múltiplas queries duplicadas de study_categories
 * Usado em: useStaticSpecialties, useOptimizedCategories, etc.
 */
export interface StudyCategory {
  id: string;
  name: string;
  type: 'specialty' | 'theme' | 'focus';
  parent_id: string | null;
}

export const useStudyCategories = () => {
  return useQuery({
    queryKey: ['study-categories-all'],
    queryFn: async (): Promise<StudyCategory[]> => {
      const { data, error } = await supabase
        .from('study_categories')
        .select('id, name, type, parent_id')
        .order('type, name')
        .limit(2000);

      if (error) {
        console.error('Erro ao buscar categorias:', error);
        throw error;
      }

      return data || [];
    },
    staleTime: 60 * 60 * 1000, // 1 hora (dados estáticos)
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
  });
};

/**
 * Hook para buscar apenas especialidades do cache consolidado
 */
export const useSpecialtiesFromCache = () => {
  const { data: categories, isLoading, error } = useStudyCategories();

  const specialties = categories?.filter(cat => cat.type === 'specialty') || [];

  return {
    data: specialties,
    isLoading,
    error
  };
};
