import { useMemo } from 'react';
import { useUnifiedCategoryResolver } from './useUnifiedMetadata';

/**
 * 🎯 HOOK OTIMIZADO - RESOLVER DE CATEGORIAS
 *
 * Agora usa useUnifiedMetadata como fonte única.
 * Elimina requisições duplicadas.
 */
export const useCategoryResolver = () => {
  // ✅ OTIMIZADO: Usar hook unificado
  const { resolver, isLoading, isReady } = useUnifiedCategoryResolver();

  /**
   * Resolve uma única questão com dados de categorias
   */
  const resolveQuestion = (question: any) => {
    if (!resolver) return question;

    return {
      ...question,
      specialty: question.specialty_id ? resolver.getSpecialty(question.specialty_id) : null,
      theme: question.theme_id ? resolver.getTheme(question.theme_id) : null,
      focus: question.focus_id ? resolver.getFocus(question.focus_id) : null,
      location: question.exam_location ? resolver.getLocation(question.exam_location) : null,
    };
  };

  /**
   * Resolve múltiplas questões com dados de categorias
   */
  const resolveQuestions = (questions: any[]) => {
    if (!resolver || !questions) return questions;
    return questions.map(resolveQuestion);
  };

  /**
   * Resolve apenas o nome de uma categoria por ID
   */
  const resolveCategoryName = (categoryId: string, type: 'specialty' | 'theme' | 'focus') => {
    if (!resolver) return null;

    switch (type) {
      case 'specialty':
        return resolver.getSpecialtyName(categoryId);
      case 'theme':
        return resolver.getThemeName(categoryId);
      case 'focus':
        return resolver.getFocusName(categoryId);
      default:
        return null;
    }
  };

  /**
   * Resolve nome de local por ID
   */
  const resolveLocationName = (locationId: string) => {
    if (!resolver) return null;
    return resolver.getLocationName(locationId);
  };

  return {
    resolveQuestion,
    resolveQuestions,
    resolveCategoryName,
    resolveLocationName,
    isReady,
    isLoading,
  };
};
