import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Brain, MessageCircle, Target, Sparkles, ArrowRight, X } from 'lucide-react';

interface DrWillContextTutorialProps {
  isVisible: boolean;
  onComplete: () => void;
  onSkip: () => void;
}

// Estado global para controlar o highlight do botão após o tutorial
let showButtonHighlight = false;
export const setButtonHighlight = (show: boolean) => {
  showButtonHighlight = show;
};
export const getButtonHighlight = () => showButtonHighlight;

export const DrWillContextTutorial: React.FC<DrWillContextTutorialProps> = ({
  isVisible,
  onComplete,
  onSkip
}) => {
  const [showDialog, setShowDialog] = useState(false);
  const [showHighlight, setShowHighlight] = useState(false);

  useEffect(() => {
    if (isVisible) {
      // Pequeno delay para garantir que o botão já está renderizado
      const timer = setTimeout(() => {
        setShowDialog(true);
      }, 1000); // Aumentei para 1 segundo para garantir que a página carregou
      return () => clearTimeout(timer);
    } else {
      setShowDialog(false);
    }
  }, [isVisible]);

  const handleComplete = () => {
    setShowDialog(false);
    setButtonHighlight(true);
    setShowHighlight(true);

    // Mostrar highlight por 5 segundos
    setTimeout(() => {
      setButtonHighlight(false);
      setShowHighlight(false);
    }, 5000);

    setTimeout(() => {
      onComplete();
    }, 300);
  };

  const handleSkip = () => {
    setShowDialog(false);
    setTimeout(() => {
      onComplete(); // Usar onComplete em vez de onSkip
    }, 300);
  };

  if (!isVisible && !showHighlight) return null;

  return (
    <>
      {/* Overlay escuro simples */}
      <AnimatePresence>
        {isVisible && showDialog && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 z-[60]"
            style={{ pointerEvents: 'auto' }}
          />
        )}
      </AnimatePresence>

      {/* Spotlight no botão Dr. Will - durante tutorial e highlight */}
      <AnimatePresence>
        {(isVisible || showHighlight) && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="fixed bottom-6 right-6 z-[85]"
            style={{
              filter: 'drop-shadow(0 0 20px rgba(16, 185, 129, 0.6))',
              pointerEvents: 'none'
            }}
          >
            {/* Anel pulsante ao redor do botão */}
            <div className="absolute inset-0 rounded-full border-4 border-emerald-400 animate-ping opacity-75" />
            <div className="absolute inset-0 rounded-full border-2 border-emerald-300 animate-pulse" />

            {/* Seta apontando para o botão - apenas no highlight */}
            {showHighlight && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="absolute -top-12 left-1/2 transform -translate-x-1/2 z-[90]"
              >
                <div className="bg-emerald-500 text-white px-3 py-1 rounded-lg text-xs font-bold whitespace-nowrap shadow-lg">
                  Clique aqui! 🎯
                </div>
                <div className="w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-emerald-500 mx-auto"></div>
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Dialog explicativo */}
      <Dialog open={showDialog} onOpenChange={() => {}}>
        <DialogContent
          className="w-[95vw] max-w-lg border-2 border-emerald-300 bg-gradient-to-br from-white to-emerald-50 shadow-2xl z-[9999] max-h-[90vh] overflow-y-auto"
          hideCloseButton
        >
          <DialogHeader className="text-center pb-4 pr-0">
            <DialogTitle className="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent mb-4">
              🚀 Você sabia?
            </DialogTitle>

            <DialogDescription className="sr-only">
              Tutorial sobre o Dr. Will Contexto - assistente que acompanha suas questões em tempo real
            </DialogDescription>

            <div className="text-lg text-gray-700 leading-relaxed">
              O <strong className="text-emerald-600">Dr. Will</strong> está acompanhando em tempo real suas questões!
            </div>
          </DialogHeader>

          <div className="space-y-4">
            {/* Funcionalidades em grid */}
            <div className="grid grid-cols-1 gap-3">
              <div className="bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-lg p-3">
                <div className="flex items-center gap-3 mb-1">
                  <div className="w-7 h-7 bg-emerald-500 rounded-full flex items-center justify-center">
                    <Brain className="h-3 w-3 text-white" />
                  </div>
                  <h4 className="font-semibold text-emerald-800 text-sm">Conhece tudo da questão</h4>
                </div>
                <p className="text-emerald-700 text-xs">Ele sabe o contexto, tema, especialidade e pode te ajudar especificamente</p>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-3">
                <div className="flex items-center gap-3 mb-1">
                  <div className="w-7 h-7 bg-purple-500 rounded-full flex items-center justify-center">
                    <div className="text-white text-xs">📊</div>
                  </div>
                  <h4 className="font-semibold text-purple-800 text-sm">Cria recursos visuais</h4>
                </div>
                <p className="text-purple-700 text-xs">Fluxogramas, mapas mentais, tabelas para auxiliar seus estudos</p>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-3">
                <div className="flex items-center gap-3 mb-1">
                  <div className="w-7 h-7 bg-orange-500 rounded-full flex items-center justify-center">
                    <MessageCircle className="h-3 w-3 text-white" />
                  </div>
                  <h4 className="font-semibold text-orange-800 text-sm">Resposta instantânea</h4>
                </div>
                <p className="text-orange-700 text-xs">Tire dúvidas na hora, sem perder o foco da questão</p>
              </div>
            </div>

            {/* Call to action destacado */}
            <div className="bg-gradient-to-r from-teal-500 to-emerald-500 rounded-lg p-4 text-center text-white">
              <div className="text-xl mb-2">✨</div>
              <h3 className="font-bold text-base mb-2">Teste a qualquer momento!</h3>
              <p className="text-teal-100 text-xs">
                Clique no botão <span className="bg-white/20 px-2 py-1 rounded font-bold">🎯</span> no canto inferior direito e descubra como o Dr. Will pode revolucionar seus estudos
              </p>
            </div>
          </div>

          {/* Botão de ação único */}
          <div className="flex justify-center pt-4">
            <Button
              onClick={handleComplete}
              className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white border-none h-12 text-base font-semibold"
            >
              <Sparkles className="h-4 w-4 mr-2" />
              <span>Vamos testar!</span>
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};
