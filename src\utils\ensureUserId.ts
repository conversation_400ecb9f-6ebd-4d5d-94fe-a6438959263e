
import { supabase } from "@/integrations/supabase/client";
import { requestCache } from "./requestCache";

/**
 * 🎯 CACHE GLOBAL AGRESSIVO PARA USER ID
 *
 * Elimina TODAS as verificações duplicadas de usuário
 * Cache válido por 5 minutos ou até logout
 */

let globalUserCache: {
  userId: string | null;
  timestamp: number;
  promise: Promise<string> | null;
} = {
  userId: null,
  timestamp: 0,
  promise: null
};

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

/**
 * Garante que temos um userId válido, usando cache global AGRESSIVO
 */
export const ensureUserId = async (): Promise<string> => {
  const now = Date.now();

  // ✅ CACHE HIT: Se temos cache válido, retornar imediatamente
  if (globalUserCache.userId && (now - globalUserCache.timestamp) < CACHE_DURATION) {
    return globalUserCache.userId;
  }

  // ✅ PROMISE DEDUPLICATION: Se já há uma promise em andamento, aguardar ela
  if (globalUserCache.promise) {
    return globalUserCache.promise;
  }

  // ✅ NOVA VERIFICAÇÃO: Criar nova promise e cachear
  globalUserCache.promise = (async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        // Limpar cache em caso de erro
        globalUserCache = { userId: null, timestamp: 0, promise: null };
        throw new Error("Usuário não autenticado");
      }

      // ✅ SALVAR NO CACHE GLOBAL
      globalUserCache = {
        userId: user.id,
        timestamp: now,
        promise: null
      };

      return user.id;
    } catch (error) {
      // Limpar cache e promise em caso de erro
      globalUserCache = { userId: null, timestamp: 0, promise: null };
      throw error;
    }
  })();

  return globalUserCache.promise;
};

/**
 * Limpar cache de usuário (usar no logout)
 */
export const clearUserCache = () => {
  globalUserCache = { userId: null, timestamp: 0, promise: null };
};
