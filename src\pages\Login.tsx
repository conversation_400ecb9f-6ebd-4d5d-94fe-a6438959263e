import { useState } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, <PERSON>rkles, Shield, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { useAutoRedirect } from "@/hooks/useAuthRedirect";
import { AuthLoadingScreen } from "@/components/common/AuthLoadingScreen";
import AuthDialog from "@/components/auth/AuthDialog";

const Login = () => {
  const navigate = useNavigate();
  const { isChecking, isAuthenticated } = useAutoRedirect();
  const [showAuthDialog, setShowAuthDialog] = useState(true);

  // Mostrar loading enquanto verifica autenticação
  if (isChecking) {
    return <AuthLoadingScreen message="Verificando autenticação..." />;
  }

  // Se já está autenticado, redirecionar
  if (isAuthenticated) {
    navigate("/plataformadeestudos", { replace: true });
    return null;
  }

  const benefits = [
    {
      icon: Sparkles,
      title: "Questões Exclusivas",
      description: "Acesso a milhares de questões atualizadas"
    },
    {
      icon: Shield,
      title: "Preparação Segura",
      description: "Metodologia comprovada por especialistas"
    },
    {
      icon: Zap,
      title: "Resultados Rápidos",
      description: "Veja seu progresso em tempo real"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#FEF7CD] via-[#FEF7CD] to-hackathon-yellow/20">
      {/* Header Mobile */}
      <header className="sticky top-0 z-40 bg-[#FEF7CD]/95 backdrop-blur-sm border-b border-black/10">
        <div className="container max-w-md mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate("/")}
              className="flex items-center gap-2 text-gray-700"
            >
              <ArrowLeft className="h-4 w-4" />
              Voltar
            </Button>
            
            <div className="flex items-center relative">
              <div className="bg-white border-2 border-black px-3 py-1 shadow-card-sm">
                <span className="font-bold text-lg tracking-tight">Med EVO</span>
              </div>
              <div className="absolute -right-1 -top-1">
                <span className="bg-hackathon-red text-white text-xs px-1.5 py-0.5 rounded border border-black font-bold">
                  beta
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container max-w-md mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-8"
        >
          <h1 className="text-3xl font-black text-gray-900 mb-3">
            Bem-vindo de volta!
          </h1>
          <p className="text-gray-600 text-lg">
            Continue sua preparação para a residência médica
          </p>
        </motion.div>

        {/* Benefits Cards */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="space-y-4 mb-8"
        >
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="bg-white border-2 border-black rounded-lg p-4 shadow-card-sm"
            >
              <div className="flex items-start gap-3">
                <div className="bg-hackathon-yellow border border-black rounded-lg p-2">
                  <benefit.icon className="h-5 w-5 text-black" />
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-gray-900 mb-1">{benefit.title}</h3>
                  <p className="text-sm text-gray-600">{benefit.description}</p>
                </div>
              </div>
            </div>
          ))}
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <div className="bg-white border-2 border-black rounded-xl p-6 shadow-card">
            <h2 className="text-xl font-bold text-gray-900 mb-2">
              Pronto para começar?
            </h2>
            <p className="text-gray-600 mb-6">
              Faça login ou crie sua conta gratuita
            </p>
            
            <div className="space-y-3">
              <Button
                onClick={() => setShowAuthDialog(true)}
                className="w-full bg-hackathon-green hover:bg-hackathon-green/90 border-2 border-black text-black font-bold py-3 shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
              >
                Entrar na Plataforma
              </Button>
              
              <p className="text-xs text-gray-500">
                Ao continuar, você concorda com nossos termos de uso
              </p>
            </div>
          </div>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-8 text-center"
        >
          <div className="flex items-center justify-center gap-2 mb-2">
            <div className="flex -space-x-1">
              <div className="w-8 h-8 rounded-full border-2 border-black bg-blue-100 flex items-center justify-center text-blue-600 font-bold text-sm">M</div>
              <div className="w-8 h-8 rounded-full border-2 border-black bg-green-100 flex items-center justify-center text-green-600 font-bold text-sm">D</div>
              <div className="w-8 h-8 rounded-full border-2 border-black bg-purple-100 flex items-center justify-center text-purple-600 font-bold text-sm">R</div>
            </div>
          </div>
          <p className="text-sm text-gray-600">
            Mais de <span className="font-bold">1.000 estudantes</span> já confiam na nossa plataforma.
          </p>
        </motion.div>
      </main>

      {/* Auth Dialog */}
      <AuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        hidden={true}
        onSuccess={() => {
          setShowAuthDialog(false);
          navigate("/plataformadeestudos");
        }}
      />
    </div>
  );
};

export default Login;
