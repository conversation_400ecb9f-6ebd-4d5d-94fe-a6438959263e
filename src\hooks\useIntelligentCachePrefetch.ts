import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { useMedicalSpecialties, useStudyCategories } from '@/hooks/useMedicalSpecialties';

/**
 * 🎯 HOOK PARA PREFETCH INTELIGENTE
 * 
 * Prefetch dados que serão usados pelos componentes lazy (TodayStudies, SessionHistory)
 * para evitar queries duplicadas quando eles carregarem com delay
 */
export const useIntelligentCachePrefetch = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // ✅ PREFETCH: Dados estáticos que serão reutilizados
  const { data: medicalSpecialties } = useMedicalSpecialties();
  const { data: studyCategories } = useStudyCategories();

  useEffect(() => {
    if (!user?.id) return;

    // ✅ PREFETCH: Dados que TodayStudies vai precisar
    const prefetchTodayStudiesData = async () => {
      try {
        // Prefetch study_schedules (evita duplicação)
        queryClient.prefetchQuery({
          queryKey: ['consolidated-schedule-data', user.id],
          staleTime: 30 * 1000, // 30 segundos
        });

        // Prefetch study_categories especialidades (evita duplicação)
        if (!queryClient.getQueryData(['study-categories-all'])) {
          queryClient.prefetchQuery({
            queryKey: ['study-categories-all'],
            staleTime: 60 * 60 * 1000, // 1 hora
          });
        }

      } catch (error) {
        console.error('Erro no prefetch TodayStudies:', error);
      }
    };

    // ✅ PREFETCH: Dados que SessionHistory vai precisar
    const prefetchSessionHistoryData = async () => {
      try {
        // Prefetch study_sessions (evita duplicação)
        queryClient.prefetchQuery({
          queryKey: ['study-sessions-consolidated', user.id],
          staleTime: 2 * 60 * 1000, // 2 minutos
        });

      } catch (error) {
        console.error('Erro no prefetch SessionHistory:', error);
      }
    };

    // ✅ TIMING: Prefetch antes dos componentes lazy carregarem
    const timer1 = setTimeout(prefetchTodayStudiesData, 1000); // 1s antes do TodayStudies (3s)
    const timer2 = setTimeout(prefetchSessionHistoryData, 2000); // 2s antes do SessionHistory (4s)

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, [user?.id, queryClient]);

  return {
    // Indicadores de cache disponível
    hasMedicalSpecialties: !!medicalSpecialties?.length,
    hasStudyCategories: !!studyCategories?.length,
  };
};

/**
 * Hook para verificar se dados estão em cache antes de fazer query
 */
export const useCacheFirst = (queryKey: string[]) => {
  const queryClient = useQueryClient();
  
  const getCachedData = () => {
    return queryClient.getQueryData(queryKey);
  };

  const isCacheAvailable = () => {
    const data = getCachedData();
    return data !== undefined && data !== null;
  };

  return {
    getCachedData,
    isCacheAvailable,
    queryClient
  };
};
