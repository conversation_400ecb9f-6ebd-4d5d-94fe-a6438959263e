import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook para detectar quando o usuário volta para a aba e invalidar cache
 * Resolve o problema de precisar dar F5 para ver dados atualizados
 */
export const usePageVisibility = (options?: {
  /** Chaves específicas do React Query para invalidar */
  queryKeys?: string[];
  /** Callback personalizado quando a página fica visível */
  onVisible?: () => void;
  /** Delay antes de invalidar (em ms) */
  delay?: number;
}) => {
  const queryClient = useQueryClient();
  const wasHidden = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const {
    queryKeys = [
      'study-sessions-consolidated',
      'daily-review-cards-mini',
      'study-schedule',
      'flashcards-sessions',
      'user-stats'
    ],
    onVisible,
    delay = 1000
  } = options || {};

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // Página ficou oculta
        wasHidden.current = true;
        
        // Limpar timeout pendente
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      } else if (wasHidden.current) {
        // Página ficou visível novamente após estar oculta
        wasHidden.current = false;
        
        // Aguardar um pouco antes de invalidar para evitar múltiplas chamadas
        timeoutRef.current = setTimeout(() => {
          // Invalidar queries específicas
          queryKeys.forEach(key => {
            queryClient.invalidateQueries({ queryKey: [key] });
          });

          // Callback personalizado
          if (onVisible) {
            onVisible();
          }
        }, delay);
      }
    };

    // Adicionar listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Cleanup
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [queryClient, queryKeys, onVisible, delay]);

  return {
    isVisible: !document.hidden,
    wasHidden: wasHidden.current
  };
};

/**
 * Hook específico para a página inicial
 * Invalida automaticamente os dados mais importantes
 */
export const useHomePageRefresh = () => {
  const queryClient = useQueryClient();

  return usePageVisibility({
    queryKeys: [
      'study-sessions-consolidated',
      'daily-review-cards-mini', 
      'study-schedule',
      'flashcards-sessions'
    ],
    onVisible: () => {
      // Também pode forçar refetch de dados críticos
      queryClient.refetchQueries({ 
        queryKey: ['study-sessions-consolidated'],
        type: 'active'
      });
    },
    delay: 500 // Mais rápido para a home
  });
};
