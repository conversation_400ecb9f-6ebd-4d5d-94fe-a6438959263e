import type { TemperatureCategory, FocusFrequencyData } from '@/types/insights';
import { TEMPERATURE_CONFIGS } from '@/types/insights';

/**
 * Utilitários para classificação de insights na matriz Temperatura × Frequência
 */

/**
 * Classifica um foco na matriz temperatura × frequência
 * 
 * @param frequency - Número de ocorrências nos últimos X anos
 * @param yearsSinceLast - Anos desde a última ocorrência
 * @param frequencyThreshold - Limite de frequência (padrão: 2)
 * @param recencyThreshold - Limite de recência em anos (padrão: 3)
 * @returns Categoria de temperatura
 */
export const classifyFocusTemperature = (
  frequency: number,
  yearsSinceLast: number,
  frequencyThreshold: number = 2,
  recencyThreshold: number = 3
): TemperatureCategory => {
  // Frio: ≤ 1 ocorrência e última aparição > 3 anos atrás
  if (frequency <= 1 && yearsSinceLast > recencyThreshold) {
    return 'frio';
  }
  
  // Morno: ≤ 1 ocorrência e última aparição ≤ 3 anos atrás
  if (frequency <= 1 && yearsSinceLast <= recencyThreshold) {
    return 'morno';
  }
  
  // Quente: ≥ 2 ocorrências e última aparição > 3 anos atrás
  if (frequency >= frequencyThreshold && yearsSinceLast > recencyThreshold) {
    return 'quente';
  }
  
  // Vulcânico: ≥ 2 ocorrências e última aparição ≤ 3 anos atrás
  if (frequency >= frequencyThreshold && yearsSinceLast <= recencyThreshold) {
    return 'vulcanico';
  }
  
  // Fallback para frio
  return 'frio';
};

/**
 * Gera tooltip curto para um insight
 */
export const generateInsightTooltip = (
  frequency: number,
  yearsSinceLast: number,
  yearsRange: number = 5
): string => {
  if (frequency <= 1) {
    return yearsSinceLast < 999 
      ? `Apareceu apenas ${frequency} vez em ${yearsRange} anos e não cai há ${yearsSinceLast} anos.`
      : `Apareceu apenas ${frequency} vez em ${yearsRange} anos.`;
  } else {
    return yearsSinceLast < 999
      ? `Caiu ${frequency} vezes em ${yearsRange} anos e não aparece há ${yearsSinceLast} anos.`
      : `Caiu ${frequency} vezes em ${yearsRange} anos.`;
  }
};

/**
 * Gera descrição detalhada para um insight
 */
export const generateInsightDescription = (
  temperature: TemperatureCategory,
  frequency: number,
  yearsSinceLast: number,
  yearsRange: number = 5
): string => {
  const config = TEMPERATURE_CONFIGS[temperature];
  
  return `
${config.emoji} **${config.label}**
• **Ocorrências:** ${frequency} ${frequency === 1 ? 'vez' : 'vezes'} nos últimos ${yearsRange} anos
• **Última aparição:** ${yearsSinceLast < 999 ? `há ${yearsSinceLast} anos` : 'nunca registrada'}

${config.description}
  `.trim();
};

/**
 * Ordena insights por prioridade (vulcânico > quente > morno > frio)
 */
export const sortInsightsByPriority = (insights: FocusFrequencyData[]): FocusFrequencyData[] => {
  return insights.sort((a, b) => {
    const tempA = classifyFocusTemperature(a.frequency, a.years_since_last);
    const tempB = classifyFocusTemperature(b.frequency, b.years_since_last);
    
    const priorityOrder = { vulcanico: 1, quente: 2, morno: 3, frio: 4 };
    
    // Primeiro critério: prioridade da temperatura
    const priorityDiff = priorityOrder[tempA] - priorityOrder[tempB];
    if (priorityDiff !== 0) return priorityDiff;
    
    // Segundo critério: frequência (maior primeiro)
    const frequencyDiff = b.frequency - a.frequency;
    if (frequencyDiff !== 0) return frequencyDiff;
    
    // Terceiro critério: recência (menor tempo desde última aparição primeiro)
    return a.years_since_last - b.years_since_last;
  });
};

/**
 * Filtra insights por categoria de temperatura
 */
export const filterInsightsByTemperature = (
  insights: FocusFrequencyData[],
  temperature: TemperatureCategory
): FocusFrequencyData[] => {
  return insights.filter(insight => 
    classifyFocusTemperature(insight.frequency, insight.years_since_last) === temperature
  );
};

/**
 * Conta insights por categoria de temperatura
 */
export const countInsightsByTemperature = (insights: FocusFrequencyData[]): Record<TemperatureCategory, number> => {
  return insights.reduce((acc, insight) => {
    const temperature = classifyFocusTemperature(insight.frequency, insight.years_since_last);
    acc[temperature]++;
    return acc;
  }, { frio: 0, morno: 0, quente: 0, vulcanico: 0 });
};

/**
 * Seleciona o melhor insight para sugestão (maior prioridade)
 */
export const selectBestInsight = (insights: FocusFrequencyData[]): FocusFrequencyData | null => {
  if (insights.length === 0) return null;
  
  const sorted = sortInsightsByPriority(insights);
  return sorted[0];
};
