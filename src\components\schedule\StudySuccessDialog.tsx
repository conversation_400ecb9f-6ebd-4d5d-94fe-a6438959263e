import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { CheckCircle, Calendar, Clock, BookOpen } from 'lucide-react';
import type { StudyTopic } from '@/types/study-schedule';

interface StudySuccessDialogProps {
  topic: StudyTopic | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const StudySuccessDialog: React.FC<StudySuccessDialogProps> = ({
  topic,
  open,
  onOpenChange
}) => {
  if (!topic) return null;

  // Calcular próxima revisão (3 dias após hoje)
  const getNextReviewDate = () => {
    const nextReview = new Date();
    nextReview.setDate(nextReview.getDate() + 3);
    
    return {
      date: nextReview.toLocaleDateString('pt-BR', { 
        day: '2-digit', 
        month: '2-digit', 
        year: '2-digit' 
      }),
      dayName: nextReview.toLocaleDateString('pt-BR', { weekday: 'long' }),
      daysFromNow: 3
    };
  };

  const nextReview = getNextReviewDate();

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="w-[80dvw] border-2 border-black rounded-xl p-0 overflow-hidden max-w-md">
        <AlertDialogHeader className="p-4 sm:p-6 border-b-2 border-black bg-green-100">
          <div className="flex items-center gap-3">
            <div className="bg-white p-2 rounded-full border-2 border-black">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <AlertDialogTitle className="text-xl font-bold text-black">
              Tópico estudado
            </AlertDialogTitle>
          </div>
        </AlertDialogHeader>

        <div className="p-4 sm:p-6">
          <AlertDialogDescription className="text-base text-gray-700">
            O tópico foi marcado como estudado com sucesso!
          </AlertDialogDescription>

          {/* Informações da próxima revisão */}
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">
              Próxima Revisão Agendada
            </h4>
            
            <div className="space-y-1 text-sm">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-blue-600" />
                <span>
                  <strong>Data:</strong> {nextReview.date} ({nextReview.dayName})
                </span>
              </div>
              
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-blue-600" />
                <span>
                  <strong>Em:</strong> {nextReview.daysFromNow} dias
                </span>
              </div>
              
              <div className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2 text-blue-600" />
                <span>
                  <strong>Revisão:</strong> primeira
                </span>
              </div>
            </div>
          </div>

          <AlertDialogFooter className="flex justify-end mt-6">
            <AlertDialogAction 
              onClick={() => onOpenChange(false)}
              className="bg-black hover:bg-black/90 text-white font-semibold rounded-xl shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all"
            >
              OK
            </AlertDialogAction>
          </AlertDialogFooter>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default StudySuccessDialog;
