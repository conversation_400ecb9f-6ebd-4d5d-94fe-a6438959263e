import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { useConsolidatedUserPreferences } from '@/hooks/useConsolidatedUserPreferences';

interface UserPreferences {
  id: string;
  user_id: string;
  welcome_dialog_shown: boolean;
  tutorial_completed: boolean;
  filter_tutorial_completed: boolean;
  cinematic_viewed: boolean;
  target_specialty: string | null;
  study_months: number | null;
  preferences_completed: boolean;
  target_institutions_unknown: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Hook consolidado para gerenciar preferências do usuário
 * Substitui múltiplas queries separadas por uma única query otimizada
 */
export const useUserPreferences = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // ✅ OTIMIZADO: Usar hook consolidado como base
  const {
    preferences,
    isLoading,
    error,
    showWelcomeDialog,
    tutorialCompleted,
    filterTutorialCompleted,
    updatePreferences,
    markWelcomeAsSeen,
    markTutorialAsCompleted,
    markFilterTutorialAsCompleted,
    isUpdating
  } = useConsolidatedUserPreferences();

  // Manter funções específicas deste hook
  const resetWelcomeDialog = () => {
    return updatePreferences({ welcome_dialog_shown: false });
  };
  // ✅ OTIMIZADO: Todas as funcionalidades agora vêm do hook consolidado

  return {
    preferences: preferences || null,
    isLoading,
    error,

    // Estados específicos - vêm do hook consolidado
    showWelcomeDialog,
    tutorialCompleted,
    filterTutorialCompleted,

    // Mutations - vêm do hook consolidado
    updatePreferences,
    markWelcomeAsSeen,
    markTutorialAsCompleted,
    markFilterTutorialAsCompleted,
    resetWelcomeDialog, // Função específica deste hook

    // Status das mutations
    isUpdating
  };
};
