import React from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from "@/integrations/supabase/client";
import { useOptimizedCategoriesData } from './useConsolidatedScheduleData';
import { useCentralizedCategories } from '@/hooks/useCentralizedCategories';
import type { DaySchedule } from '@/types/study-schedule';

interface FilterOption {
  id: string;
  name: string;
  type: string;
  parentId: string | null;
}

/**
 * ✅ FUNÇÃO AUXILIAR: Chunking para IDs grandes
 * Divide array de IDs em chunks menores para evitar URLs muito longas
 */
const chunkArray = <T>(array: T[], chunkSize: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

/**
 * ✅ FUNÇÃO AUXILIAR: Buscar categorias com chunking
 * Fallback para casos onde RPC falha ou não está disponível
 */
const fetchCategoriesWithChunking = async (categoryIds: string[], chunkSize: number = 50) => {
  const chunks = chunkArray(categoryIds, chunkSize);
  const allResults: any[] = [];



  for (const chunk of chunks) {
    const { data, error } = await supabase
      .from("study_categories")
      .select("id, name, type, parent_id")
      .in("id", chunk);

    if (error) {
      throw error;
    }

    if (data) {
      allResults.push(...data);
    }
  }

  return allResults;
};

/**
 * Hook otimizado para carregamento de categorias de estudo
 * Carrega apenas categorias necessárias baseadas no cronograma atual
 */
export const useOptimizedCategories = (weeklyPlan: DaySchedule[] = []) => {
  // ✅ OTIMIZADO: Usar cache centralizado SEMPRE
  const { data: centralizedCategories, isLoading: centralizedLoading } = useCentralizedCategories();
  const { usedCategories, availableCategories, isLoading: consolidatedLoading, allCategories } = useOptimizedCategoriesData();

  const queryClient = useQueryClient();

  // Estabilizar query key baseada nos IDs únicos de categorias usadas
  const usedCategoryIds = React.useMemo(() => {
    const ids = new Set<string>();
    weeklyPlan.forEach(day => {
      day.topics.forEach(topic => {
        if (topic.specialtyId) ids.add(topic.specialtyId);
        if (topic.themeId) ids.add(topic.themeId);
        if (topic.focusId) ids.add(topic.focusId);
      });
    });
    return Array.from(ids).sort().join(',');
  }, [weeklyPlan]);

  return useQuery({
    queryKey: ["study-categories-optimized", usedCategoryIds, centralizedLoading],
    queryFn: async (): Promise<FilterOption[]> => {
      // ✅ PRIORIDADE MÁXIMA: Sempre tentar cache centralizado primeiro
      if (!centralizedLoading && centralizedCategories.length > 0) {

        return centralizedCategories.map(cat => ({
          id: cat.id,
          name: cat.name,
          type: cat.type,
          parentId: cat.parent_id
        }));
      }

      // ✅ PRIORIDADE 2: Usar dados consolidados se disponíveis
      if (!consolidatedLoading && allCategories.length > 0) {

        return allCategories.map(cat => ({
          id: cat.id,
          name: cat.name,
          type: cat.type,
          parentId: cat.parent_id
        }));
      }

      // ✅ PRIORIDADE 3: Tentar cache do QueryClient como último recurso antes de fazer queries
      const cachedAllCategories = queryClient.getQueryData(['study-categories-all']) as any[];
      if (cachedAllCategories && cachedAllCategories.length > 0) {

        return cachedAllCategories.map(cat => ({
          id: cat.id,
          name: cat.name,
          type: cat.type,
          parentId: cat.parent_id
        }));
      }

      console.log('🔄 [useOptimizedCategories] Carregando categorias via query tradicional');
      // Log removido - problema resolvido com memoização
      // Extrair IDs de categorias dos tópicos existentes
      const usedCategoryIds = new Set<string>();
      const usedSpecialtyIds = new Set<string>();
      const usedThemeIds = new Set<string>();
      
      weeklyPlan.forEach(day => {
        day.topics.forEach(topic => {
          if (topic.specialtyId) {
            usedCategoryIds.add(topic.specialtyId);
            usedSpecialtyIds.add(topic.specialtyId);
          }
          if (topic.themeId) {
            usedCategoryIds.add(topic.themeId);
            usedThemeIds.add(topic.themeId);
          }
          if (topic.focusId) {
            usedCategoryIds.add(topic.focusId);
          }
        });
      });

      // ✅ PRIORIDADE 3: Se não há tópicos, usar cache centralizado para especialidades
      if (usedCategoryIds.size === 0) {
        // Tentar buscar do cache centralizado primeiro
        const cachedCategories = queryClient.getQueryData(['study-categories-all']) as any[];
        if (cachedCategories) {
          const specialties = cachedCategories
            .filter(cat => cat.type === 'specialty')
            .slice(0, 20)
            .map(category => ({
              id: category.id,
              name: category.name,
              type: category.type,
              parentId: category.parent_id
            }));

          if (specialties.length > 0) {
            return specialties as FilterOption[];
          }
        }

        // ✅ ÚLTIMO RECURSO: Query direta apenas se cache não disponível
        const { data, error } = await supabase
          .from("study_categories")
          .select("id, name, type, parent_id")
          .eq("type", "specialty")
          .order("name")
          .limit(20);

        if (error) {
          throw error;
        }

        return data.map(category => ({
          id: category.id,
          name: category.name,
          type: category.type,
          parentId: category.parent_id
        })) as FilterOption[];
      }

      // Estratégia de carregamento otimizada:
      // 1. Carregar categorias usadas
      // 2. Carregar temas filhos das especialidades usadas
      // 3. Carregar focos filhos dos temas usados

      const queries = [];
      const asyncResults = [];

      // Query 1: Categorias já usadas
      if (usedCategoryIds.size > 0) {
        const categoryIdsArray = Array.from(usedCategoryIds);

        // ✅ SOLUÇÃO: Usar RPC quando há muitos IDs para evitar URLs muito longas
        if (categoryIdsArray.length > 50 && categoryIdsArray.length <= 1000) {


          // Executar RPC com fallback para chunking
          const rpcPromise = (async () => {
            try {
              const rpcResult = await supabase.rpc('get_categories_by_ids', {
                category_ids: categoryIdsArray
              });

              if (rpcResult.error) {

                const chunkData = await fetchCategoriesWithChunking(categoryIdsArray);
                return { data: chunkData, error: null };
              }

              return rpcResult;
            } catch (error) {

              const chunkData = await fetchCategoriesWithChunking(categoryIdsArray);
              return { data: chunkData, error: null };
            }
          })();

          asyncResults.push(rpcPromise);
        } else {
          // Usar query normal para poucos IDs
          queries.push(
            supabase
              .from("study_categories")
              .select("id, name, type, parent_id")
              .in("id", categoryIdsArray)
          );
        }
      }

      // Query 2: Temas das especialidades usadas (para permitir expansão)
      if (usedSpecialtyIds.size > 0) {
        const specialtyIdsArray = Array.from(usedSpecialtyIds);

        // ✅ OTIMIZAÇÃO: Limitar número de especialidades para evitar URLs longas
        if (specialtyIdsArray.length > 30) {

          specialtyIdsArray.splice(30); // Manter apenas as primeiras 30
        }

        queries.push(
          supabase
            .from("study_categories")
            .select("id, name, type, parent_id")
            .eq("type", "theme")
            .in("parent_id", specialtyIdsArray)
            .limit(50) // Limitar para performance
        );
      }

      // Query 3: Focos dos temas usados (para permitir expansão)
      if (usedThemeIds.size > 0) {
        const themeIdsArray = Array.from(usedThemeIds);

        // ✅ OTIMIZAÇÃO: Limitar número de temas para evitar URLs longas
        if (themeIdsArray.length > 40) {

          themeIdsArray.splice(40); // Manter apenas os primeiros 40
        }

        queries.push(
          supabase
            .from("study_categories")
            .select("id, name, type, parent_id")
            .eq("type", "focus")
            .in("parent_id", themeIdsArray)
            .limit(100) // Limitar para performance
        );
      }

      // Executar queries em paralelo (incluindo RPC/chunking assíncronos)
      const allPromises = [...queries, ...asyncResults];
      const results = await Promise.all(allPromises);
      
      // Combinar resultados
      const allCategories: FilterOption[] = [];
      const seenIds = new Set<string>();

      results.forEach(result => {
        if (result.data) {
          result.data.forEach(category => {
            if (!seenIds.has(category.id)) {
              seenIds.add(category.id);
              allCategories.push({
                id: category.id,
                name: category.name,
                type: category.type,
                parentId: category.parent_id
              });
            }
          });
        }
      });

      // Verificar se há erros
      const errors = results.filter(result => result.error);
      if (errors.length > 0) {
        throw errors[0].error;
      }

      return allCategories;
    },
    staleTime: 10 * 60 * 1000, // 10 minutos de cache
    cacheTime: 30 * 60 * 1000, // 30 minutos no cache
    enabled: true, // Sempre habilitado, mas com lógica condicional interna
    retry: 2,
    retryDelay: 1000
  });
};

/**
 * Hook para carregar categorias completas quando necessário (ex: criação de tópicos)
 * ✅ OTIMIZADO: Usa dados consolidados quando disponíveis
 */
export const useFullCategories = (enabled: boolean = false) => {
  return useQuery({
    queryKey: ["study-categories-full"],
    queryFn: async (): Promise<FilterOption[]> => {
  
      const { data, error } = await supabase
        .from("study_categories")
        .select("id, name, type, parent_id")
        .order("type, name")
        .limit(2000); // Limite aumentado para incluir todas as categorias

      if (error) {

        throw error;
      }



      return data.map(category => ({
        id: category.id,
        name: category.name,
        type: category.type,
        parentId: category.parent_id
      })) as FilterOption[];
    },
    staleTime: 30 * 60 * 1000, // 30 minutos de cache (mais agressivo)
    gcTime: 2 * 60 * 60 * 1000, // 2 horas no cache
    enabled,
    retry: 1,
    retryDelay: 2000
  });
};

/**
 * Hook para carregar categorias por demanda (lazy loading)
 */
export const useLazyCategoriesByType = (type: 'specialty' | 'theme' | 'focus', parentId?: string) => {
  return useQuery({
    queryKey: ["study-categories-lazy", type, parentId],
    queryFn: async (): Promise<FilterOption[]> => {
      let query = supabase
        .from("study_categories")
        .select("id, name, type, parent_id")
        .eq("type", type)
        .order("name");

      if (parentId) {
        query = query.eq("parent_id", parentId);
      }

      const { data, error } = await query.limit(50); // Limitar para performance

      if (error) {
        throw error;
      }

      return data.map(category => ({
        id: category.id,
        name: category.name,
        type: category.type,
        parentId: category.parent_id
      })) as FilterOption[];
    },
    staleTime: 10 * 60 * 1000, // 10 minutos de cache
    cacheTime: 30 * 60 * 1000, // 30 minutos no cache
    enabled: !!type,
    retry: 2,
    retryDelay: 1000
  });
};
