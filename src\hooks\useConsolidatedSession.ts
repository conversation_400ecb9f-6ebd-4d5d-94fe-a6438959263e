import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

/**
 * 🎯 HOOK CONSOLIDADO PARA SESSÃO ATIVA
 * 
 * Substitui múltiplas queries separadas por uma única query otimizada
 * Elimina duplicações de study_sessions, session_events, user_answers
 */

export interface ConsolidatedSessionData {
  // Dados da sessão
  session: {
    id: string;
    title: string;
    user_id: string;
    status: string;
    total_questions: number;
    current_question_index: number;
    questions: string[];
    stats: any;
    started_at: string;
    completed_at: string | null;
    specialty_id: string | null;
    theme_id: string | null;
    focus_id: string | null;
    knowledge_domain: string;
    question_times: any;
    total_time_spent: number;
  } | null;

  // Eventos da sessão (múltipla escolha)
  sessionEvents: {
    question_id: string;
    response_status: boolean;
    response: any;
  }[];

  // Respostas do usuário (todas as questões)
  userAnswers: {
    question_id: string;
    is_correct: boolean;
    text_answer: string | null;
    selected_answer: string | null;
    ai_analyzed: boolean;
  }[];

  // Sessão em progresso (se houver)
  activeSession: any | null;
}

/**
 * Hook principal para dados consolidados da sessão
 */
export const useConsolidatedSession = (sessionId: string | null) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['consolidated-session-data', sessionId, user?.id],
    queryFn: async (): Promise<ConsolidatedSessionData> => {
      if (!sessionId || !user?.id) {
        return {
          session: null,
          sessionEvents: [],
          userAnswers: [],
          activeSession: null
        };
      }

      // ✅ QUERY ÚNICA CONSOLIDADA para todos os dados da sessão
      const [sessionResult, eventsResult, answersResult, activeSessionResult] = await Promise.allSettled([
        // 1. Dados completos da sessão
        supabase
          .from('study_sessions')
          .select(`
            id,
            title,
            user_id,
            status,
            total_questions,
            current_question_index,
            questions,
            stats,
            started_at,
            completed_at,
            specialty_id,
            theme_id,
            focus_id,
            knowledge_domain,
            question_times,
            total_time_spent
          `)
          .eq('id', sessionId)
          .single(),

        // 2. Eventos da sessão (múltipla escolha)
        supabase
          .from('session_events')
          .select('question_id, response_status, response')
          .eq('session_id', sessionId),

        // 3. Respostas do usuário (todas as questões)
        supabase
          .from('user_answers')
          .select('question_id, is_correct, text_answer, selected_answer, ai_analyzed')
          .eq('session_id', sessionId)
          .eq('user_id', user.id),

        // 4. Sessão ativa (se houver)
        supabase
          .from('study_sessions')
          .select('*')
          .eq('user_id', user.id)
          .eq('status', 'in_progress')
          .order('started_at', { ascending: false })
          .limit(1)
      ]);

      // Processar resultados
      const session = sessionResult.status === 'fulfilled' ? sessionResult.value.data : null;
      const sessionEvents = eventsResult.status === 'fulfilled' ? eventsResult.value.data || [] : [];
      const userAnswers = answersResult.status === 'fulfilled' ? answersResult.value.data || [] : [];
      const activeSession = activeSessionResult.status === 'fulfilled' ? 
        (activeSessionResult.value.data?.[0] || null) : null;

      return {
        session,
        sessionEvents,
        userAnswers,
        activeSession
      };
    },
    enabled: !!sessionId && !!user?.id,
    staleTime: 30 * 1000, // 30 segundos
    gcTime: 2 * 60 * 1000, // 2 minutos
  });
};

/**
 * Hook para buscar apenas dados básicos da sessão (mais leve)
 */
export const useSessionBasicData = (sessionId: string | null) => {
  const { data, isLoading, error } = useConsolidatedSession(sessionId);

  return {
    session: data?.session,
    isLoading,
    error
  };
};

/**
 * Hook para buscar apenas sessão ativa
 */
export const useActiveSession = () => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['active-session', user?.id],
    queryFn: async () => {
      if (!user?.id) return null;

      const { data, error } = await supabase
        .from('study_sessions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'in_progress')
        .order('started_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error) throw error;
      return data;
    },
    enabled: !!user?.id,
    staleTime: 30 * 1000,
    gcTime: 2 * 60 * 1000,
  });
};

/**
 * Hook para invalidar caches relacionados à sessão
 */
export const useSessionCacheInvalidation = () => {
  const queryClient = useQueryClient();

  const invalidateSessionCaches = (sessionId?: string) => {
    queryClient.invalidateQueries({
      predicate: (query) => {
        const key = query.queryKey[0] as string;
        return key === 'consolidated-session-data' ||
               key === 'active-session' ||
               key === 'study-sessions-consolidated' ||
               (sessionId && query.queryKey.includes(sessionId));
      }
    });
  };

  return { invalidateSessionCaches };
};
