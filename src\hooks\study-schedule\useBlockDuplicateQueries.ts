/**
 * 🎯 HOOK PARA BLOQUEAR QUERIES DUPLICADAS
 * 
 * Bloqueia queries desnecessárias quando dados consolidados estão disponíveis
 */
import { useQuery } from '@tanstack/react-query';
import { useConsolidatedScheduleData } from './useConsolidatedScheduleData';

export const useBlockDuplicateQueries = () => {
  const { data: consolidatedData, isLoading: consolidatedLoading } = useConsolidatedScheduleData();

  // Verificar se dados consolidados estão prontos
  const isConsolidatedReady = !consolidatedLoading && consolidatedData?.schedules.allSchedules.length > 0;

  return {
    isConsolidatedReady,
    shouldBlockQuery: (queryType: 'study_schedules' | 'study_schedule_items') => {
      if (isConsolidatedReady) {
        console.log(`🚫 [useBlockDuplicateQueries] BLOQUEANDO query duplicada para ${queryType} - dados consolidados prontos`);
        return true;
      }
      return false;
    }
  };
};
