import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useStudyCategories } from '@/hooks/useMedicalSpecialties';

/**
 * 🎯 HOOK CENTRALIZADO FINAL PARA STUDY CATEGORIES
 * 
 * Este hook FORÇA o uso do cache centralizado e elimina TODAS as duplicações
 * Substitui: useOptimizedCategories, useConsolidatedScheduleData fallbacks, etc.
 */

export interface CentralizedCategory {
  id: string;
  name: string;
  type: 'specialty' | 'theme' | 'focus';
  parent_id: string | null;
}

/**
 * Hook principal que sempre usa cache centralizado primeiro
 */
export const useCentralizedCategories = () => {
  const { data: allCategories, isLoading, error } = useStudyCategories();

  return {
    data: allCategories || [],
    isLoading,
    error,
    
    // Helpers para filtrar por tipo
    specialties: allCategories?.filter(cat => cat.type === 'specialty') || [],
    themes: allCategories?.filter(cat => cat.type === 'theme') || [],
    focuses: allCategories?.filter(cat => cat.type === 'focus') || [],
  };
};

/**
 * Hook para especialidades que SEMPRE usa cache
 */
export const useCentralizedSpecialties = () => {
  const { specialties, isLoading, error } = useCentralizedCategories();
  
  return {
    data: specialties,
    isLoading,
    error
  };
};

/**
 * Hook para categorias por tipo com cache inteligente
 */
export const useCategoriesByType = (type: 'specialty' | 'theme' | 'focus', parentId?: string) => {
  const { data: allCategories, isLoading, error } = useCentralizedCategories();
  
  const filteredCategories = allCategories.filter(cat => {
    if (cat.type !== type) return false;
    if (parentId && cat.parent_id !== parentId) return false;
    return true;
  });

  return {
    data: filteredCategories,
    isLoading,
    error
  };
};

/**
 * Hook que força cache ou faz query única se necessário
 */
export const useForceCache = (queryKey: string[], fallbackQueryFn?: () => Promise<any>) => {
  const queryClient = useQueryClient();
  
  return useQuery({
    queryKey: [`force-cache-${queryKey.join('-')}`],
    queryFn: async () => {
      // Sempre tentar cache primeiro
      const cachedData = queryClient.getQueryData(queryKey);
      
      if (cachedData) {
        return cachedData;
      }
      
      // Se não há cache e há fallback, usar fallback
      if (fallbackQueryFn) {
        const data = await fallbackQueryFn();
        // Salvar no cache principal para próximas vezes
        queryClient.setQueryData(queryKey, data);
        return data;
      }
      
      // Se não há cache nem fallback, retornar vazio
      return [];
    },
    staleTime: 60 * 60 * 1000, // 1 hora
    gcTime: 2 * 60 * 60 * 1000, // 2 horas
  });
};
