import { useState, useEffect, useRef, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

/**
 * 🎯 SISTEMA DE TIMER SIMPLES E FUNCIONAL
 * 
 * LÓGICA:
 * - Timer visual: savedTime + (agora - pageEntryTime)
 * - Salvamento: APENAS quando marca resposta
 * - Recuperação: Retoma do último tempo salvo
 */

interface SessionTimeState {
  sessionId: string;
  pageEntryTime: number; // Quando entrou na página
  savedTotalTime: number; // Tempo salvo no banco
  currentQuestionId: string | null;
  questionTimes: Record<string, { startTime: number; totalTime: number }>; // ✅ Rastrear tempo por questão
}

const STORAGE_KEY_PREFIX = 'simple_timer_';

export const useRobustSessionTimer = (sessionId: string, userId: string) => {
  const [state, setState] = useState<SessionTimeState>(() => {
    const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
    const saved = localStorage.getItem(storageKey);

    if (saved) {
      try {
        const parsed = JSON.parse(saved);

        return {
          ...parsed,
          pageEntryTime: Date.now(), // Reset tempo de entrada
          questionTimes: parsed.questionTimes || {} // ✅ Garantir que questionTimes existe
        };
      } catch (error) {
        console.error('❌ [SimpleTimer] Erro ao parsear:', error);
      }
    }

    // ✅ LIMPEZA: Log removido - estado inicial criado
    return {
      sessionId,
      pageEntryTime: Date.now(),
      savedTotalTime: 0,
      currentQuestionId: null,
      questionTimes: {} // ✅ Inicializar tempos das questões
    };
  });

  const [, forceUpdate] = useState(0); // ✅ Para forçar re-render
  const isActiveRef = useRef<boolean>(true);

  /**
   * 💾 SALVAR NO LOCALSTORAGE
   */
  const saveToLocalStorage = useCallback((newState: SessionTimeState) => {
    try {
      const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
      localStorage.setItem(storageKey, JSON.stringify(newState));
    } catch (error) {
      // Erro silencioso
    }
  }, [sessionId]);

  /**
   * 💾 SALVAR NO BANCO
   */
  const saveToDatabase = useCallback(async (timeToSave: number) => {
    try {
      // ✅ LIMPEZA: Log removido - salvamento de timer

      const { error } = await supabase
        .from('study_sessions')
        .update({
          total_time_spent: Math.floor(timeToSave),
          last_activity: new Date().toISOString()
        })
        .eq('id', sessionId);

      if (error) throw error;
      // ✅ LIMPEZA: Log removido - salvamento bem-sucedido
    } catch (error) {
      console.error('❌ [SimpleTimer] Erro ao salvar:', error);
    }
  }, [sessionId]);

  /**
   * 📊 CALCULAR TEMPO VISUAL
   */
  const calculateVisualTime = useCallback((): number => {
    const now = Date.now();
    const timeSinceEntry = Math.floor((now - state.pageEntryTime) / 1000);
    const visualTime = state.savedTotalTime + timeSinceEntry;

    return visualTime;
  }, [state]);

  /**
   * 🔄 CARREGAR TEMPO SALVO
   */
  const loadFromDatabase = useCallback(async () => {
    try {
      const { data: session, error } = await supabase
        .from('study_sessions')
        .select('total_time_spent')
        .eq('id', sessionId)
        .single();

      if (error || !session) return;

      setState(prev => ({
        ...prev,
        savedTotalTime: session.total_time_spent || 0
      }));

      // ✅ LIMPEZA: Log removido - tempo carregado
    } catch (error) {
      console.error('❌ [SimpleTimer] Erro ao carregar:', error);
    }
  }, [sessionId]);

  /**
   * ⏰ INICIAR TIMER PARA QUESTÃO
   */
  const startQuestionTimer = useCallback((questionId: string) => {
    const now = Date.now();

    setState(prev => ({
      ...prev,
      currentQuestionId: questionId,
      questionTimes: {
        ...prev.questionTimes,
        [questionId]: {
          startTime: now,
          totalTime: prev.questionTimes[questionId]?.totalTime || 0 // ✅ Manter tempo acumulado
        }
      }
    }));

    isActiveRef.current = true;
    // ✅ LIMPEZA: Log removido - timer iniciado
  }, []);

  /**
   * ⏸️ PAUSAR TIMER
   */
  const pauseCurrentTimer = useCallback(() => {
    setState(prev => {
      const newQuestionTimes = { ...prev.questionTimes };

      // ✅ Salvar tempo acumulado da questão atual
      if (prev.currentQuestionId && newQuestionTimes[prev.currentQuestionId]) {
        const questionData = newQuestionTimes[prev.currentQuestionId];
        const timeSpent = Math.floor((Date.now() - questionData.startTime) / 1000);
        newQuestionTimes[prev.currentQuestionId] = {
          ...questionData,
          totalTime: questionData.totalTime + timeSpent
        };
      }

      return {
        ...prev,
        currentQuestionId: null,
        questionTimes: newQuestionTimes
      };
    });

    console.log('⏸️ [SimpleTimer] Timer pausado');
  }, []);

  /**
   * 💾 SALVAR QUANDO MARCAR RESPOSTA
   */
  const saveOnAnswer = useCallback(() => {
    const currentVisualTime = calculateVisualTime();

    // Salvar no banco primeiro
    saveToDatabase(currentVisualTime);

    // Reset imediato mas compensando o tempo de processamento
    const resetTime = Date.now();

    setState(prev => {
      const newQuestionTimes = { ...prev.questionTimes };

      // ✅ Salvar tempo acumulado da questão atual antes do reset
      if (prev.currentQuestionId && newQuestionTimes[prev.currentQuestionId]) {
        const questionData = newQuestionTimes[prev.currentQuestionId];
        const timeSpent = Math.floor((Date.now() - questionData.startTime) / 1000);
        newQuestionTimes[prev.currentQuestionId] = {
          ...questionData,
          totalTime: questionData.totalTime + timeSpent
        };
      }

      const newState = {
        ...prev,
        savedTotalTime: currentVisualTime,
        pageEntryTime: resetTime,
        questionTimes: newQuestionTimes
      };
      saveToLocalStorage(newState);
      return newState;
    });
  }, [calculateVisualTime, saveToDatabase, saveToLocalStorage, state]);

  /**
   * 🏁 FINALIZAR SESSÃO
   */
  const finishSession = useCallback(async () => {
    // ✅ LIMPEZA: Log removido - finish session

    // ✅ NÃO salvar automaticamente - apenas limpar
    const storageKey = `${STORAGE_KEY_PREFIX}${sessionId}`;
    localStorage.removeItem(storageKey);

    // ✅ LIMPEZA: Log removido - sessão finalizada
  }, [sessionId]);

  /**
   * 🔄 EFEITOS
   */
  
  // Carregar tempo salvo na inicialização
  useEffect(() => {
    loadFromDatabase();
  }, [loadFromDatabase]);

  // ✅ TIMER VISUAL - Atualizar a cada segundo para mostrar tempo em tempo real
  useEffect(() => {
    const interval = setInterval(() => {
      forceUpdate(prev => prev + 1); // ✅ Força re-render para atualizar timer visual
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Salvar estado no localStorage periodicamente (sem forçar re-render)
  useEffect(() => {
    const interval = setInterval(() => {
      saveToLocalStorage(state);
    }, 5000);

    return () => clearInterval(interval);
  }, [state, saveToLocalStorage]);

  return {
    startQuestionTimer,
    pauseCurrentTimer,
    finishSession,
    saveOnAnswer,
    getVisualTime: calculateVisualTime,
    getSavedTime: () => state.savedTotalTime,
    getQuestionTime: (questionId: string) => {
      const questionData = state.questionTimes[questionId];
      if (!questionData) return 0;

      const currentTime = state.currentQuestionId === questionId
        ? Math.floor((Date.now() - questionData.startTime) / 1000)
        : 0;

      return questionData.totalTime + currentTime;
    }, // ✅ Retornar tempo real da questão
    getTotalSessionTime: calculateVisualTime, // Compatibilidade
    getActiveStudyTime: () => state.savedTotalTime, // Compatibilidade
    isActive: isActiveRef.current,
    _debugState: process.env.NODE_ENV === 'development' ? {
      ...state,
      visualTime: calculateVisualTime()
    } : undefined
  };
};
