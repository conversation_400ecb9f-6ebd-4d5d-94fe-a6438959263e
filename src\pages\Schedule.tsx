import React, { useState, useC<PERSON>back, useMemo, useEffect, memo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Plus, Sparkles, Calendar, ArrowLeft, Info, MessageCircle, Heart, Target, CheckCircle, BookOpen, X } from "lucide-react";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { SimpleStudySchedule } from "@/components/progress/SimpleStudySchedule";
import { useScheduleQuery } from "@/hooks/study-schedule/useScheduleQuery";
import { useAIScheduleQuery } from "@/hooks/study-schedule/useAIScheduleQuery";
import { useStudyTopics } from "@/hooks/study-schedule/useStudyTopics";

import { AddWeeksDialog } from "@/components/progress/AddWeeksDialog";
import { AIScheduleDialogSimple as AIScheduleDialog } from "@/components/progress/ai-schedule/AIScheduleDialogSimple";
import type { AIScheduleOptions } from '@/types/study-schedule';
import { motion } from "framer-motion";
import { PatternBackground } from "@/components/ui/pattern-background";
import { useNavigate, useLocation } from "react-router-dom";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { useScheduleTutorial } from "@/hooks/useScheduleTutorial";
import { ScheduleTutorialDialog } from "@/components/schedule/ScheduleTutorialDialog";
import TimelineWeekView from "@/components/schedule/TimelineWeekView";

const SchedulePage = () => {
  // Logs de debug removidos - problema resolvido com sistema independente

  // ✅ TODOS OS HOOKS DEVEM VIR ANTES DE QUALQUER RETURN CONDICIONAL
  const navigate = useNavigate();
  const location = useLocation();
  const [isAddWeeksDialogOpen, setIsAddWeeksDialogOpen] = useState(false);
  const [isAIScheduleDialogOpen, setIsAIScheduleDialogOpen] = useState(false);
  const [isFeedbackDialogOpen, setIsFeedbackDialogOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // ✅ DETECTAR MODO TUTORIAL GUIADO
  const urlParams = new URLSearchParams(location.search);
  const isGuided = urlParams.get('guided') === 'true';
  const fromTutorial = urlParams.get('from') === 'tutorial';

  // ✅ HOOK DO TUTORIAL DE CRONOGRAMA
  const { tutorialCompleted, markTutorialCompleted, isLoading: tutorialLoading } = useScheduleTutorial();

  // ✅ NOVO: Função para completar tutorial e fechar dialog definitivamente
  const handleTutorialComplete = useCallback(async () => {
    await markTutorialCompleted();
    setShowTutorialDialog(false);
    setTutorialDialogShown(true);
  }, [markTutorialCompleted]);

  const {
    weeklySchedule,
    isLoading: scheduleLoading,
    addWeeks,
    markTopicAsStudied,
    updateTopic
  } = useScheduleQuery();

  // ✅ Hook direto para API sem React Query
  const { markTopicAsStudied: directMarkTopicAsStudied } = useStudyTopics();



  // ✅ CALLBACKS MEMOIZADOS
  const handleTopicClick = useCallback((topic: any) => {
    // Lógica para clique no tópico pode ser adicionada aqui
  }, []);

  const handleMarkAsStudied = useCallback(async (topicId: string) => {
    try {
      // ✅ SOLUÇÃO: Usar hook direto - SEM React Query
      await directMarkTopicAsStudied(topicId);
    } catch (error) {
      console.error('Erro ao marcar tópico como estudado:', error);
    }
  }, [directMarkTopicAsStudied]);

  const handleMarkAsPending = useCallback((topicId: string) => {
    updateTopic({ id: topicId, study_status: 'pending' });
  }, [updateTopic]);

  const {
    generateAISchedule,
    isGenerating: isAIGenerating,
    isComplete: isAIComplete,
    generationStats,
    resetCompletion
  } = useAIScheduleQuery();

  // Logs removidos - usando dialog simples

  const handleAddWeeks = useCallback(async (weeks: number) => {
    try {
      await addWeeks(weeks);
      setIsAddWeeksDialogOpen(false);
    } catch (error) {
      console.error('Erro ao adicionar semanas:', error);
    }
  }, [addWeeks]);

  const handleGenerateAISchedule = useCallback(async (options: AIScheduleOptions) => {
    generateAISchedule(options);
  }, [generateAISchedule]);

  const existingWeeks = useMemo(() =>
    weeklySchedule?.recommendations ?
      [...new Set(weeklySchedule.recommendations.map(day => day.weekNumber))].sort((a, b) => a - b)
      : [],
    [weeklySchedule?.recommendations]
  );

  // ✅ TUTORIAL GUIADO - Controlar dialog do tutorial
  const [showTutorialDialog, setShowTutorialDialog] = useState(false);
  const [tutorialDialogShown, setTutorialDialogShown] = useState(false); // ✅ NOVO: Flag para evitar múltiplas exibições

  useEffect(() => {
    // ✅ CORREÇÃO: Mostrar tutorial apenas se nunca viu antes E não foi mostrado nesta sessão
    const shouldShowTutorial = (!scheduleLoading && tutorialCompleted !== null) &&
                               (tutorialCompleted === false) &&
                               !showTutorialDialog &&
                               !tutorialDialogShown; // ✅ NOVO: Evitar mostrar múltiplas vezes na mesma sessão

    if (shouldShowTutorial) {
      setShowTutorialDialog(true);
      setTutorialDialogShown(true); // ✅ NOVO: Marcar como mostrado

      // Limpar URL params se veio do tutorial
      if (isGuided && fromTutorial) {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, '', newUrl);
      }
    }
  }, [scheduleLoading, tutorialCompleted, showTutorialDialog, tutorialDialogShown]); // ✅ CORREÇÃO: Remover isGuided e fromTutorial das dependências

  // ✅ AGORA SIM PODEMOS TER RETURNS CONDICIONAIS
  // ✅ CORRIGIDO: scheduleLoading agora usa sistema independente (igual IA)
  if (scheduleLoading && !isAIGenerating && !isAddWeeksDialogOpen && !isAIScheduleDialogOpen) {
    return (
      <div className="min-h-screen bg-[#FEF7CD]">
        <Header />
        <StudyNavBar />
        <PatternBackground className="min-h-screen">
          <div className="container mx-auto px-4 pt-24 md:pt-12 pb-8">
            <div className="flex items-center justify-center min-h-[50vh]">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center p-6 sm:p-8 bg-white rounded-xl border-2 border-gray-200 shadow-lg max-w-md"
              >
                <div className="mb-6">
                  <div className="mx-auto w-16 h-16 bg-gradient-to-br from-[#7E69AB] to-[#9B87C4] rounded-full flex items-center justify-center mb-4 shadow-lg border-2 border-black">
                    <Calendar className="h-8 w-8 text-white animate-pulse" />
                  </div>
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#7E69AB] mx-auto mb-4"></div>
                </div>
                <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-2">
                  Carregando cronograma
                </h3>
                <p className="text-sm sm:text-base text-gray-600">
                  Aguarde enquanto buscamos seus dados de estudo...
                </p>
              </motion.div>
            </div>
          </div>
        </PatternBackground>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#FEF7CD]">
      <Header />
      <StudyNavBar />

      <PatternBackground className="min-h-screen">
        {/* Container principal otimizado para mobile */}
        <div className="container mx-auto px-3 sm:px-4 pt-28 md:pt-16 pb-6 space-y-4 sm:space-y-6">

          {/* Navigation ultra compacta */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center justify-between mb-3 sm:mb-4"
          >
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/plataformadeestudos")}
              className="flex items-center gap-1 border border-gray-300 bg-white hover:bg-gray-50 shadow-sm hover:shadow transition-all text-xs h-7 px-2"
            >
              <ArrowLeft className="h-3 w-3" />
              <span className="hidden sm:inline text-xs">Painel</span>
            </Button>

            <div className="flex items-center gap-1">
              <div className="inline-block transform -rotate-2">
                <div className="bg-orange-500 border border-black px-1 py-0.5 text-white font-bold text-[9px] shadow-sm rounded">
                  BETA
                </div>
              </div>
            </div>
          </motion.div>

          {/* Header moderno e compacto */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="relative"
          >
            {/* Título principal redesenhado */}
            <div className="bg-gradient-to-r from-white to-gray-50 border border-gray-200 rounded-2xl p-4 sm:p-6 shadow-lg mb-4">
              <div className="flex items-center justify-between mb-3 sm:mb-4">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <div className="bg-gradient-to-br from-[#7E69AB] to-[#9B87C4] p-2 sm:p-2.5 rounded-xl border border-black shadow-md">
                      <Calendar className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                    </div>
                    <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-[#58CC02] rounded-full border border-black"></div>
                  </div>

                  <div>
                    <h1 className="text-lg sm:text-2xl font-black text-gray-800 leading-tight">
                      Cronograma de Estudos
                    </h1>
                    <p className="text-xs sm:text-sm text-gray-600 font-medium hidden sm:block">
                      Organize sua rotina de estudos de forma inteligente
                    </p>
                  </div>
                </div>

                {/* Botão de info compacto */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsFeedbackDialogOpen(true)}
                  className="border border-blue-300 text-blue-600 hover:bg-blue-50 shadow-sm transition-all h-8 w-8 p-0 sm:w-auto sm:px-3"
                >
                  <Info className="h-3.5 w-3.5" />
                  <span className="hidden sm:inline ml-1 text-xs">Info</span>
                </Button>
              </div>

              {/* Cards explicativos compactos - só no desktop */}
              <div className="hidden sm:grid grid-cols-3 gap-2">
                <div className="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-2 text-center">
                  <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-1">
                    <Sparkles className="h-3 w-3 text-white" />
                  </div>
                  <h3 className="font-bold text-purple-800 text-[10px] mb-0.5">IA Personalizada</h3>
                  <p className="text-purple-700 text-[9px]">Adaptado ao seu perfil</p>
                </div>

                <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-2 text-center">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-1">
                    <Calendar className="h-3 w-3 text-white" />
                  </div>
                  <h3 className="font-bold text-green-800 text-[10px] mb-0.5">Organização</h3>
                  <p className="text-green-700 text-[9px]">Semanas flexíveis</p>
                </div>

                <div className="bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-lg p-2 text-center">
                  <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-1">
                    <Heart className="h-3 w-3 text-white" />
                  </div>
                  <h3 className="font-bold text-orange-800 text-[10px] mb-0.5">Em Beta</h3>
                  <p className="text-orange-700 text-[9px]">Seu feedback importa</p>
                </div>
              </div>

              {/* Versão mobile compacta */}
              <div className="sm:hidden bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-2 mt-3">
                <div className="flex items-center gap-2">
                  <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-[10px] font-bold">β</span>
                  </div>
                  <div>
                    <p className="text-orange-800 text-xs font-bold">Versão Beta</p>
                    <p className="text-orange-700 text-[10px]">Seu feedback é essencial para melhorias</p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Erro */}
          {error && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Erro ao carregar cronograma</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </motion.div>
          )}

          {/* Botões de ação modernos */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="flex flex-col sm:flex-row gap-2 sm:gap-3"
          >
            <Button
              onClick={() => setIsAIScheduleDialogOpen(true)}
              className="bg-gradient-to-r from-[#7E69AB] to-[#9B87C4] hover:from-[#6B5B95] hover:to-[#8A7BB8] text-white border border-black shadow-lg hover:shadow-xl transition-all font-bold h-10 sm:h-11"
              disabled={scheduleLoading || isAIGenerating} // ✅ CORRIGIDO: scheduleLoading independente
            >
              <Sparkles className="h-4 w-4 mr-2" />
              <span className="text-sm">Gerar com IA</span>
              <div className="ml-2 px-1.5 py-0.5 bg-white/20 rounded text-[10px] hidden sm:block">Recomendado</div>
            </Button>
            <Button
              onClick={() => setIsAddWeeksDialogOpen(true)}
              variant="outline"
              className="border border-[#58CC02] text-[#58CC02] hover:bg-[#58CC02] hover:text-white shadow-md hover:shadow-lg transition-all font-bold h-10 sm:h-11"
              disabled={scheduleLoading} // ✅ CORRIGIDO: scheduleLoading independente
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="text-sm">Adicionar Semanas</span>
            </Button>
          </motion.div>

          {/* ✅ TUTORIAL REMOVIDO DA PÁGINA - Agora é um dialog */}

          {/* Vista Timeline da Semana Atual - APENAS DESKTOP/TABLET */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.15 }}
            className="hidden md:block" // ✅ Ocultar no mobile
          >
            <TimelineWeekView
              weeklySchedule={weeklySchedule}
              isLoading={scheduleLoading}
              onTopicClick={handleTopicClick}
              onMarkAsStudied={handleMarkAsStudied}
              onMarkAsPending={handleMarkAsPending}
            />
          </motion.div>



          {/* Cronograma Otimizado */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <SimpleStudySchedule />
          </motion.div>

          {/* Fallback para quando não há cronograma - agora gerenciado pelo OptimizedStudySchedule */}
          {false && (
              <div className="text-center p-6 sm:p-8 bg-gradient-to-br from-white to-gray-50 rounded-xl border-2 border-gray-200 shadow-lg">
                <div className="mb-6">
                  <div className="mx-auto w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-[#7E69AB] to-[#9B87C4] rounded-full flex items-center justify-center mb-4 shadow-lg border-2 border-black">
                    <Calendar className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
                    Seu cronograma está vazio
                  </h3>
                  <p className="text-sm sm:text-base text-gray-600 max-w-md mx-auto mb-6">
                    Crie seu primeiro cronograma de estudos personalizado e organize sua rotina de forma inteligente.
                  </p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-w-lg mx-auto mb-6">
                  <Button
                    onClick={() => setIsAIScheduleDialogOpen(true)}
                    variant="outline"
                    className="border-2 border-[#7E69AB] text-[#7E69AB] hover:bg-[#7E69AB] hover:text-white transition-all duration-300 h-12 sm:h-14 text-sm font-semibold shadow-button hover:translate-y-0.5 hover:shadow-sm"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    <div className="text-center">
                      <div>Gerar com IA</div>
                      <div className="text-xs opacity-70">Personalizado</div>
                    </div>
                  </Button>
                  <Button
                    onClick={() => setIsAddWeeksDialogOpen(true)}
                    className="bg-[#58CC02] hover:bg-[#46a302] text-white h-12 sm:h-14 text-sm font-semibold shadow-button hover:translate-y-0.5 hover:shadow-sm transition-all duration-300 border-2 border-black"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    <div className="text-center">
                      <div>Criar Manual</div>
                      <div className="text-xs opacity-90">Semanas vazias</div>
                    </div>
                  </Button>
                </div>

                <div className="p-3 sm:p-4 bg-blue-50 rounded-lg border-2 border-blue-200">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 border border-blue-600">
                      <span className="text-white text-xs font-bold">💡</span>
                    </div>
                    <div className="text-left">
                      <h4 className="font-semibold text-blue-800 mb-1 text-sm">Dica</h4>
                      <p className="text-blue-700 text-xs sm:text-sm">
                        Recomendamos usar a <strong>IA personalizada</strong> para criar um cronograma
                        otimizado baseado no seu perfil e disponibilidade de tempo.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
        </div>
      </PatternBackground>

      {/* Dialogs */}
      <AddWeeksDialog
        open={isAddWeeksDialogOpen}
        onOpenChange={setIsAddWeeksDialogOpen}
        onSubmit={handleAddWeeks}
        isLoading={scheduleLoading} // ✅ CORRIGIDO: scheduleLoading independente
      />

      <AIScheduleDialog
        open={isAIScheduleDialogOpen}
        onOpenChange={(open) => {
          // Só permite fechar se não estiver carregando
          if (!isAIGenerating) {
            setIsAIScheduleDialogOpen(open);
            // Reset completion state when closing
            if (!open) {
              resetCompletion();
            }
          }
        }}
        onSubmit={handleGenerateAISchedule}
        isLoading={isAIGenerating}
        isComplete={isAIComplete}
        generationStats={generationStats}
        existingWeeks={existingWeeks}
      />

      {/* Dialog de Feedback otimizado */}
      <Dialog open={isFeedbackDialogOpen} onOpenChange={setIsFeedbackDialogOpen}>
        <DialogContent className="sm:max-w-lg border border-black shadow-xl max-h-[85vh] overflow-y-auto">
          <DialogHeader className="pb-3">
            <DialogTitle className="flex items-center gap-2 text-base sm:text-lg font-black">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center border border-black">
                <Heart className="h-3 w-3 sm:h-4 sm:w-4 text-white" />
              </div>
              <span className="text-sm sm:text-base">Cronograma de Estudos - Beta</span>
            </DialogTitle>
            <DialogDescription className="space-y-3 text-left">
              <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-3">
                <div className="font-bold text-orange-800 mb-2 flex items-center gap-2 text-sm">
                  <MessageCircle className="h-3.5 w-3.5" />
                  O que é esta ferramenta?
                </div>
                <div className="text-orange-700 text-xs sm:text-sm mb-2">
                  O <strong>Cronograma de Estudos</strong> organiza sua rotina usando IA para criar cronogramas personalizados.
                </div>

                <div className="space-y-1.5 text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                    <span className="text-orange-700"><strong>IA:</strong> Adaptado ao seu perfil</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    <span className="text-orange-700"><strong>Flexível:</strong> Edite semanas manualmente</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span className="text-orange-700"><strong>Progresso:</strong> Marque estudos concluídos</span>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-lg p-3">
                <div className="font-bold text-red-800 mb-2 flex items-center gap-2 text-sm">
                  <Heart className="h-3.5 w-3.5" />
                  Precisamos do seu feedback!
                </div>
                <div className="text-red-700 text-xs sm:text-sm">
                  Esta ferramenta está em <strong>versão beta</strong>. Seu uso e feedback são <strong>fundamentais</strong> para melhorarmos nas próximas atualizações.
                </div>
              </div>

              <div className="text-center pt-2">
                <Button
                  onClick={() => setIsFeedbackDialogOpen(false)}
                  className="bg-gradient-to-r from-[#7E69AB] to-[#9B87C4] hover:from-[#6B5B95] hover:to-[#8A7BB8] text-white border border-black shadow-lg transition-all font-bold text-sm h-9 sm:h-10"
                >
                  Entendi! Vamos começar
                </Button>
              </div>
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>

      {/* ✅ DIALOG DO TUTORIAL DE CRONOGRAMA */}
      <ScheduleTutorialDialog
        open={showTutorialDialog}
        onOpenChange={(open) => {
          if (!open) {
            setShowTutorialDialog(false);
            setTutorialDialogShown(true); // ✅ NOVO: Marcar como mostrado ao fechar
          }
        }}
        onComplete={handleTutorialComplete}
        isLoading={tutorialLoading}
      />
    </div>
  );
};

export default SchedulePage;
