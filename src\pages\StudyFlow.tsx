import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, Clock, Target, Zap, TrendingUp, Settings, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import Header from '@/components/Header';
import { useNavigate } from 'react-router-dom';
import { studyFlowTheme } from '@/components/studyflow/StudyFlowTheme';
import StudyBlock, { StudyBlockData } from '@/components/studyflow/StudyBlock';



// Mock data para demonstração
const mockStudyData = {
  currentWeek: new Date(),
  studyBlocks: [
    {
      id: '1',
      title: 'Cardiologia: Arritmias',
      specialty: 'Clínica Médica',
      time: '08:00',
      duration: 90,
      day: 'monday',
      completed: false,
      difficulty: 'medium' as const
    },
    {
      id: '2',
      title: 'Neurologia: AVC',
      specialty: 'Neurologia',
      time: '14:00',
      duration: 120,
      day: 'monday',
      completed: true,
      difficulty: 'hard' as const
    },
    {
      id: '3',
      title: 'Pediatria: Vacinação',
      specialty: 'Pediatria',
      time: '10:00',
      duration: 60,
      day: 'tuesday',
      completed: false,
      difficulty: 'easy' as const
    },
    {
      id: '4',
      title: 'Gastroenterologia: Úlcera Péptica',
      specialty: 'Clínica Médica',
      time: '16:00',
      duration: 75,
      day: 'tuesday',
      completed: false,
      difficulty: 'medium' as const
    },
    {
      id: '5',
      title: 'Ortopedia: Fraturas',
      specialty: 'Cirurgia',
      time: '09:00',
      duration: 105,
      day: 'wednesday',
      completed: true,
      difficulty: 'hard' as const
    }
  ] as StudyBlockData[],
  streak: 7,
  weekProgress: 65,
  totalHours: 12.5
};

const StudyFlow: React.FC = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'timeline' | 'focus' | 'heatmap'>('timeline');
  const [selectedWeek, setSelectedWeek] = useState(new Date());

  // Animação de entrada
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: studyFlowTheme.colors.background }}>
      <Header />
      
      <motion.div 
        className="container mx-auto px-4 py-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Header com navegação */}
        <motion.div 
          className="flex items-center justify-between mb-8"
          variants={itemVariants}
        >
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/schedule')}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4" />
              Cronograma Atual
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">StudyFlow</h1>
              <p className="text-gray-600">Cronograma revolucionário em desenvolvimento</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 px-3 py-2 bg-white rounded-lg border border-gray-200">
              <Zap className="h-4 w-4 text-orange-500" />
              <span className="text-sm font-medium">{mockStudyData.streak} dias</span>
            </div>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </motion.div>

        {/* Modo de visualização */}
        <motion.div 
          className="flex items-center gap-2 mb-6"
          variants={itemVariants}
        >
          {(['timeline', 'focus', 'heatmap'] as const).map((mode) => (
            <Button
              key={mode}
              variant={viewMode === mode ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode(mode)}
              className={viewMode === mode ? 'bg-indigo-600 hover:bg-indigo-700' : ''}
            >
              {mode === 'timeline' && <Calendar className="h-4 w-4 mr-2" />}
              {mode === 'focus' && <Target className="h-4 w-4 mr-2" />}
              {mode === 'heatmap' && <TrendingUp className="h-4 w-4 mr-2" />}
              {mode.charAt(0).toUpperCase() + mode.slice(1)}
            </Button>
          ))}
        </motion.div>

        {/* Conteúdo principal baseado no modo */}
        <AnimatePresence mode="wait">
          {viewMode === 'timeline' && (
            <motion.div
              key="timeline"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <TimelineView studyData={mockStudyData} />
            </motion.div>
          )}
          
          {viewMode === 'focus' && (
            <motion.div
              key="focus"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <FocusView studyData={mockStudyData} />
            </motion.div>
          )}
          
          {viewMode === 'heatmap' && (
            <motion.div
              key="heatmap"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <HeatmapView studyData={mockStudyData} />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Banner de desenvolvimento */}
        <motion.div 
          className="mt-8 p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl"
          variants={itemVariants}
        >
          <div className="flex items-center gap-3 mb-3">
            <div className="p-2 bg-indigo-100 rounded-full">
              <Zap className="h-5 w-5 text-indigo-600" />
            </div>
            <h3 className="text-lg font-semibold text-indigo-900">
              🚀 Experiência Revolucionária em Desenvolvimento
            </h3>
          </div>
          <p className="text-indigo-700 mb-4">
            Esta é uma prévia do novo StudyFlow - um cronograma completamente repensado com 
            interface moderna, drag & drop intuitivo, gamificação e IA integrada.
          </p>
          <div className="flex flex-wrap gap-2">
            <span className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm font-medium">
              Timeline Horizontal
            </span>
            <span className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm font-medium">
              Drag & Drop
            </span>
            <span className="px-3 py-1 bg-green-100 text-green-700 rounded-full text-sm font-medium">
              Gamificação
            </span>
            <span className="px-3 py-1 bg-orange-100 text-orange-700 rounded-full text-sm font-medium">
              IA Integrada
            </span>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

// Componente Timeline View
const TimelineView: React.FC<{ studyData: any }> = ({ studyData }) => {
  const [draggedBlock, setDraggedBlock] = useState<string | null>(null);

  const handleBlockClick = (id: string) => {
    console.log('Block clicked:', id);
  };

  const handleToggleComplete = (id: string) => {
    console.log('Toggle complete:', id);
  };

  const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  const dayNames = ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'];

  return (
    <Card className="p-6 bg-white border border-gray-200 rounded-xl shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold">Vista Timeline</h3>
        <div className="text-sm text-gray-500">
          Semana de {new Date().toLocaleDateString('pt-BR')}
        </div>
      </div>

      {/* Grid da timeline */}
      <div className="grid grid-cols-7 gap-4">
        {days.map((day, index) => {
          const dayBlocks = studyData.studyBlocks.filter((block: StudyBlockData) => block.day === day);

          return (
            <div key={day} className="space-y-3">
              {/* Header do dia */}
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="font-semibold text-sm text-gray-900">
                  {dayNames[index]}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {new Date(Date.now() + index * 24 * 60 * 60 * 1000).getDate()}
                </div>
              </div>

              {/* Blocos de estudo do dia */}
              <div className="space-y-2 min-h-[200px]">
                {dayBlocks.map((block: StudyBlockData) => (
                  <StudyBlock
                    key={block.id}
                    data={block}
                    isDragging={draggedBlock === block.id}
                    onDragStart={setDraggedBlock}
                    onDragEnd={() => setDraggedBlock(null)}
                    onClick={handleBlockClick}
                    onToggleComplete={handleToggleComplete}
                  />
                ))}

                {/* Área de drop vazia */}
                {dayBlocks.length === 0 && (
                  <div className="h-32 border-2 border-dashed border-gray-200 rounded-lg flex items-center justify-center text-gray-400 text-sm">
                    Nenhum estudo
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </Card>
  );
};

// Componente Focus View
const FocusView: React.FC<{ studyData: any }> = ({ studyData }) => {
  const today = new Date().toLocaleDateString('pt-BR', { weekday: 'long' }).toLowerCase();
  const todayBlocks = studyData.studyBlocks.filter((block: StudyBlockData) =>
    block.day === 'monday' // Simulando segunda-feira como hoje
  );

  const completedToday = todayBlocks.filter((block: StudyBlockData) => block.completed).length;
  const totalToday = todayBlocks.length;
  const progressPercentage = totalToday > 0 ? (completedToday / totalToday) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Card de progresso do dia */}
      <Card className="p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-xl">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-xl font-semibold text-indigo-900">Foco de Hoje</h3>
            <p className="text-indigo-700">Segunda-feira, {new Date().toLocaleDateString('pt-BR')}</p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-indigo-900">{completedToday}/{totalToday}</div>
            <div className="text-sm text-indigo-700">estudos concluídos</div>
          </div>
        </div>

        {/* Barra de progresso */}
        <div className="w-full bg-indigo-200 rounded-full h-3 mb-4">
          <motion.div
            className="bg-indigo-600 h-3 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 1, ease: "easeOut" }}
          />
        </div>

        <div className="text-sm text-indigo-700">
          {progressPercentage === 100 ? '🎉 Parabéns! Você completou todos os estudos de hoje!' :
           progressPercentage > 50 ? '💪 Você está indo muito bem!' :
           progressPercentage > 0 ? '🚀 Continue assim!' :
           '📚 Vamos começar os estudos!'}
        </div>
      </Card>

      {/* Lista de estudos do dia */}
      <Card className="p-6 bg-white border border-gray-200 rounded-xl shadow-sm">
        <h4 className="text-lg font-semibold mb-4">Estudos de Hoje</h4>

        {todayBlocks.length > 0 ? (
          <div className="space-y-3">
            {todayBlocks.map((block: StudyBlockData) => (
              <StudyBlock
                key={block.id}
                data={block}
                onClick={() => console.log('Focus block clicked:', block.id)}
                onToggleComplete={() => console.log('Focus toggle:', block.id)}
                className="w-full"
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Target className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>Nenhum estudo agendado para hoje</p>
          </div>
        )}
      </Card>
    </div>
  );
};

// Componente Heatmap View
const HeatmapView: React.FC<{ studyData: any }> = ({ studyData }) => {
  // Gerar dados mock para o heatmap (últimos 30 dias)
  const generateHeatmapData = () => {
    const data = [];
    const today = new Date();

    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      // Simular intensidade de estudo (0-4)
      const intensity = Math.floor(Math.random() * 5);

      data.push({
        date: date.toISOString().split('T')[0],
        intensity,
        studies: intensity * 2 + Math.floor(Math.random() * 3)
      });
    }

    return data;
  };

  const heatmapData = generateHeatmapData();

  const getIntensityColor = (intensity: number) => {
    const colors = [
      studyFlowTheme.colors.neutral[100], // 0 - sem estudo
      studyFlowTheme.colors.success[100], // 1 - pouco
      studyFlowTheme.colors.success[300], // 2 - médio
      studyFlowTheme.colors.success[500], // 3 - alto
      studyFlowTheme.colors.success[700]  // 4 - muito alto
    ];
    return colors[intensity] || colors[0];
  };

  return (
    <div className="space-y-6">
      {/* Estatísticas gerais */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4 bg-white border border-gray-200 rounded-xl">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{studyData.streak}</div>
            <div className="text-sm text-gray-600">Dias consecutivos</div>
          </div>
        </Card>

        <Card className="p-4 bg-white border border-gray-200 rounded-xl">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{studyData.totalHours}h</div>
            <div className="text-sm text-gray-600">Esta semana</div>
          </div>
        </Card>

        <Card className="p-4 bg-white border border-gray-200 rounded-xl">
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{studyData.weekProgress}%</div>
            <div className="text-sm text-gray-600">Progresso semanal</div>
          </div>
        </Card>
      </div>

      {/* Heatmap */}
      <Card className="p-6 bg-white border border-gray-200 rounded-xl shadow-sm">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold">Atividade dos Últimos 30 Dias</h3>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>Menos</span>
            <div className="flex gap-1">
              {[0, 1, 2, 3, 4].map(intensity => (
                <div
                  key={intensity}
                  className="w-3 h-3 rounded-sm"
                  style={{ backgroundColor: getIntensityColor(intensity) }}
                />
              ))}
            </div>
            <span>Mais</span>
          </div>
        </div>

        {/* Grid do heatmap */}
        <div className="grid grid-cols-10 gap-1">
          {heatmapData.map((day, index) => (
            <motion.div
              key={day.date}
              className="w-6 h-6 rounded-sm cursor-pointer"
              style={{ backgroundColor: getIntensityColor(day.intensity) }}
              whileHover={{ scale: 1.2 }}
              title={`${day.date}: ${day.studies} estudos`}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.02 }}
            />
          ))}
        </div>

        <div className="mt-4 text-sm text-gray-500">
          Clique em um dia para ver detalhes dos estudos
        </div>
      </Card>
    </div>
  );
};

export default StudyFlow;
