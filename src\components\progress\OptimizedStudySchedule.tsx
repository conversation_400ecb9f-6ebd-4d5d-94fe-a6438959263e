import React, { useEffect, useRef, useCallback, memo } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Calendar, Filter, Loader2, ChevronDown } from "lucide-react";
import { StudySchedule } from "./StudySchedule";
import { useOptimizedSchedule } from "@/hooks/study-schedule/useOptimizedSchedule";
import { motion } from "framer-motion";

interface OptimizedStudyScheduleProps {
  onUpdateTopic?: (topic: any) => void;
  onMarkStudied?: (topicId: string, revisionNumber?: number) => void;
  onDeleteWeek?: (weekNumber: number) => void;
  onDeleteAllWeeks?: () => void;
  onDeleteTopic?: (topicId: string) => void;
}

const OptimizedStudyScheduleComponent = ({
  onUpdateTopic,
  onMarkStudied,
  onDeleteWeek,
  onDeleteAllWeeks,
  onDeleteTopic
}: OptimizedStudyScheduleProps) => {
  // ✅ LIMPEZA: Logs removidos - criação de componente rotineira

  const {
    isLoading,
    weeklySchedule,
    hasMoreWeeks,
    loadedWeeksCount,
    loadInitialSchedule,
    loadMoreWeeks,
    handleMarkStudied,
    handleUpdateTopic,
    handleDeleteWeek,
    handleDeleteAllWeeks,
    handleDeleteTopic
  } = useOptimizedSchedule();

  const loadMoreRef = useRef<HTMLDivElement>(null);
  const hasInitialized = useRef(false);

  // Carregamento inicial
  useEffect(() => {
    console.log('🚀 [OptimizedStudySchedule] useEffect inicial - hasInitialized:', hasInitialized.current);
    console.log('🔍 [OptimizedStudySchedule] loadInitialSchedule dependency changed');
    if (!hasInitialized.current) {
      console.log('🎯 [OptimizedStudySchedule] Primeira inicialização - carregando schedule...');
      hasInitialized.current = true;
      loadInitialSchedule();
    } else {
      console.log('⏸️ [OptimizedStudySchedule] Já inicializado - pulando carregamento');
    }
  }, []); // ✅ Removendo dependência que causava re-execução

  // Intersection Observer para lazy loading
  useEffect(() => {
    if (!hasMoreWeeks || isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          loadMoreWeeks();
        }
      },
      {
        rootMargin: '200px', // Carregar quando estiver 200px antes de aparecer
        threshold: 0.1
      }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => observer.disconnect();
  }, [hasMoreWeeks, isLoading, loadMoreWeeks]);

  // Loading inicial
  if (isLoading && !weeklySchedule) {
    return (
      <Card className="border-0 shadow-lg overflow-hidden bg-gradient-to-b from-white to-gray-50">
        <CardHeader className="pb-2 border-b border-gray-100">
          <CardTitle className="flex items-center gap-2">
            <div className="bg-[#58CC02] p-2 rounded-full">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg sm:text-xl font-bold text-gray-800">Cronograma de Estudos</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <div className="flex flex-col items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-[#58CC02] mb-4" />
            <p className="text-gray-600 text-center">
              Carregando cronograma otimizado...
            </p>
            <p className="text-sm text-gray-500 mt-2">
              Priorizando semana atual e dados essenciais
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sem cronograma
  if (!weeklySchedule || weeklySchedule.recommendations.length === 0) {
    return (
      <Card className="border-0 shadow-lg overflow-hidden bg-gradient-to-b from-white to-gray-50">
        <CardHeader className="pb-2 border-b border-gray-100">
          <CardTitle className="flex items-center gap-2">
            <div className="bg-[#58CC02] p-2 rounded-full">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg sm:text-xl font-bold text-gray-800">Cronograma de Estudos</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <div className="text-center py-12">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-[#7E69AB] to-[#9B87C4] rounded-full flex items-center justify-center mb-4 shadow-lg border-2 border-black">
              <Calendar className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-semibold mb-4 text-gray-800">
              Nenhum cronograma encontrado
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Você ainda não possui um cronograma de estudos criado. 
              Gere um novo cronograma para começar a organizar seus estudos.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Componente principal do cronograma */}
      <StudySchedule
        weeklyPlan={weeklySchedule.recommendations}
        onUpdateTopic={onUpdateTopic || handleUpdateTopic}
        onMarkStudied={onMarkStudied || handleMarkStudied}
        onDeleteWeek={onDeleteWeek || handleDeleteWeek}
        onDeleteAllWeeks={onDeleteAllWeeks || handleDeleteAllWeeks}
        onDeleteTopic={onDeleteTopic || handleDeleteTopic}
      />

      {/* Indicador de carregamento de mais semanas */}
      {hasMoreWeeks && (
        <motion.div
          ref={loadMoreRef}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex flex-col items-center justify-center py-8"
        >
          {isLoading ? (
            <div className="flex items-center gap-3 text-gray-600">
              <Loader2 className="h-5 w-5 animate-spin text-[#58CC02]" />
              <span className="text-sm font-medium">Carregando mais semanas...</span>
            </div>
          ) : (
            <Button
              variant="outline"
              onClick={loadMoreWeeks}
              className="border-dashed border-[#58CC02] text-[#58CC02] hover:bg-[#58CC02]/5"
            >
              <ChevronDown className="h-4 w-4 mr-2" />
              Carregar mais semanas
            </Button>
          )}
        </motion.div>
      )}

      {/* Estatísticas de carregamento (apenas em desenvolvimento) */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm text-blue-700">
              <span>Semanas carregadas: {loadedWeeksCount}</span>
              <span>Mais semanas: {hasMoreWeeks ? 'Sim' : 'Não'}</span>
              <span>Total de dias: {weeklySchedule.recommendations.length}</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

// ✅ Memoização para evitar re-renders desnecessários
export const OptimizedStudySchedule = memo(OptimizedStudyScheduleComponent, (prevProps, nextProps) => {
  // Comparação personalizada das props para evitar re-renders
  const propsEqual =
    prevProps.onUpdateTopic === nextProps.onUpdateTopic &&
    prevProps.onMarkStudied === nextProps.onMarkStudied &&
    prevProps.onDeleteWeek === nextProps.onDeleteWeek &&
    prevProps.onDeleteAllWeeks === nextProps.onDeleteAllWeeks &&
    prevProps.onDeleteTopic === nextProps.onDeleteTopic;

  console.log('🔍 [OptimizedStudySchedule.memo] Props iguais?', propsEqual);
  return propsEqual;
});

export default OptimizedStudySchedule;
