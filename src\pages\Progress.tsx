
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { LearningInsights } from "@/components/progress/LearningInsights";
import { SkillTree } from "@/components/progress/SkillTree";
import { TemporalInsights } from "@/components/progress/TemporalInsights";
import { DifficultyInsights } from "@/components/progress/DifficultyInsights";
import { useUserStatistics } from "@/hooks/useUserStatistics";

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Brain, Target, Flame, BarChart3, HelpCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import Header from "@/components/Header";
import StudyNavBar from "@/components/study/StudyNavBar";
import { useStreakSystem } from '@/hooks/useOptimizedStreakStats';

import { motion } from "framer-motion";
import { PatternBackground } from "@/components/ui/pattern-background";
import { Progress } from "@/components/ui/progress";

const ProgressPage = () => {
  const { data: stats, isLoading, error } = useUserStatistics();
  const { currentStreak, maxStreak, isLoading: streakLoading } = useStreakSystem();




  if (error) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-12" />
        <div className="container mx-auto p-4 pt-16 md:p-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Erro ao carregar progresso</AlertTitle>
            <AlertDescription>
              Não foi possível carregar seus dados de progresso. Por favor,
              tente novamente mais tarde.
            </AlertDescription>
          </Alert>
        </div>
      </>
    );
  }

  if (isLoading) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-12" />
        <div className="container mx-auto p-4 pt-16 md:p-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </>
    );
  }

  if (!stats) {
    return (
      <>
        <Header />
        <StudyNavBar className="mb-12" />
        <div className="container mx-auto p-4 pt-16 md:p-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Nenhuma estatística disponível</AlertTitle>
            <AlertDescription>
              Comece a responder questões para ver suas estatísticas de
              progresso.
            </AlertDescription>
          </Alert>
        </div>
      </>
    );
  }



  const processedStats = {
    accuracy: stats.total_questions > 0
      ? (stats.correct_answers / stats.total_questions) * 100
      : 0,
    averageTime: stats?.avg_response_time || 0,
    totalQuestions: stats?.total_questions || 0, // Questões únicas
    totalAnswers: stats?.total_answers || 0, // ✅ Total de respostas
    maxStreak: stats?.max_streak || 0,
    currentStreak: stats?.streak_days || 0,
    totalTimeSpent: stats?.total_study_time || 0,
    weeklyChange: stats?.weekly_stats
      ? {
          accuracy:
            stats.weekly_stats.current_week.correct +
            stats.weekly_stats.current_week.incorrect > 0
              ? ((stats.weekly_stats.current_week.correct /
                  (stats.weekly_stats.current_week.correct +
                    stats.weekly_stats.current_week.incorrect)) *
                  100 || 0) -
                (stats.weekly_stats.previous_week.correct +
                stats.weekly_stats.previous_week.incorrect > 0
                  ? (stats.weekly_stats.previous_week.correct /
                      (stats.weekly_stats.previous_week.correct +
                        stats.weekly_stats.previous_week.incorrect)) *
                      100 || 0
                  : 0)
              : 0,
          questions:
            stats.weekly_stats.current_week.correct +
            stats.weekly_stats.current_week.incorrect -
            (stats.weekly_stats.previous_week.correct +
             stats.weekly_stats.previous_week.incorrect),
        }
      : {
          accuracy: 0,
          questions: 0,
        },
  };



  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const item = {
    hidden: { y: 20, opacity: 0 },
    show: { y: 0, opacity: 1 }
  };

  // Componente de diálogo de ajuda para sequências
  const StreakHelpDialog = () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-5 w-5 p-0 rounded-full hover:bg-gray-100/50 transition-colors ml-1"
          title="Como funciona a sequência?"
        >
          <HelpCircle className="h-3 w-3 text-gray-400" />
        </Button>
      </DialogTrigger>
      <DialogContent className="w-[80dvw] max-w-md max-h-[80dvh] overflow-y-auto rounded-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Flame className="h-5 w-5 text-orange-500" />
            Sistema de Sequência de Estudos
          </DialogTitle>
        </DialogHeader>
        <div className="space-y-4 text-sm">
          <div>
            <h4 className="font-semibold text-gray-800 mb-2">🔥 O que conta como "Dia de Estudo"?</h4>
            <div className="space-y-2">
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Responder 1+ questão</strong>
                  <p className="text-gray-600">Qualquer questão respondida na plataforma</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Completar 1+ sessão</strong>
                  <p className="text-gray-600">Simulados, revisões ou estudos dirigidos</p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <strong>Marcar cronograma como estudado</strong>
                  <p className="text-gray-600">Itens do seu cronograma pessoal</p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <p className="text-blue-800 text-sm">
              <strong>💡 Dica:</strong> Responder apenas 1 questão por dia já mantém sua sequência ativa!
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-screen bg-[#FEF7CD]">
      <Header />
      <StudyNavBar className="mb-8" />

      <div className="container max-w-6xl mx-auto px-4 pt-16 md:pt-8 space-y-8 animate-fade-in">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative overflow-hidden rounded-xl shadow-lg"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-[#7E69AB] via-[#9b87f5] to-[#6e5bd9] rounded-lg"></div>
          <PatternBackground className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-indigo-500/20"></div>
          </PatternBackground>

          <div className="relative py-5 px-6 md:px-8">
            <div className="flex flex-col md:flex-row justify-between items-center gap-6">
              <div className="flex items-center gap-4">
                <div className="inline-block transform -rotate-2">
                  <div className="bg-hackathon-red border-2 border-white px-4 py-1 text-white font-bold tracking-wide text-sm shadow-md">
                    ACOMPANHE SEU AVANÇO
                  </div>
                </div>

                <div className="z-10">
                  <h1 className="text-4xl font-black text-white drop-shadow-md flex items-center gap-2">
                    <span className="inline-block bg-hackathon-yellow text-black px-3 py-1 transform -rotate-1 border-2 border-black shadow-lg">
                      Seu
                    </span>
                    <span className="text-white">
                      Progresso
                    </span>
                  </h1>
                </div>
              </div>

              <motion.div
                whileHover={{ y: -2 }}
                className="bg-white/90 backdrop-blur-sm rounded-lg border border-purple-200 p-3 shadow-lg flex-shrink-0 w-full md:w-auto"
              >
                <div className="flex items-center gap-2 mb-2">
                  <BarChart3 className="h-4 w-4 text-[#7E69AB]" />
                  <h3 className="font-semibold text-gray-800 text-sm">Resumo de Atividades</h3>
                </div>
                <div className="grid grid-cols-3 gap-3">
                  <div className="flex flex-col">
                    <span className="text-xs text-gray-500">Questões</span>
                    <span className="text-lg font-bold text-gray-800">{stats.total_questions}</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-xs text-gray-500">Acertos</span>
                    <span className="text-lg font-bold text-green-600">{stats.correct_answers}</span>
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-1">
                      <span className="text-xs text-gray-500">Sequência</span>
                      <StreakHelpDialog />
                    </div>
                    <div className="flex items-center gap-1">
                      <Flame className="h-3 w-3 text-orange-500" />
                      <span className="text-sm font-medium">
                        {streakLoading ? '...' : `${currentStreak} dias`}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="mt-2">
                  <div className="flex justify-between items-center text-xs mb-1">
                    <span className="text-gray-600">Precisão</span>
                    <span className="font-medium">{processedStats.accuracy.toFixed(1)}%</span>
                  </div>
                  <Progress value={processedStats.accuracy} className="h-2" />
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>



        <Tabs defaultValue="insights" className="space-y-8">
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white border-2 border-gray-200 rounded-xl p-1 shadow-sm"
          >
            <TabsList className="grid w-full grid-cols-2 gap-2 bg-gray-50 p-1 rounded-lg">
              <TabsTrigger
                value="insights"
                className="rounded-lg py-2.5 data-[state=active]:bg-[#7E69AB] data-[state=active]:text-white font-medium transition-all duration-200"
              >
                <Brain className="h-4 w-4 mr-2" />
                Insights
              </TabsTrigger>
              <TabsTrigger
                value="questions"
                className="rounded-lg py-2.5 data-[state=active]:bg-[#7E69AB] data-[state=active]:text-white font-medium transition-all duration-200"
              >
                <Target className="h-4 w-4 mr-2" />
                Questões
              </TabsTrigger>
            </TabsList>
          </motion.div>

          <TabsContent
            value="insights"
            className="space-y-8 animate-in fade-in-50"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white border-2 border-gray-200 rounded-lg p-6 shadow-sm"
            >
              <LearningInsights stats={processedStats} />
            </motion.div>
          </TabsContent>



          <TabsContent
            value="questions"
            className="space-y-8 animate-in fade-in-50"
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white border-2 border-gray-200 rounded-lg p-6 shadow-sm"
            >
              <div className="flex items-center gap-2 mb-6">
                <Target className="h-5 w-5 text-hackathon-red" />
                <h2 className="text-xl font-semibold">Análise Avançada de Questões</h2>
              </div>

              {/* ✅ Sub-abas simplificadas - apenas 3 abas */}
              <Tabs defaultValue="categories" className="space-y-6">
                <TabsList className="grid w-full grid-cols-3 gap-1 bg-gray-100 p-1 rounded-lg">
                  <TabsTrigger
                    value="categories"
                    className="rounded-md py-2.5 text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium transition-all duration-200"
                  >
                    📊 Por Categoria
                  </TabsTrigger>
                  <TabsTrigger
                    value="temporal"
                    className="rounded-md py-2.5 text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium transition-all duration-200"
                  >
                    📈 Evolução
                  </TabsTrigger>
                  <TabsTrigger
                    value="difficulty"
                    className="rounded-md py-2.5 text-sm data-[state=active]:bg-white data-[state=active]:shadow-sm font-medium transition-all duration-200"
                  >
                    🎯 Inteligência
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="categories" className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Target className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-800">Hierarquia de Categorias</span>
                    </div>
                    <p className="text-xs text-blue-700">
                      <strong>Especialidades</strong> → <strong>Temas</strong> → <strong>Focos</strong>
                      (Ex: Pediatria → Neonatologia → Prematuridade)
                    </p>
                  </div>
                  <SkillTree />
                </TabsContent>

                <TabsContent value="temporal" className="space-y-4">
                  <TemporalInsights />
                </TabsContent>

                <TabsContent value="difficulty" className="space-y-4">
                  <DifficultyInsights />
                </TabsContent>
              </Tabs>
            </motion.div>
          </TabsContent>
        </Tabs>
      </div>


    </div>
  );
};

export default ProgressPage;
