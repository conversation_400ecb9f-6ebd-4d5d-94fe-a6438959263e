import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart3, Download, Filter, TrendingUp, Building2, Calendar, FileText } from 'lucide-react';
import { useInstitutionPrevalence, PrevalenceFilters, InstitutionStats } from '@/hooks/useInstitutionPrevalence';
import { useToast } from '@/hooks/use-toast';
import { PrevalenceChart } from '@/components/admin/PrevalenceChart';
import { PrevalenceTable } from '@/components/admin/PrevalenceTable';
import { InstitutionSelector } from '@/components/study/InstitutionSelector';

export default function PrevalenceAnalysisPage() {
  const [filters, setFilters] = useState<PrevalenceFilters>({
    institutionIds: [],
    startYear: 2020,
    endYear: new Date().getFullYear(),
    domain: 'residencia'
  });
  
  const [results, setResults] = useState<InstitutionStats[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [activeTab, setActiveTab] = useState('specialties');
  
  const { calculatePrevalence, getInstitutions } = useInstitutionPrevalence();
  const { toast } = useToast();

  const handleAnalyze = async () => {
    if (filters.institutionIds.length === 0) {
      toast({
        title: "Erro",
        description: "Selecione pelo menos uma instituição para análise",
        variant: "destructive"
      });
      return;
    }

    setIsAnalyzing(true);
    try {
      const data = await calculatePrevalence(filters);
      setResults(data);
      
      toast({
        title: "Análise concluída",
        description: `Dados processados para ${data.length} instituição(ões)`,
      });
    } catch (error) {
      toast({
        title: "Erro na análise",
        description: "Não foi possível processar os dados",
        variant: "destructive"
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleExportCSV = () => {
    if (results.length === 0) return;

    const csvData = [];
    
    // Header
    csvData.push(['Instituição', 'Categoria', 'Nome', 'Quantidade', 'Percentual']);
    
    // Data
    results.forEach(institution => {
      const categories = {
        'Especialidade': institution.specialties,
        'Tema': institution.themes,
        'Foco': institution.focuses
      };
      
      Object.entries(categories).forEach(([categoryType, items]) => {
        items.forEach(item => {
          csvData.push([
            institution.institution_name,
            categoryType,
            item.specialty_name || item.theme_name || item.focus_name,
            item.question_count,
            `${item.percentage.toFixed(2)}%`
          ]);
        });
      });
    });

    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `prevalencia_${filters.institutionIds.join('_')}_${filters.startYear}-${filters.endYear}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getTotalQuestions = () => {
    return results.reduce((total, institution) => total + institution.total_questions, 0);
  };

  const getCombinedData = (type: 'specialties' | 'themes' | 'focuses') => {
    const combined = new Map<string, { name: string; count: number; percentage: number }>();
    let totalQuestions = 0;

    results.forEach(institution => {
      totalQuestions += institution.total_questions;
      institution[type].forEach(item => {
        const name = item.specialty_name || item.theme_name || item.focus_name;
        const current = combined.get(name) || { name, count: 0, percentage: 0 };
        combined.set(name, {
          ...current,
          count: current.count + item.question_count
        });
      });
    });

    // Recalcular percentuais
    return Array.from(combined.values())
      .map(item => ({
        ...item,
        percentage: totalQuestions > 0 ? (item.count / totalQuestions) * 100 : 0
      }))
      .sort((a, b) => b.percentage - a.percentage)
      .slice(0, 20); // Top 20
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Análise de Prevalência</h1>
          <p className="text-gray-600 mt-2">
            Analise a prevalência de especialidades, temas e focos por instituição
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={handleExportCSV}
            variant="outline"
            disabled={results.length === 0}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Exportar CSV
          </Button>
        </div>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros de Análise
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>Domínio</Label>
              <Select
                value={filters.domain}
                onValueChange={(value) => setFilters(prev => ({ ...prev, domain: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="residencia">Residência Médica</SelectItem>
                  <SelectItem value="oftalmologia">Oftalmologia</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label>Ano Inicial</Label>
              <Input
                type="number"
                value={filters.startYear || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  startYear: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                placeholder="Ex: 2020"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Ano Final</Label>
              <Input
                type="number"
                value={filters.endYear || ''}
                onChange={(e) => setFilters(prev => ({ 
                  ...prev, 
                  endYear: e.target.value ? parseInt(e.target.value) : undefined 
                }))}
                placeholder="Ex: 2024"
              />
            </div>
          </div>

          <div className="mt-4">
            <InstitutionSelector
              selectedInstitutions={filters.institutionIds}
              onInstitutionsChange={(ids) => setFilters(prev => ({ ...prev, institutionIds: ids }))}
              generationMode="institution_based"
              onGenerationModeChange={() => {}} // Não usado aqui
            />
          </div>

          <div className="flex justify-end">
            <Button
              onClick={handleAnalyze}
              disabled={isAnalyzing || filters.institutionIds.length === 0}
              className="flex items-center gap-2"
            >
              {isAnalyzing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Analisando...
                </>
              ) : (
                <>
                  <BarChart3 className="h-4 w-4" />
                  Analisar Prevalência
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Resultados */}
      {results.length > 0 && (
        <>
          {/* Estatísticas Gerais */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm text-gray-600">Instituições</p>
                    <p className="text-2xl font-bold">{results.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-600">Total de Questões</p>
                    <p className="text-2xl font-bold">{getTotalQuestions().toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-purple-500" />
                  <div>
                    <p className="text-sm text-gray-600">Período</p>
                    <p className="text-2xl font-bold">{filters.startYear} - {filters.endYear}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-orange-500" />
                  <div>
                    <p className="text-sm text-gray-600">Domínio</p>
                    <p className="text-2xl font-bold capitalize">{filters.domain}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Análise por Categorias */}
          <Card>
            <CardHeader>
              <CardTitle>Análise de Prevalência por Categoria</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="specialties">Especialidades</TabsTrigger>
                  <TabsTrigger value="themes">Temas</TabsTrigger>
                  <TabsTrigger value="focuses">Focos</TabsTrigger>
                </TabsList>
                
                <TabsContent value="specialties" className="space-y-4">
                  <PrevalenceChart data={getCombinedData('specialties')} title="Top 20 Especialidades" />
                  <PrevalenceTable data={getCombinedData('specialties')} type="Especialidade" />
                </TabsContent>
                
                <TabsContent value="themes" className="space-y-4">
                  <PrevalenceChart data={getCombinedData('themes')} title="Top 20 Temas" />
                  <PrevalenceTable data={getCombinedData('themes')} type="Tema" />
                </TabsContent>
                
                <TabsContent value="focuses" className="space-y-4">
                  <PrevalenceChart data={getCombinedData('focuses')} title="Top 20 Focos" />
                  <PrevalenceTable data={getCombinedData('focuses')} type="Foco" />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
