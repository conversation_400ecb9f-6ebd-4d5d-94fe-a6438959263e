import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useCurrentQuestion } from '@/contexts/CurrentQuestionContext';

interface SessionInactiveDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  sessionTitle: string;
  sessionId?: string;
}

export const SessionInactiveDialog: React.FC<SessionInactiveDialogProps> = ({
  open,
  onOpenChange,
  sessionTitle,
  sessionId
}) => {
  const navigate = useNavigate();
  const { clearQuestionContext } = useCurrentQuestion();

  const handleGoToSession = async () => {
    console.log('🎯 [SessionInactiveDialog] Navegando para sessão:', {
      sessionId,
      sessionTitle
    });

    onOpenChange(false);

    // 🎯 LIMPAR contexto atual antes de navegar para nova sessão
    clearQuestionContext();
    console.log('🧹 [SessionInactiveDialog] Contexto limpo antes da navegação');

    if (sessionId) {
      // Aguardar um frame para garantir que o contexto foi limpo
      await new Promise(resolve => requestAnimationFrame(resolve));

      // Usar navigate do React Router para navegação fluida
      navigate(`/questions/${sessionId}`, {
        replace: true, // Substitui a entrada atual no histórico
        state: { forceRefresh: true } // Flag para forçar atualização
      });
    } else {
      // Fallback para página de questões
      navigate('/questions');
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center gap-3 mb-2">
            <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center flex-shrink-0">
              <span className="text-2xl">⚠️</span>
            </div>
            <div className="min-w-0 flex-1">
              <DialogTitle className="text-xl font-bold text-gray-900 truncate">
                Sessão Inativa
              </DialogTitle>
              <DialogDescription className="text-orange-600 text-sm">
                Contexto não disponível
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          <div className="text-center">
            <h4 className="text-lg font-semibold text-gray-900 mb-2">
              Conversa Contextual Detectada
            </h4>
            <p className="text-gray-600 text-sm leading-relaxed break-words">
              Esta conversa é sobre a sessão{" "}
              <span className="font-semibold text-orange-600 break-all">"{sessionTitle}"</span>.{" "}
              Para acessá-la com contexto completo, você precisa retornar a essa sessão específica.
            </p>
          </div>

          <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="text-sm text-orange-800 min-w-0 flex-1">
                <div className="font-medium mb-1">O que isso significa?</div>
                <ul className="space-y-1 text-orange-700">
                  <li className="break-words">• Esta conversa contém contexto específico de questões</li>
                  <li className="break-words">• Sem a sessão ativa, as respostas podem ser imprecisas</li>
                  <li className="break-words">• {sessionId ? 'Clique para retornar à sessão original' : 'Vá para questões para iniciar uma sessão'}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 pt-4">
          <Button
            onClick={handleGoToSession}
            className="flex-1 bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-sm"
          >
            <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
            <span className="truncate">
              {sessionId ? `Ir para "${sessionTitle}"` : 'Ir para Questões'}
            </span>
          </Button>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="sm:w-auto"
          >
            Fechar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
