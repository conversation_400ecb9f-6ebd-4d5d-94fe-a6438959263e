/**
 * 🎯 HOOK OTIMIZADO PARA PREFERÊNCIAS NA PÁGINA SCHEDULE
 * 
 * Versão simplificada do useStudyPreferences que não carrega instituições
 * para evitar query desnecessária de get_institutions_with_questions
 */
import { useOptimizedStudyPreferencesData } from '@/hooks/useConsolidatedUserPreferences';

export const useSchedulePreferences = () => {
  const { 
    studyPreferencesData, 
    isLoading, 
    error 
  } = useOptimizedStudyPreferencesData();

  return {
    preferences: studyPreferencesData,
    isLoading,
    error,
    // ✅ OTIMIZADO: Não carregar instituições na página Schedule
    availableInstitutions: [],
    isLoadingInstitutions: false,
    hasCompletedPreferences: studyPreferencesData?.preferences_completed || false
  };
};
