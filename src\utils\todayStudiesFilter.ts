import type { WeeklySchedule, StudyTopic } from '@/types/study-schedule';

/**
 * Utilitários para filtrar focos dos estudos do dia
 */

/**
 * Extrai os focus_ids dos tópicos dos estudos do dia
 *
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Array de focus_ids dos estudos de hoje
 */
export const extractTodayFocusIds = (weeklySchedule: WeeklySchedule | null): string[] => {
  if (!weeklySchedule?.recommendations) {
    return [];
  }

  const today = new Date();
  const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
  const currentDay = weekDays[today.getDay()];

  // ✅ LIMPEZA: Remover log desnecessário

  // Encontrar os estudos do dia atual
  const todayStudies = weeklySchedule.recommendations.find(day =>
    day.day.toLowerCase() === currentDay
  );

  if (!todayStudies?.topics) {
    // ✅ LIMPEZA: Remover log desnecessário
    return [];
  }

  // Extrair focus_ids dos tópicos
  const focusIds = todayStudies.topics
    .map(topic => topic.focusId)
    .filter((id): id is string => Boolean(id));

  // ✅ LIMPEZA: Remover log desnecessário

  return focusIds;
};

/**
 * ✅ NOVO: Extrai focus_ids das próximas 2 semanas do cronograma
 *
 * @param weeklySchedules - Array de cronogramas semanais (atual + próximas semanas)
 * @returns Array de focus_ids únicos das próximas 2 semanas
 */
export const extractNext2WeeksFocusIds = (weeklySchedules: WeeklySchedule[]): string[] => {
  if (!weeklySchedules || weeklySchedules.length === 0) {
    return [];
  }

  const allFocusIds: string[] = [];

  // Processar cada cronograma semanal
  weeklySchedules.forEach((schedule, weekIndex) => {
    if (!schedule?.recommendations) return;

    // ✅ LIMPEZA: Remover log desnecessário

    // Processar cada dia da semana
    schedule.recommendations.forEach(day => {
      if (!day?.topics) return;

      // Extrair focus_ids dos tópicos do dia
      const dayFocusIds = day.topics
        .map(topic => topic.focusId)
        .filter((id): id is string => Boolean(id));

      allFocusIds.push(...dayFocusIds);
    });
  });

  // Remover duplicatas
  const uniqueFocusIds = [...new Set(allFocusIds)];

  // ✅ LIMPEZA: Remover log desnecessário

  return uniqueFocusIds;
};

/**
 * Extrai informações detalhadas dos tópicos dos estudos do dia
 * 
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Array de tópicos dos estudos de hoje
 */
export const extractTodayTopics = (weeklySchedule: WeeklySchedule | null): StudyTopic[] => {
  if (!weeklySchedule?.recommendations) {
    return [];
  }

  const today = new Date();
  const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
  const currentDay = weekDays[today.getDay()];

  // Encontrar os estudos do dia atual
  const todayStudies = weeklySchedule.recommendations.find(day => 
    day.day.toLowerCase() === currentDay
  );

  return todayStudies?.topics || [];
};

/**
 * Verifica se um focus_id está nos estudos do dia
 * 
 * @param focusId - ID do foco a verificar
 * @param weeklySchedule - Cronograma semanal atual
 * @returns true se o foco está nos estudos de hoje
 */
export const isFocusInTodayStudies = (focusId: string, weeklySchedule: WeeklySchedule | null): boolean => {
  const todayFocusIds = extractTodayFocusIds(weeklySchedule);
  return todayFocusIds.includes(focusId);
};

/**
 * Conta quantos focos únicos estão nos estudos do dia
 * 
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Número de focos únicos nos estudos de hoje
 */
export const countTodayUniqueFoci = (weeklySchedule: WeeklySchedule | null): number => {
  const focusIds = extractTodayFocusIds(weeklySchedule);
  const uniqueFocusIds = [...new Set(focusIds)];
  return uniqueFocusIds.length;
};

/**
 * ✅ NOVO: Extrai focus_ids combinando hoje + próximas 2 semanas
 *
 * @param currentWeekSchedule - Cronograma da semana atual
 * @param nextWeeksSchedules - Array de cronogramas das próximas semanas
 * @returns Array de focus_ids únicos para excluir dos insights
 */
export const extractAllScheduledFocusIds = (
  currentWeekSchedule: WeeklySchedule | null,
  nextWeeksSchedules: WeeklySchedule[] = []
): string[] => {
  // Focus IDs do dia atual
  const todayFocusIds = extractTodayFocusIds(currentWeekSchedule);

  // Focus IDs das próximas 2 semanas (incluindo resto da semana atual)
  const allSchedules = currentWeekSchedule ? [currentWeekSchedule, ...nextWeeksSchedules] : nextWeeksSchedules;
  const next2WeeksFocusIds = extractNext2WeeksFocusIds(allSchedules);

  // Combinar e remover duplicatas
  const allFocusIds = [...todayFocusIds, ...next2WeeksFocusIds];
  const uniqueFocusIds = [...new Set(allFocusIds)];

  // ✅ OTIMIZAÇÃO: Log apenas quando há focos para excluir
  if (uniqueFocusIds.length > 0) {
    console.log('🎯 [extractAllScheduledFocusIds] Focos excluídos:', {
      total: uniqueFocusIds.length,
      focusIds: uniqueFocusIds
    });
  }

  return uniqueFocusIds;
};

/**
 * Obtém estatísticas dos estudos do dia para debug
 *
 * @param weeklySchedule - Cronograma semanal atual
 * @returns Objeto com estatísticas dos estudos de hoje
 */
export const getTodayStudiesStats = (weeklySchedule: WeeklySchedule | null) => {
  const topics = extractTodayTopics(weeklySchedule);
  const focusIds = extractTodayFocusIds(weeklySchedule);
  const uniqueFocusIds = [...new Set(focusIds)];

  return {
    totalTopics: topics.length,
    totalFocusIds: focusIds.length,
    uniqueFocusIds: uniqueFocusIds.length,
    focusIds: uniqueFocusIds,
    topics: topics.map(topic => ({
      id: topic.id,
      focus: topic.focus,
      focusId: topic.focusId,
      specialty: topic.specialty,
      theme: topic.theme
    }))
  };
};
