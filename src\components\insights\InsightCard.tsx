import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Brain,
  TrendingUp,
  Clock,
  Info,
  Play,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { InsightInfoDialog } from './InsightInfoDialog';
import { useStudyPreferences } from '@/hooks/useStudyPreferences';
import type { FocusInsight } from '@/types/insights';

interface InsightCardProps {
  insight: FocusInsight | null;
  isLoading?: boolean;
  className?: string;
  compact?: boolean;
  simplified?: boolean; // ✅ NOVO: Modo ultra simplificado para banner
  onStudyClick?: (insight: FocusInsight) => void;
}

const InsightCard: React.FC<InsightCardProps> = ({
  insight,
  isLoading = false,
  className = '',
  compact = false,
  simplified = false,
  onStudyClick
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [showInfoDialog, setShowInfoDialog] = useState(false);
  const { preferences } = useStudyPreferences();

  // Função para redirecionar para preferências
  const handleGoToPreferences = () => {
    window.location.href = '/study-preferences';
  };

  // Loading state
  if (isLoading) {
    // Verificar se o usuário não tem preferências configuradas
    const hasPreferences = preferences?.target_institutions && preferences.target_institutions.length > 0;

    // Se não tem preferências, não mostrar loading, mostrar configuração
    if (!hasPreferences) {
      if (simplified) {
        return (
          <div className={`flex items-center gap-2 ${className}`}>
            <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Settings className="h-3 w-3 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-xs font-medium text-gray-700">
                Configure suas preferências
              </div>
            </div>
            <Button
              size="sm"
              onClick={handleGoToPreferences}
              className="h-6 px-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-medium"
            >
              <Settings className="h-2.5 w-2.5 mr-1" />
              Configurar
            </Button>
          </div>
        );
      }

      return (
        <div className={`bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200 ${className}`}>
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <Settings className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="text-sm font-semibold text-gray-800">
                Configure suas preferências
              </div>
              <div className="text-xs text-gray-600">
                Personalize seus insights de estudo
              </div>
            </div>
          </div>
          <Button
            onClick={handleGoToPreferences}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 text-sm font-medium"
          >
            <Settings className="h-4 w-4 mr-2" />
            Ir para Preferências
          </Button>
        </div>
      );
    }

    // Loading normal para usuários com preferências
    if (simplified) {
      return (
        <div className={`flex items-center gap-3 ${className}`}>
          <div className="w-8 h-8 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-lg animate-pulse flex items-center justify-center">
            <Brain className="h-4 w-4 text-white/80" />
          </div>
          <div className="flex-1">
            <div className="h-3 bg-gray-200 rounded-full animate-pulse mb-2"></div>
            <div className="h-2 bg-gray-100 rounded-full animate-pulse w-2/3"></div>
          </div>
        </div>
      );
    }

    return (
      <div className={`bg-white/80 backdrop-blur-sm rounded-xl p-3 border border-gray-200/50 shadow-sm ${className}`}>
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-lg animate-pulse flex items-center justify-center">
            <Brain className="h-4 w-4 text-white/80" />
          </div>
          <div className="flex-1">
            <div className="h-3 bg-gray-200 rounded-full animate-pulse mb-2"></div>
            <div className="h-2 bg-gray-100 rounded-full animate-pulse w-2/3"></div>
          </div>
        </div>
      </div>
    );
  }

  // No insight available
  if (!insight) {
    // Verificar se o usuário não tem preferências configuradas
    const hasPreferences = preferences?.target_institutions && preferences.target_institutions.length > 0;

    if (simplified) {
      if (!hasPreferences) {
        // Usuário sem preferências - mostrar botão para configurar
        return (
          <div className={`flex items-center gap-2 ${className}`}>
            <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Settings className="h-3 w-3 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-xs font-medium text-gray-700">
                Configure suas preferências
              </div>
            </div>
            <Button
              size="sm"
              onClick={handleGoToPreferences}
              className="h-6 px-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-medium"
            >
              <Settings className="h-2.5 w-2.5 mr-1" />
              Configurar
            </Button>
          </div>
        );
      }

      // Usuário com preferências mas sem insights disponíveis
      return (
        <div className={`flex items-center gap-3 ${className}`}>
          <div className="w-8 h-8 bg-gray-300 rounded-lg flex items-center justify-center">
            <Brain className="h-4 w-4 text-gray-500" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-500">
              Aguardando insights...
            </div>
            <div className="text-xs text-gray-400">
              Carregando sugestões
            </div>
          </div>
        </div>
      );
    }

    // Versão não simplificada
    if (!hasPreferences) {
      return (
        <div className={`bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 border border-blue-200 ${className}`}>
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
              <Settings className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <div className="text-sm font-semibold text-gray-800">
                Configure suas preferências
              </div>
              <div className="text-xs text-gray-600">
                Personalize seus insights de estudo
              </div>
            </div>
          </div>
          <Button
            onClick={handleGoToPreferences}
            className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 text-sm font-medium"
          >
            <Settings className="h-4 w-4 mr-2" />
            Ir para Preferências
          </Button>
        </div>
      );
    }

    return (
      <div className={`bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-gray-200/30 ${className}`}>
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-300 rounded-lg flex items-center justify-center">
            <Brain className="h-4 w-4 text-gray-500" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-500">
              Aguardando insights...
            </div>
            <div className="text-xs text-gray-400">
              Carregando sugestões
            </div>
          </div>
        </div>
      </div>
    );
  }

  const { config } = insight;

  // Cores modernas baseadas na temperatura
  const getModernColors = (temperature: string) => {
    switch (temperature) {
      case 'vulcanico':
        return {
          bg: 'bg-gradient-to-br from-red-50 to-orange-50',
          border: 'border-red-200/50',
          icon: 'bg-gradient-to-br from-red-500 to-orange-500',
          text: 'text-red-700',
          badge: 'bg-red-100 text-red-700 border-red-200'
        };
      case 'quente':
        return {
          bg: 'bg-gradient-to-br from-orange-50 to-amber-50',
          border: 'border-orange-200/50',
          icon: 'bg-gradient-to-br from-orange-500 to-amber-500',
          text: 'text-orange-700',
          badge: 'bg-orange-100 text-orange-700 border-orange-200'
        };
      case 'morno':
        return {
          bg: 'bg-gradient-to-br from-yellow-50 to-amber-50',
          border: 'border-yellow-200/50',
          icon: 'bg-gradient-to-br from-yellow-500 to-amber-500',
          text: 'text-yellow-700',
          badge: 'bg-yellow-100 text-yellow-700 border-yellow-200'
        };
      case 'frio':
        return {
          bg: 'bg-gradient-to-br from-blue-50 to-cyan-50',
          border: 'border-blue-200/50',
          icon: 'bg-gradient-to-br from-blue-500 to-cyan-500',
          text: 'text-blue-700',
          badge: 'bg-blue-100 text-blue-700 border-blue-200'
        };
      default:
        return {
          bg: 'bg-gradient-to-br from-gray-50 to-slate-50',
          border: 'border-gray-200/50',
          icon: 'bg-gradient-to-br from-gray-500 to-slate-500',
          text: 'text-gray-700',
          badge: 'bg-gray-100 text-gray-700 border-gray-200'
        };
    }
  };

  const colors = getModernColors(insight.temperature);

  // ✅ NOVO: Layout compacto e organizado
  if (simplified) {
    return (
      <>
        <div className={`relative ${className}`}>
          {/* Título completo - linha inteira */}
          <div className="text-sm font-semibold text-gray-800 leading-snug mb-3 pr-2">
            {insight.focus_name}
          </div>

          {/* Footer com badge e botões */}
          <div className="flex items-center justify-between">
            {/* Badge de temperatura no canto esquerdo */}
            <span className={`px-2 py-0.5 rounded-full ${colors.badge} border font-medium text-xs`}>
              {config.label}
            </span>

            {/* Botões no canto direito */}
            <div className="flex items-center gap-1.5">
              {/* Botão Estudar menor */}
              {onStudyClick && (
                <Button
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    onStudyClick(insight);
                  }}
                  className="h-6 px-2.5 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-medium rounded-md"
                >
                  <Play className="h-2.5 w-2.5 mr-1" />
                  Estudar
                </Button>
              )}

              {/* Info button menor */}
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowInfoDialog(true);
                }}
                className="w-6 h-6 rounded-md bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300 flex items-center justify-center transition-all duration-200 group/info"
                title="Ver detalhes do insight"
              >
                <Info className="h-3 w-3 text-gray-500 group-hover/info:text-gray-700" />
              </button>
            </div>
          </div>
        </div>

        {/* Dialog de Informações sobre Insights */}
        <InsightInfoDialog
          open={showInfoDialog}
          onOpenChange={setShowInfoDialog}
        />
      </>
    );
  }

  const cardContent = (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, ease: "easeOut" }}
      className={`relative bg-white/80 backdrop-blur-sm rounded-xl border ${colors.border} shadow-sm hover:shadow-lg transition-all duration-300 group ${className}`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
    >
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center gap-3">
          {/* Icon moderno */}
          <div className={`w-10 h-10 ${colors.icon} rounded-xl flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-200`}>
            <span className="text-lg">{config.icon}</span>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className={`text-xs font-semibold px-2.5 py-1 rounded-full ${colors.badge} border`}>
                {config.label}
              </span>

              {!compact && (
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-3 w-3" />
                    <span className="font-medium">{insight.frequency}</span>
                  </div>
                  <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{insight.years_since_last < 999 ? `${insight.years_since_last}a` : '∞'}</span>
                  </div>
                </div>
              )}
            </div>

            <div className="text-sm font-semibold text-gray-800 truncate mb-0.5">
              {insight.focus_name}
            </div>

            {!compact && (
              <div className="text-xs text-gray-500 truncate">
                {insight.specialty_name} • {insight.theme_name}
              </div>
            )}
          </div>

          {/* Botões de ação */}
          <div className="flex items-center gap-2">
            {/* Botão Estudar */}
            {onStudyClick && (
              <Button
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onStudyClick(insight);
                }}
                className="h-8 px-3 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 text-xs font-semibold"
              >
                <Play className="h-3 w-3 mr-1" />
                Estudar
              </Button>
            )}

            {/* Info button */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowInfoDialog(true);
              }}
              className="w-8 h-8 rounded-lg bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200 group/info"
              title="Mais informações sobre insights"
            >
              <Info className="h-4 w-4 text-gray-600 group-hover/info:text-gray-800" />
            </button>
          </div>
        </div>

        {/* ✅ REMOVIDO: Stats desnecessárias no modo compact */}
      </div>

      {/* Tooltip moderno */}
      <AnimatePresence>
        {showTooltip && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
          >
            <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-2 max-w-xs shadow-xl">
              <div className="font-medium mb-1">{insight.config.description}</div>
              <div className="text-gray-300">
                {insight.frequency} ocorrências • {insight.years_since_last < 999 ? `${insight.years_since_last} anos atrás` : 'nunca registrada'}
              </div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

    </motion.div>
  );

  return cardContent;
};

export default InsightCard;
