import { useState, useEffect, useMemo } from 'react';
import { useNavigate } from "react-router-dom";
import { QuestionCard } from "@/components/question/QuestionCard";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useStudySession } from "@/hooks/useStudySession";
import { useSessionPersistence } from "@/hooks/useSessionPersistence";
import { useSessionTimer } from "@/hooks/useTimerMigration";
import { useAnswerSubmission } from "@/hooks/useAnswerSubmission";
import { useQuestionSolverState } from "@/hooks/useQuestionSolverState";
import { QuestionNavigation } from "./QuestionNavigation";
import { Progress } from "@/components/ui/progress";
import type { Question } from "@/types/question";
import { useQueryClient } from '@tanstack/react-query';
import { useNavigationLock } from '@/contexts/NavigationLockContext';
import { useCurrentQuestion } from '@/contexts/CurrentQuestionContext';
import { useConsolidatedSession } from '@/hooks/useConsolidatedSession';
import { useBlockDuplicateQueries } from '@/hooks/useSimpleQueryBlocker';
import { useRequestLogger } from '@/utils/requestLogger';

import { resetSessionTimer } from '@/utils/resetSessionTimer';

interface QuestionSolverProps {
  questions: Question[];
  sessionId: string;
  userId: string;
}

export const QuestionSolver = ({ questions, sessionId, userId }: QuestionSolverProps) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [hasLoadedProgress, setHasLoadedProgress] = useState(false);
  const [isLoadingProgress, setIsLoadingProgress] = useState(true);
  const [isFinishingSession, setIsFinishingSession] = useState(false);
  const [sessionTitle, setSessionTitleState] = useState<string>('Sessão de Estudos');
  const navigate = useNavigate();
  const { toast } = useToast();
  const { updateSessionProgress } = useStudySession();
  const { markQuestionAsAnswered } = useSessionPersistence(userId);
  const queryClient = useQueryClient();
  const { lockNavigation, unlockNavigation } = useNavigationLock();

  // ✅ OTIMIZADO: Hook consolidado para dados da sessão
  const { data: consolidatedData, isLoading: isConsolidatedLoading } = useConsolidatedSession(sessionId);

  // ✅ BLOQUEADOR SIMPLES: Bloquear queries duplicadas
  useBlockDuplicateQueries(sessionId);

  // 🔍 LOGGER: Monitorar requests para diagnóstico
  const { logRequest } = useRequestLogger('QuestionSolver');

  // 🎯 Hook para atualizar contexto da questão atual
  const {
    updateQuestionContext,
    clearQuestionContext,
    setCurrentQuestion,
    setQuestionIndex,
    setTotalQuestions,
    setSessionId: setContextSessionId,
    setSessionTitle
  } = useCurrentQuestion();

  // Hook otimizado para gerenciar estado das questões
  const {
    selectAnswer,
    markAsAnswered,
    setFeedback,
    resetAll,
    getSelectedAnswer,
    isQuestionAnswered,
    hasQuestionFeedback,
    getQuestionCorrectness,
    stats
  } = useQuestionSolverState();

  // Hook para submissão de respostas
  const { submitAnswer } = useAnswerSubmission(sessionId, () => {
    // Callback vazio - o controle de estado agora é feito no handleSubmitAnswer
  });

  // ✅ NOVO: Sistema de timer robusto e à prova de falhas
  const timerHook = useSessionTimer(sessionId, userId);
  const {
    totalElapsedTime,
    currentQuestionTime,
    startQuestionTimer,
    pauseCurrentTimer,
    getQuestionTime,
    finishSession: finishTimerSession,
    isActive: isTimerActive,
    saveOnAnswer, // ✅ NOVO: Salvar quando marcar resposta
    getTotalSessionTime,
    getActiveStudyTime
  } = timerHook;

  const currentQuestion = questions[currentIndex];
  const currentQuestionId = currentQuestion?.id;
  const isLastQuestion = currentIndex === questions.length - 1;
  const isFirstQuestion = currentIndex === 0;
  const allQuestionsAnswered = stats.totalAnswered === questions.length;
  const progressPercentage = (stats.totalAnswered / questions.length) * 100;

  // Iniciar timer quando questão muda
  useEffect(() => {
    if (currentQuestionId) {
      startQuestionTimer(currentQuestionId);
    }
  }, [currentQuestionId, startQuestionTimer, currentIndex]);

  // 🎯 Atualizar contexto da questão atual quando questão muda
  useEffect(() => {
    if (currentQuestion && sessionId) {
      updateQuestionContext({
        question: currentQuestion,
        questionIndex: currentIndex,
        totalQuestions: questions.length,
        sessionId: sessionId,
        sessionTitle: sessionTitle
      });
    }
  }, [currentQuestion, currentIndex, questions.length, sessionId, sessionTitle, updateQuestionContext]);

  // 🎯 Limpar contexto quando componente é desmontado
  useEffect(() => {
    return () => {
      clearQuestionContext();
    };
  }, [clearQuestionContext]);

  // ✅ OTIMIZADO: Usar título do hook consolidado + interceptar queries
  useEffect(() => {
    if (consolidatedData?.session?.title) {
      setSessionTitleState(consolidatedData.session.title);
    }

  }, [consolidatedData?.session?.title]);

  // ✅ Salvar progresso automaticamente ao sair (beforeunload)
  useEffect(() => {
    const handleBeforeUnload = async () => {
      // Salvar progresso automaticamente (não finalizar)
      await handleSaveProgressSilently();
    };

    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'hidden') {
        // Salvar progresso quando aba fica inativa (não finalizar)
        await handleSaveProgressSilently();
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      // Salvar progresso ao desmontar componente (não finalizar)
      handleSaveProgressSilently();
    };
  }, [sessionId, stats.totalAnswered]);

  useEffect(() => {
    const loadSessionProgress = async () => {
      if (!sessionId || hasLoadedProgress) {
        return;
      }
      setIsLoadingProgress(true);

      try {
        // Verificar se o usuário está autenticado
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          return;
        }

        // ✅ CARREGAMENTO DIRETO E SIMPLES
        const [answersResult] = await Promise.allSettled([
          supabase
            .from('user_answers')
            .select('question_id, is_correct, text_answer, ai_analyzed, selected_answer')
            .eq('session_id', sessionId)
            .eq('user_id', user.id)
        ]);

        const answersData = answersResult.status === 'fulfilled' ? answersResult.value.data || [] : [];

        // ✅ PROCESSAR RESPOSTAS DE FORMA SIMPLES
        answersData.forEach(answer => {
          markAsAnswered(answer.question_id);

          if (answer.text_answer) {
            // Questão dissertativa
            if (answer.ai_analyzed) {
              setFeedback(answer.question_id, answer.is_correct);
            }
          } else if (answer.selected_answer !== null) {
            // Questão de múltipla escolha
            selectAnswer(answer.question_id, answer.selected_answer.toString());
            setFeedback(answer.question_id, answer.is_correct);
          }
        });

        setHasLoadedProgress(true);
      } catch (error) {
        // Error handling
      } finally {
        setIsLoadingProgress(false);
      }
    };

    loadSessionProgress();
  }, [sessionId, hasLoadedProgress, questions.length, selectAnswer, setFeedback, toast, isConsolidatedLoading, consolidatedData]);

  const handleSubmitAnswer = async (timeSpent: number) => {
    if (!currentQuestionId) return;

    const selectedAnswer = getSelectedAnswer(currentQuestionId);
    if (!selectedAnswer) return;

    try {
      // Usar o tempo real da questão atual em vez do parâmetro
      const actualTimeSpent = getQuestionTime(currentQuestionId);

      // Usar o hook useAnswerSubmission que salva corretamente na tabela user_answers
      const success = await submitAnswer(
        userId,
        currentQuestion,
        selectedAnswer,
        actualTimeSpent
      );

      if (success) {
        // ✅ PRIMEIRO: Marcar como respondida (salva no banco)
        markAsAnswered(currentQuestionId);

        // ✅ SEGUNDO: Determinar se a resposta está correta para atualizar o status
        const correctAnswerValue = typeof currentQuestion.correct_answer === 'number'
          ? (currentQuestion.correct_answer + 1).toString()
          : (parseInt(String(currentQuestion.correct_answer)) + 1).toString();

        const isCorrect = selectedAnswer === correctAnswerValue;
        setFeedback(currentQuestionId, isCorrect);

        // ✅ SALVAR TEMPO quando marcar resposta
        saveOnAnswer();

        // ✅ FORÇAR RECARREGAMENTO das respostas
        setHasLoadedProgress(false);
      }
    } catch (error) {
      // Erro silencioso
    }
  };

  const handleSelectAnswer = (questionId: string, answer: string) => {
    selectAnswer(questionId, answer);
  };

  const handleSelectQuestion = (index: number) => {
    setCurrentIndex(index);
  };

  const handleNextQuestion = () => {
    if (currentIndex < questions.length - 1) {
      setCurrentIndex(prevIndex => prevIndex + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prevIndex => prevIndex - 1);
    }
  };

  // ✅ Atualizar progresso da sessão no banco
  const updateSessionIndex = async () => {
    try {
      const { error } = await supabase
        .from('study_sessions')
        .update({
          current_question_index: stats.totalAnswered
        })
        .eq('id', sessionId);

      if (!error) {
        queryClient.invalidateQueries({ queryKey: ['study-sessions'] });
      }
    } catch (error) {
      // Silent fail
    }
  };

  // ✅ Função para salvar progresso silenciosamente (sem finalizar a sessão)
  const handleSaveProgressSilently = async () => {
    try {
      // Verificar se o usuário está autenticado
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // APENAS salvar progresso, NÃO finalizar a sessão
      // Só marcar como completed se TODAS as questões foram respondidas
      const allQuestionsAnswered = stats.totalAnswered >= questions.length;

      const updateData: any = {
        current_question_index: stats.totalAnswered,
        last_activity: new Date().toISOString()
      };

      // Só marcar como completed se realmente terminou todas as questões
      if (allQuestionsAnswered) {
        updateData.completed_at = new Date().toISOString();
        updateData.status = 'completed';
      }

      const { error } = await supabase
        .from('study_sessions')
        .update(updateData)
        .eq('id', sessionId);

      if (!error) {
        // Invalidar TODOS os caches relacionados incluindo streak
        queryClient.invalidateQueries({ queryKey: ['study-sessions'] });
        if (allQuestionsAnswered) {
          queryClient.invalidateQueries({ queryKey: ['user-statistics'] });
          queryClient.invalidateQueries({ queryKey: ['consolidated-dashboard-stats'] });
          queryClient.invalidateQueries({ queryKey: ['week-activities'] });
          queryClient.invalidateQueries({ queryKey: ['optimized-streak-stats'] });
        }
      }
    } catch (error) {
      // Falha silenciosa - não mostrar erro ao usuário
      console.warn('⚠️ [QuestionSolver] Erro ao salvar progresso silenciosamente:', error);
    }
  };

  const handleFinishSession = async () => {
    if (isFinishingSession) return; // Evitar múltiplos cliques

    setIsFinishingSession(true);
    lockNavigation('Finalizando sessão de estudos...');

    try {


      // Finalizar sistema de timer primeiro
      await finishTimerSession();

      // Verificar se o usuário está autenticado
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast({
          title: "Erro de autenticação",
          description: "Você precisa estar logado para finalizar a sessão.",
          variant: "destructive"
        });
        return;
      }

      // ✅ OTIMIZADO: Verificar permissão usando dados consolidados
      const sessionData = consolidatedData?.session;

      if (sessionData && sessionData.user_id !== user.id) {
        // Verificar se o usuário é admin

        const { data: profileData } = await supabase
          .from('profiles')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        const isAdmin = profileData?.is_admin || false;

        if (!isAdmin) {
          toast({
            title: "Erro de permissão",
            description: "Você não tem permissão para finalizar esta sessão.",
            variant: "destructive"
          });
          return;
        }
      }

      // Determinar status correto baseado no progresso
      const allQuestionsAnswered = stats.totalAnswered >= questions.length;
      const sessionStatus = allQuestionsAnswered ? 'completed' : 'abandoned';

      const { error } = await supabase
        .from('study_sessions')
        .update({
          current_question_index: stats.totalAnswered,
          completed_at: new Date().toISOString(),
          status: sessionStatus
        })
        .eq('id', sessionId);

      if (error) {
        // Se o erro for relacionado à tabela não existir ou permissão negada, apenas continuamos
        if (error.code === '42P01') {
          // Tabela não encontrada
        } else if (error.code === '42501') {
          // Permissão negada
        } else {
          throw error;
        }
      }

      // ✅ INVALIDAR TODOS OS CACHES quando sessão é finalizada manualmente
      // Invalidar estatísticas
      await queryClient.refetchQueries({ queryKey: ['user-statistics'] });
      await queryClient.refetchQueries({ queryKey: ['user-study-stats'] });

      // Invalidar sessões
      await queryClient.refetchQueries({
        predicate: (query) => {
          const key = query.queryKey[0] as string;
          return key === 'study-sessions' || key === 'flashcard-sessions';
        }
      });

      // Invalidar a query de questões acertadas para forçar uma atualização
      queryClient.invalidateQueries({ queryKey: ['correct-questions'] });

      // Toast removido para experiência mais limpa

      navigate(`/results/${sessionId}`);
    } catch {
      toast({
        title: "Erro ao finalizar sessão",
        description: "Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsFinishingSession(false);
      unlockNavigation();
    }
  };

  if (!questions.length) return null;

  // ✅ Memoizar indexedAnswerStatuses para evitar recálculos desnecessários
  const indexedAnswerStatuses = useMemo(() => {
    const statuses: Record<number, boolean | null> = {};

    questions.forEach((question, index) => {
      const correctness = getQuestionCorrectness(question.id);
      if (correctness !== null) {
        statuses[index] = correctness;
      }
    });

    return statuses;
  }, [questions, getQuestionCorrectness]);





  const isAnswered = currentQuestionId ? isQuestionAnswered(currentQuestionId) : false;
  const showFeedback = currentQuestionId ? hasQuestionFeedback(currentQuestionId) : false;

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6 bg-white rounded-xl shadow-lg p-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm font-medium text-gray-500">
              Progresso: {stats.totalAnswered}/{questions.length} questões
            </span>
            <span className="text-sm font-medium text-gray-500">
              {progressPercentage.toFixed(0)}%
            </span>
          </div>
          <Progress value={progressPercentage} className="h-3 bg-gray-100" />
        </div>
      </div>

      <QuestionNavigation
        currentIndex={currentIndex}
        totalQuestions={questions.length}
        onSelectQuestion={handleSelectQuestion}
        onFinishSession={handleFinishSession}
        elapsedTime={totalElapsedTime}
        onTimeUpdate={() => {}} // Não usado mais - timer é gerenciado pelo hook
        questionId={currentQuestion?.id || ""}
        userId={userId}
        answeredStatuses={isLoadingProgress ? {} : indexedAnswerStatuses}
        isLoading={isLoadingProgress}
      />

      <div className="space-y-6">
        <QuestionCard
          question={currentQuestion}
          selectedAnswer={getSelectedAnswer(currentQuestionId) || null}
          hasAnswered={isAnswered}
          onSelectAnswer={(answer) => handleSelectAnswer(currentQuestionId, answer)}
          onSubmitAnswer={handleSubmitAnswer}
          onNext={isLastQuestion && allQuestionsAnswered ? handleFinishSession : handleNextQuestion}
          onPrevious={isFirstQuestion ? undefined : handlePreviousQuestion}
          userId={userId}
          sessionId={sessionId}
          isAnswered={isAnswered}
          timeSpent={currentQuestionTime}
          showFeedback={showFeedback}
          isLastQuestion={isLastQuestion}
          isFirstQuestion={isFirstQuestion}
          allQuestionsAnswered={allQuestionsAnswered}
          onSetFeedback={setFeedback}
          onMarkAsAnswered={markAsAnswered}
        />
      </div>


    </div>
  );
};

export default QuestionSolver;
