import React from 'react';
import { motion } from 'framer-motion';
import { Clock, CheckCircle, Circle, GripVertical } from 'lucide-react';
import { studyFlowTheme } from './StudyFlowTheme';

export interface StudyBlockData {
  id: string;
  title: string;
  specialty: string;
  time: string;
  duration: number; // em minutos
  day: string;
  completed: boolean;
  difficulty: 'easy' | 'medium' | 'hard';
  color?: string;
}

interface StudyBlockProps {
  data: StudyBlockData;
  isDragging?: boolean;
  onDragStart?: (id: string) => void;
  onDragEnd?: () => void;
  onClick?: (id: string) => void;
  onToggleComplete?: (id: string) => void;
  className?: string;
}

const StudyBlock: React.FC<StudyBlockProps> = ({
  data,
  isDragging = false,
  onDragStart,
  onDragEnd,
  onClick,
  onToggleComplete,
  className = ''
}) => {
  // Cores baseadas na dificuldade
  const getDifficultyColors = (difficulty: string, completed: boolean) => {
    if (completed) {
      return {
        bg: studyFlowTheme.colors.success[50],
        border: studyFlowTheme.colors.success[200],
        text: studyFlowTheme.colors.success[800],
        accent: studyFlowTheme.colors.success[500]
      };
    }
    
    switch (difficulty) {
      case 'easy':
        return {
          bg: studyFlowTheme.colors.success[50],
          border: studyFlowTheme.colors.success[200],
          text: studyFlowTheme.colors.success[800],
          accent: studyFlowTheme.colors.success[500]
        };
      case 'medium':
        return {
          bg: studyFlowTheme.colors.warning[50],
          border: studyFlowTheme.colors.warning[200],
          text: studyFlowTheme.colors.warning[800],
          accent: studyFlowTheme.colors.warning[500]
        };
      case 'hard':
        return {
          bg: studyFlowTheme.colors.error[50],
          border: studyFlowTheme.colors.error[200],
          text: studyFlowTheme.colors.error[800],
          accent: studyFlowTheme.colors.error[500]
        };
      default:
        return {
          bg: studyFlowTheme.colors.neutral[50],
          border: studyFlowTheme.colors.neutral[200],
          text: studyFlowTheme.colors.neutral[800],
          accent: studyFlowTheme.colors.neutral[500]
        };
    }
  };

  const colors = getDifficultyColors(data.difficulty, data.completed);
  
  // Formatação do tempo de duração
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h${mins > 0 ? ` ${mins}m` : ''}`;
    }
    return `${mins}m`;
  };

  // Variantes de animação
  const blockVariants = {
    idle: {
      scale: 1,
      rotate: 0,
      opacity: 1,
      boxShadow: studyFlowTheme.shadows.sm
    },
    hover: {
      scale: 1.02,
      rotate: 0,
      opacity: 1,
      boxShadow: studyFlowTheme.shadows.md,
      transition: { duration: 0.2 }
    },
    dragging: {
      scale: 1.05,
      rotate: 2,
      opacity: 0.8,
      boxShadow: studyFlowTheme.shadows.lg,
      transition: { duration: 0.2 }
    },
    tap: {
      scale: 0.98,
      transition: { duration: 0.1 }
    }
  };

  return (
    <motion.div
      className={`
        relative group cursor-pointer select-none
        rounded-xl border-2 p-4 min-h-[120px]
        transition-all duration-200
        ${className}
      `}
      style={{
        backgroundColor: colors.bg,
        borderColor: colors.border,
        color: colors.text
      }}
      variants={blockVariants}
      initial="idle"
      animate={isDragging ? "dragging" : "idle"}
      whileHover="hover"
      whileTap="tap"
      onClick={() => onClick?.(data.id)}
      draggable
      onDragStart={() => onDragStart?.(data.id)}
      onDragEnd={onDragEnd}
    >
      {/* Barra de cor lateral */}
      <div 
        className="absolute left-0 top-0 bottom-0 w-1 rounded-l-xl"
        style={{ backgroundColor: colors.accent }}
      />
      
      {/* Handle de drag */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <GripVertical 
          className="h-4 w-4"
          style={{ color: colors.accent }}
        />
      </div>
      
      {/* Conteúdo principal */}
      <div className="flex flex-col h-full">
        {/* Header com horário e status */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4" style={{ color: colors.accent }} />
            <span className="text-sm font-medium">{data.time}</span>
            <span className="text-xs opacity-75">
              ({formatDuration(data.duration)})
            </span>
          </div>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleComplete?.(data.id);
            }}
            className="p-1 rounded-full hover:bg-white/50 transition-colors"
          >
            {data.completed ? (
              <CheckCircle 
                className="h-5 w-5" 
                style={{ color: colors.accent }}
              />
            ) : (
              <Circle 
                className="h-5 w-5" 
                style={{ color: colors.accent }}
              />
            )}
          </button>
        </div>
        
        {/* Título do tópico */}
        <h4 className="font-semibold text-sm leading-tight mb-2 flex-grow">
          {data.title}
        </h4>
        
        {/* Especialidade */}
        <div className="flex items-center justify-between">
          <span 
            className="text-xs px-2 py-1 rounded-full font-medium"
            style={{ 
              backgroundColor: `${colors.accent}20`,
              color: colors.accent
            }}
          >
            {data.specialty}
          </span>
          
          {/* Badge de dificuldade */}
          <span 
            className="text-xs px-2 py-1 rounded-full font-medium capitalize"
            style={{ 
              backgroundColor: `${colors.accent}15`,
              color: colors.text
            }}
          >
            {data.difficulty === 'easy' ? 'Fácil' : 
             data.difficulty === 'medium' ? 'Médio' : 'Difícil'}
          </span>
        </div>
      </div>
      
      {/* Overlay de conclusão */}
      {data.completed && (
        <motion.div
          className="absolute inset-0 bg-white/20 rounded-xl flex items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <CheckCircle 
            className="h-8 w-8" 
            style={{ color: colors.accent }}
          />
        </motion.div>
      )}
    </motion.div>
  );
};

export default StudyBlock;
