
import React from "react";
import { Controller, UseFormReturn } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { CalendarDays, Plus, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import type { AIScheduleFormData, DayConfig } from "../../types";
import { WEEK_DAYS } from "../../types";

interface DaysSectionProps {
  form: UseFormReturn<AIScheduleFormData>;
}

export const DaysSection = ({ form }: DaysSectionProps) => {
  const availableDays = form.watch('availableDays') || {};
  const topicDuration = parseInt(form.watch('topicDuration') || '30');
  const { setValue } = form;

  // Função para formatar horário no formato 24h brasileiro
  const formatTime = (value: string): string => {
    // Remove tudo que não é número
    const numbers = value.replace(/\D/g, '');

    if (numbers.length === 0) return '';
    if (numbers.length <= 2) return numbers;
    if (numbers.length <= 4) {
      const hours = numbers.slice(0, 2);
      const minutes = numbers.slice(2);
      return `${hours}:${minutes}`;
    }

    // Limitar a 4 dígitos (HHMM)
    const hours = numbers.slice(0, 2);
    const minutes = numbers.slice(2, 4);
    return `${hours}:${minutes}`;
  };

  // Função para validar horário
  const isValidTime = (time: string): boolean => {
    const regex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return regex.test(time);
  };

  // Função para converter horário em minutos
  const timeToMinutes = (time: string): number => {
    if (!time || !isValidTime(time)) return 0;
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  // Função para validar período
  const validatePeriod = (startTime: string, endTime: string, topicDuration: number): {
    isValid: boolean;
    error?: string;
    canFitTopics?: number;
  } => {
    // ✅ VALIDAÇÃO MAIS RIGOROSA: Campos vazios não são permitidos
    if (!startTime || startTime.trim() === '') {
      return { isValid: false, error: "Horário de início obrigatório" };
    }

    if (!endTime || endTime.trim() === '') {
      return { isValid: false, error: "Horário de término obrigatório" };
    }

    if (!isValidTime(startTime) || !isValidTime(endTime)) {
      return { isValid: false, error: "Formato inválido (HH:MM)" };
    }

    const startMinutes = timeToMinutes(startTime);
    const endMinutes = timeToMinutes(endTime);

    if (startMinutes >= endMinutes) {
      return { isValid: false, error: "Início deve ser menor que término" };
    }

    const periodMinutes = endMinutes - startMinutes;
    const canFitTopics = Math.floor(periodMinutes / topicDuration);

    if (canFitTopics === 0) {
      return {
        isValid: false,
        error: `Período muito curto (${periodMinutes}min). Mínimo: ${topicDuration}min`,
        canFitTopics: 0
      };
    }

    return {
      isValid: true,
      canFitTopics
    };
  };

  // Calculate enabled days count
  const enabledDaysCount = Object.values(availableDays).filter(day => day?.enabled).length;

  // Validar todos os períodos
  const validateAllPeriods = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    Object.entries(availableDays).forEach(([dayName, config]) => {
      if (config?.enabled && config.periods) {
        config.periods.forEach((period, index) => {
          // ✅ VALIDAÇÃO OBRIGATÓRIA: Se o dia está habilitado, TODOS os campos devem estar preenchidos
          const validation = validatePeriod(period.startTime, period.endTime, topicDuration);
          if (!validation.isValid) {
            errors.push(`${dayName} - Período ${index + 1}: ${validation.error}`);
          }
        });
      }
    });

    return { isValid: errors.length === 0, errors };
  };

  // Expor validação para o componente pai
  React.useEffect(() => {
    const validation = validateAllPeriods();
    // Validação silenciosa - erros são mostrados nos campos individuais
  }, [availableDays, topicDuration]);
  const addPeriod = (dayId: string) => {
    const currentDayConfig = availableDays[dayId];
    const updatedPeriods = [...currentDayConfig.periods, { startTime: "", endTime: "" }];
    setValue(`availableDays.${dayId}.periods`, updatedPeriods);
  };

  const removePeriod = (dayId: string, periodIndex: number) => {
    const currentDayConfig = availableDays[dayId];
    if (currentDayConfig.periods.length <= 1) return;
    const updatedPeriods = currentDayConfig.periods.filter((_, index) => index !== periodIndex);
    setValue(`availableDays.${dayId}.periods`, updatedPeriods);
  };

  return (
    <div className="p-6 space-y-4 border-2 border-slate-200 rounded-xl bg-white shadow-md">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-slate-800">
          <div className="p-2 rounded-full bg-emerald-100">
            <CalendarDays className="w-5 h-5 text-emerald-600" />
          </div>
          <h3 className="text-lg font-bold">Dias e períodos disponíveis</h3>
        </div>
        <span className="px-3 py-1 text-sm font-bold rounded-full bg-emerald-100 text-emerald-700">
          {enabledDaysCount} dias
        </span>
      </div>
      
      <div className="overflow-hidden divide-y-2 border-2 border-slate-200 rounded-xl shadow-md">
        {WEEK_DAYS.map((day) => (
          <div key={day.id} className={cn(
            "py-4 px-5",
            availableDays[day.id]?.enabled ? "bg-white" : "bg-slate-50"
          )}>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Controller
                  name={`availableDays.${day.id}.enabled`}
                  control={form.control}
                  render={({ field }) => (
                    <Checkbox
                      id={`day-${day.id}`}
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className={cn(
                        "border-slate-300 w-5 h-5",
                        field.value ? "text-blue-600 border-blue-600" : ""
                      )}
                    />
                  )}
                />
                <Label htmlFor={`day-${day.id}`} className="text-base font-bold cursor-pointer">
                  {day.label}
                </Label>
              </div>
              
              {availableDays[day.id]?.enabled && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="h-8 text-xs border-blue-600 text-blue-600 hover:bg-blue-50 font-bold"
                  onClick={() => addPeriod(day.id)}
                >
                  <Plus className="h-3.5 w-3.5 sm:mr-1" />
                  <span className="hidden sm:inline">Adicionar período</span>
                </Button>
              )}
            </div>
            
            {availableDays[day.id]?.enabled && (
              <div className="pl-7 mt-4 space-y-4">
                {availableDays[day.id]?.periods.map((period, periodIndex) => (
                  <div key={periodIndex} className="flex items-center gap-3">
                    <div className="grid flex-1 grid-cols-2 gap-3">
                      <div>
                        <div className="mb-1 text-xs font-medium text-slate-500">Início (ex: 08:00)</div>
                        <Controller
                          name={`availableDays.${day.id}.periods.${periodIndex}.startTime`}
                          control={form.control}
                          render={({ field }) => {
                            const endTime = availableDays[day.id]?.periods[periodIndex]?.endTime || '';
                            // ✅ VALIDAÇÃO SEMPRE ATIVA: Mostrar erro se campo vazio ou inválido
                            const validation = validatePeriod(field.value, endTime, topicDuration);

                            return (
                              <div>
                                <Input
                                  type="text"
                                  value={field.value}
                                  onChange={(e) => {
                                    const formatted = formatTime(e.target.value);
                                    field.onChange(formatted);
                                  }}
                                  placeholder="08:00"
                                  maxLength={5}
                                  className={`h-10 border-2 focus:ring-blue-600/20 font-medium font-mono ${
                                    !validation.isValid ?
                                      'border-red-500 focus:border-red-600' :
                                      'border-slate-300 focus:border-blue-600'
                                  }`}
                                />
                                {!validation.isValid && validation.error && (
                                  <p className="text-xs text-red-500 mt-1">{validation.error}</p>
                                )}
                              </div>
                            );
                          }}
                        />
                      </div>
                      <div>
                        <div className="mb-1 text-xs font-medium text-slate-500">Término (ex: 18:00)</div>
                        <Controller
                          name={`availableDays.${day.id}.periods.${periodIndex}.endTime`}
                          control={form.control}
                          render={({ field }) => {
                            const startTime = availableDays[day.id]?.periods[periodIndex]?.startTime || '';
                            // ✅ VALIDAÇÃO SEMPRE ATIVA: Mostrar erro se campo vazio ou inválido
                            const validation = validatePeriod(startTime, field.value, topicDuration);

                            return (
                              <div>
                                <Input
                                  type="text"
                                  value={field.value}
                                  onChange={(e) => {
                                    const formatted = formatTime(e.target.value);
                                    field.onChange(formatted);
                                  }}
                                  placeholder="18:00"
                                  maxLength={5}
                                  className={`h-10 border-2 focus:ring-blue-600/20 font-medium font-mono ${
                                    !validation.isValid ?
                                      'border-red-500 focus:border-red-600' :
                                      'border-slate-300 focus:border-blue-600'
                                  }`}
                                />
                                {!validation.isValid && validation.error && (
                                  <p className="text-xs text-red-500 mt-1">{validation.error}</p>
                                )}
                                {validation.isValid && validation.canFitTopics && (
                                  <p className="text-xs text-green-600 mt-1">
                                    ✓ {validation.canFitTopics} tópico(s) de {topicDuration}min
                                  </p>
                                )}
                              </div>
                            );
                          }}
                        />
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="w-9 h-9 mt-5 text-red-500 hover:text-red-600 hover:bg-red-50"
                      onClick={() => removePeriod(day.id, periodIndex)}
                      disabled={availableDays[day.id]?.periods.length <= 1}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};
