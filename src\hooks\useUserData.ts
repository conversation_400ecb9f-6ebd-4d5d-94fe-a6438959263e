import { useMemo, useRef, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './useAuth';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useOptimizedUserPreferencesData } from '@/hooks/useConsolidatedUserPreferences';
interface UserProfile {
  id: string;
  full_name: string;
  avatar_url: string | null;
  formation_area: string;
  graduation_year: string;
  is_student: boolean;
  preparation_type: string | null;
  specialty: string | null;
  is_admin: boolean;
  premium: boolean;
  premium_requested: boolean;
  premium_requested_history: boolean;
}

interface UserPreferences {
  preferences_completed: boolean;
}

/**
 * Hook centralizado para gerenciar todos os dados do usuário
 * Usa React Query para cache otimizado e evita múltiplas chamadas
 */
export const useUserData = () => {
  const { user, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();
  const lastUserIdRef = useRef<string | null>(null);

  // Estabilizar user ID para evitar re-fetches desnecessários
  const stableUserId = useMemo(() => user?.id || null, [user?.id]);

  // Limpar cache quando usuário muda ou faz logout
  useEffect(() => {
    const currentUserId = stableUserId;
    const lastUserId = lastUserIdRef.current;

    // Se mudou de usuário ou fez logout, limpar cache
    if (lastUserId !== currentUserId) {
      if (lastUserId) {
        // Limpar cache do usuário anterior
        queryClient.removeQueries(['user-profile', lastUserId]);
      }

      // Se fez logout, limpar todos os caches relacionados
      if (!currentUserId && lastUserId) {
        queryClient.removeQueries(['user-profile']);
        queryClient.removeQueries(['user-access']);
        queryClient.removeQueries(['user-preferences']);
      }

      // Atualizar referência APÓS a limpeza para evitar loops
      lastUserIdRef.current = currentUserId;
    }
  }, [stableUserId, queryClient]);

  // React Query para buscar dados completos do perfil
  const { data: profile, isLoading: profileLoading, error: profileError } = useQuery({
    queryKey: ['user-profile', stableUserId],
    queryFn: async (): Promise<UserProfile | null> => {
      if (!stableUserId) {
        return null;
      }

      // Verificar se ainda é o mesmo usuário (evitar race conditions)
      const currentUser = await supabase.auth.getUser();
      if (currentUser.data.user?.id !== stableUserId) {
        throw new Error('User changed during fetch');
      }

      // Buscar todos os dados do perfil diretamente do banco
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', stableUserId)
        .single();

      if (error) {
        // Se o perfil não existe, não é necessariamente um erro crítico
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      return data as UserProfile;
    },
    enabled: !!stableUserId && !authLoading,
    staleTime: 5 * 60 * 1000, // 5 minutos - dados do perfil não mudam frequentemente
    cacheTime: 10 * 60 * 1000, // 10 minutos de cache
    retry: (failureCount, error: any) => {
      // Não retry se usuário mudou ou não existe
      if (error?.message === 'User changed during fetch' || error?.code === 'PGRST116') {
        return false;
      }
      return failureCount < 2;
    },
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    onError: (error) => {
      console.error('❌ [useUserData] Query error:', error);
    }
  });

  // ✅ OTIMIZADO: Usar dados consolidados para user_preferences
  const {
    userPreferences,
    isLoading: preferencesLoading,
    error: preferencesError,
    hasCompletedPreferences
  } = useOptimizedUserPreferencesData();

  // Memoizar o retorno para evitar re-renders desnecessários
  return useMemo(() => {
    const isLoading = profileLoading || preferencesLoading || authLoading;
    const error = profileError || preferencesError;

    // Se não há usuário autenticado, retornar estado limpo
    if (!user || authLoading) {
      return {
        user: null,
        profile: null,
        isLoading: authLoading,
        error: null,
        isAdmin: false,
        isPremium: false,
        isPremiumRequested: false,
        isPremiumRequestedHistory: false,
        domain: 'residencia',
        isResidencia: true,
        isReady: false,
        hasAccess: false,
        hasCompletedOnboarding: false,
        hasCompletedPreferences: false
      };
    }

    const result = {
      user,
      profile,
      isLoading,
      error,
      isAdmin: profile?.is_admin || false,
      isPremium: profile?.premium || false,
      isPremiumRequested: profile?.premium_requested || false,
      isPremiumRequestedHistory: profile?.premium_requested_history || false,
      domain: profile?.preparation_type === 'residencia'
        ? 'residencia'
        : profile?.preparation_type === 'revalida'
        ? 'revalida'
        : profile?.specialty?.toLowerCase() || 'residencia',
      isResidencia: profile?.preparation_type === 'residencia' || !profile?.preparation_type,
      isReady: !isLoading && !!user && (!!profile || profileError?.code === 'PGRST116'),
      hasAccess: !!(profile?.premium || profile?.premium_requested),
      hasCompletedOnboarding: !!profile?.preparation_type,
      hasCompletedPreferences: hasCompletedPreferences
    };



    return result;
  }, [
    user?.id,
    profile?.id,
    profile?.premium,
    profile?.premium_requested,
    profile?.preparation_type,
    hasCompletedPreferences,
    profileLoading,
    preferencesLoading,
    profileError,
    preferencesError,
    authLoading
  ]);
};
