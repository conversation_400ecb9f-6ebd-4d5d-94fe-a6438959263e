import React, { useState, memo, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, BookOpen, CheckCircle, Circle, ChevronDown, ChevronUp, Calendar, Eye } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import StudyTopicDialog from './StudyTopicDialog';
import ConfirmStudyDialog from './ConfirmStudyDialog';
import StudySuccessDialog from './StudySuccessDialog';
import type { WeeklySchedule, DaySchedule, StudyTopic } from '@/types/study-schedule';

interface TimelineWeekViewProps {
  weeklySchedule: WeeklySchedule | null;
  isLoading?: boolean;
  onTopicClick?: (topic: StudyTopic) => void;
  onMarkAsStudied?: (topicId: string) => void;
  onMarkAsPending?: (topicId: string) => void;
}

interface ExpandedDay {
  [dayName: string]: boolean;
}

const TimelineWeekView: React.FC<TimelineWeekViewProps> = ({
  weeklySchedule,
  isLoading = false,
  onTopicClick,
  onMarkAsStudied,
  onMarkAsPending
}) => {
  const [expandedDays, setExpandedDays] = useState<ExpandedDay>({});
  const [selectedTopic, setSelectedTopic] = useState<StudyTopic | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
  const [topicToMarkAsStudied, setTopicToMarkAsStudied] = useState<StudyTopic | null>(null);

  // ✅ SOLUÇÃO: Estado local para tópicos estudados (sem mexer no cache)
  const [localStudiedTopics, setLocalStudiedTopics] = useState<Set<string>>(new Set());



  // ✅ MEMOIZAR FUNÇÕES para evitar re-renderizações
  const toggleDayExpansion = useCallback((dayName: string) => {
    setExpandedDays(prev => ({
      ...prev,
      [dayName]: !prev[dayName]
    }));
  }, []);

  const handleTopicClick = useCallback((topic: StudyTopic) => {
    setSelectedTopic(topic);
    setIsDialogOpen(true);
    onTopicClick?.(topic);
  }, [onTopicClick]);

  const handleMarkAsStudiedClick = useCallback((topic: StudyTopic) => {
    setTopicToMarkAsStudied(topic);
    setIsDialogOpen(false);
    setIsConfirmDialogOpen(true);
  }, []);

  const handleConfirmStudied = useCallback(() => {
    if (topicToMarkAsStudied) {
      // ✅ SOLUÇÃO: Atualizar estado local IMEDIATAMENTE
      setLocalStudiedTopics(prev => new Set([...prev, topicToMarkAsStudied.id]));

      // Chamar API em background (sem esperar)
      onMarkAsStudied?.(topicToMarkAsStudied.id);

      setIsConfirmDialogOpen(false);
      setIsSuccessDialogOpen(true);
    }
  }, [topicToMarkAsStudied, onMarkAsStudied]);

  // ✅ FUNÇÃO de ordenação com estado local
  const sortTopicsByStatus = useCallback((topics: StudyTopic[]) => {
    return [...topics].sort((a, b) => {
      // ✅ USAR ESTADO LOCAL para determinar se foi estudado
      const aCompleted = a.studyStatus === 'completed' || a.study_status === 'completed' || localStudiedTopics.has(a.id);
      const bCompleted = b.studyStatus === 'completed' || b.study_status === 'completed' || localStudiedTopics.has(b.id);

      // Não estudados primeiro (false < true)
      if (aCompleted !== bCompleted) {
        return aCompleted ? 1 : -1;
      }

      // Se ambos têm o mesmo status, manter ordem original (por horário)
      return 0;
    });
  }, [localStudiedTopics]);
  // Função para obter a semana atual
  const getCurrentWeekNumber = (): number | null => {
    if (!weeklySchedule?.recommendations?.length) {
      return null;
    }

    const currentDate = new Date();
    const currentDateStr = currentDate.toISOString().split('T')[0];



    // Encontrar a semana que contém a data atual
    for (const day of weeklySchedule.recommendations) {
      const weekStart = new Date(day.weekStartDate);
      const weekEnd = new Date(day.weekEndDate);
      const current = new Date(currentDateStr);

      if (current >= weekStart && current <= weekEnd) {
        return day.weekNumber;
      }
    }

    // Se não encontrar, retornar a primeira semana disponível
    return weeklySchedule.recommendations[0]?.weekNumber || null;
  };

  const currentWeekNumber = getCurrentWeekNumber();

  // Filtrar apenas os dias da semana atual
  const currentWeekDays = weeklySchedule?.recommendations?.filter(
    day => day.weekNumber === currentWeekNumber
  ) || [];

  // Ordenar os dias da semana
  const weekDays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado'];
  const sortedDays = weekDays.map(dayName =>
    currentWeekDays.find(day => day.day.toLowerCase() === dayName)
  );

  // Função para obter nome curto do dia
  const getShortDayName = (dayName: string): string => {
    const dayMap: Record<string, string> = {
      'domingo': 'Dom',
      'segunda-feira': 'Seg',
      'terça-feira': 'Ter',
      'quarta-feira': 'Qua',
      'quinta-feira': 'Qui',
      'sexta-feira': 'Sex',
      'sábado': 'Sáb'
    };
    return dayMap[dayName.toLowerCase()] || dayName.slice(0, 3);
  };

  // Função para obter cor baseada na dificuldade
  const getDifficultyColor = (difficulty: string): string => {
    switch (difficulty?.toLowerCase()) {
      case 'fácil':
      case 'easy':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'médio':
      case 'medium':
        return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      case 'difícil':
      case 'hard':
        return 'bg-red-100 text-red-700 border-red-200';
      default:
        return 'bg-gray-100 text-gray-700 border-gray-200';
    }
  };

  // Função para formatar duração
  const formatDuration = (duration: string): string => {
    const match = duration?.match(/(\d+)/);
    return match ? `${match[1]}min` : duration || '';
  };

  // Função para obter data atual formatada
  const getCurrentDateInfo = () => {
    const today = new Date();

    // Obter horário de Brasília (GMT-3)
    const brasiliaTime = new Date(today.toLocaleString("en-US", {timeZone: "America/Sao_Paulo"}));

    return {
      dayName: today.toLocaleDateString('pt-BR', { weekday: 'long' }),
      dateStr: `${today.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: 'long',
        year: 'numeric'
      })} - ${brasiliaTime.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit',
        timeZone: 'America/Sao_Paulo'
      })}`,
      currentDateStr: today.toISOString().split('T')[0]
    };
  };

  const currentDateInfo = getCurrentDateInfo();

  if (isLoading) {
    return (
      <Card className="p-4 bg-white border border-gray-200 rounded-xl shadow-sm">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-7 gap-2">
            {Array.from({ length: 7 }).map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-8 bg-gray-200 rounded"></div>
                <div className="h-16 bg-gray-100 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (!weeklySchedule?.recommendations?.length) {
    return (
      <Card className="p-6 bg-white border border-gray-200 rounded-xl shadow-sm">
        <div className="text-center text-gray-500">
          <BookOpen className="h-8 w-8 mx-auto mb-2 opacity-50" />
          <p className="text-sm">Nenhum cronograma encontrado</p>
          <p className="text-xs mt-1">Crie seu primeiro cronograma usando os botões acima</p>
        </div>
      </Card>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="p-4 bg-white border border-gray-200 rounded-xl shadow-sm">
        {/* Header Melhorado */}
        <div className="space-y-3 mb-4">
          {/* Linha principal */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-[#7E69AB] to-[#9B87C4] rounded-xl flex items-center justify-center border border-black shadow-md">
                <Calendar className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="font-black text-gray-800 text-base">Vista da Semana Atual</h3>
                <p className="text-xs text-gray-600 font-medium">
                  {currentDateInfo.dateStr}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {currentWeekNumber && (
                <Badge className="px-3 py-1 bg-gradient-to-r from-[#58CC02] to-[#46a302] text-white border border-black text-xs font-bold shadow-sm">
                  Semana Atual
                </Badge>
              )}
              <Badge className="px-2 py-1 bg-blue-100 text-blue-700 border border-blue-200 text-xs font-medium">
                <Eye className="h-3 w-3 mr-1" />
                Hoje: {currentDateInfo.dayName}
              </Badge>
            </div>
          </div>

          {/* Período da semana */}
          {currentWeekDays.length > 0 && (
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 border border-gray-200 rounded-lg p-2">
              <div className="flex items-center justify-center gap-2 text-xs text-gray-700">
                <Calendar className="h-3 w-3" />
                <span className="font-medium">
                  {new Date(currentWeekDays[0].weekStartDate + 'T00:00:00').toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: 'short'
                  })} - {new Date(currentWeekDays[0].weekEndDate + 'T00:00:00').toLocaleDateString('pt-BR', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                  })}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Grid da timeline */}
        <div className="grid grid-cols-7 gap-2">
          {sortedDays.map((dayData, index) => {
            const dayName = weekDays[index];
            const isToday = currentDateInfo.dayName.toLowerCase() === dayName;

            // Calcular a data real do dia baseada na semana
            let dayDate = new Date();
            if (currentWeekDays.length > 0) {
              // Usar as datas reais da semana do banco
              const weekStart = new Date(currentWeekDays[0].weekStartDate + 'T00:00:00');
              const dayOfWeekIndex = weekDays.indexOf(dayName);
              dayDate = new Date(weekStart);
              dayDate.setDate(weekStart.getDate() + dayOfWeekIndex);
            }

            return (
              <div key={dayName} className="space-y-2">
                {/* Header do dia */}
                <div className={`text-center p-2 rounded-lg text-xs font-medium ${
                  isToday
                    ? 'bg-[#58CC02] text-white border border-black'
                    : 'bg-gray-50 text-gray-700 border border-gray-200'
                }`}>
                  <div className="font-bold">{getShortDayName(dayName)}</div>
                  <div className="text-[10px] opacity-75">
                    {dayDate.getDate()}
                  </div>
                </div>

                {/* Tópicos do dia */}
                <div className="space-y-1 min-h-[80px]">
                  {dayData?.topics?.length ? (
                    <>
                      {/* Tópicos visíveis (primeiros 2 ou todos se expandido) */}
                      <AnimatePresence>
                        {(() => {
                          // ✅ CORREÇÃO: Aplicar priorização ANTES de slice
                          const sortedTopics = sortTopicsByStatus(dayData.topics);
                          const visibleTopics = expandedDays[dayName] ? sortedTopics : sortedTopics.slice(0, 2);

                          return visibleTopics.map((topic: StudyTopic, topicIndex: number) => (
                          <motion.div
                            key={topic.id}
                            initial={{ opacity: 0, scale: 0.9 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.9 }}
                            transition={{ delay: index * 0.05 + topicIndex * 0.02 }}
                            onClick={() => handleTopicClick(topic)}
                            className={`
                              p-2 rounded-lg border text-xs cursor-pointer
                              transition-all duration-200 hover:scale-105 hover:shadow-md
                              ${getDifficultyColor(topic.difficulty)}
                              ${topic.studyStatus === 'completed' ? 'ring-2 ring-green-400 ring-opacity-50' : ''}
                            `}
                          >
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span className="text-[10px] font-medium">
                                  {topic.startTime || '08:00'}
                                </span>
                              </div>
                              <span className="text-[10px] font-medium bg-white/50 px-1 rounded">
                                {formatDuration(topic.duration)}
                              </span>
                            </div>

                            <div className="font-medium leading-tight mb-1 line-clamp-2">
                              {topic.focusName || topic.focus}
                            </div>

                            <div className="flex items-center justify-between">
                              <span className="text-[9px] opacity-75 truncate">
                                {topic.specialtyName || topic.specialty}
                              </span>
                              <div className="flex-shrink-0">
                                {topic.studyStatus === 'completed' || topic.study_status === 'completed' || localStudiedTopics.has(topic.id) ? (
                                  <CheckCircle className="h-3 w-3 text-green-600" />
                                ) : (
                                  <Circle className="h-3 w-3 opacity-50" />
                                )}
                              </div>
                            </div>
                          </motion.div>
                          ));
                        })()}
                      </AnimatePresence>

                      {/* Botão de expandir/colapsar */}
                      {dayData.topics.length > 2 && (
                        <motion.div
                          className="text-center"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ delay: 0.3 }}
                        >
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleDayExpansion(dayName)}
                            className="h-6 px-2 text-[10px] text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-all"
                          >
                            {expandedDays[dayName] ? (
                              <>
                                <ChevronUp className="h-3 w-3 mr-1" />
                                Mostrar menos
                              </>
                            ) : (
                              <>
                                <ChevronDown className="h-3 w-3 mr-1" />
                                +{dayData.topics.length - 2} mais
                              </>
                            )}
                          </Button>
                        </motion.div>
                      )}
                    </>
                  ) : (
                    <div className="h-16 border-2 border-dashed border-gray-200 rounded-lg flex items-center justify-center hover:border-gray-300 transition-colors">
                      <span className="text-[10px] text-gray-400">Livre</span>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer com estatísticas melhoradas */}
        {currentWeekDays.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-3 gap-4 mb-3">
              <div className="text-center">
                <div className="text-lg font-bold text-[#7E69AB]">
                  {currentWeekDays.reduce((acc, day) => acc + (day.topics?.length || 0), 0)}
                </div>
                <div className="text-[10px] text-gray-600 font-medium">Estudos</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-[#58CC02]">
                  {Math.round(currentWeekDays.reduce((acc, day) => acc + day.totalHours, 0) / 60)}h
                </div>
                <div className="text-[10px] text-gray-600 font-medium">Total</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-orange-600">
                  {currentWeekDays.filter(day => day.topics?.some(t => t.studyStatus === 'completed')).length}
                </div>
                <div className="text-[10px] text-gray-600 font-medium">Dias ativos</div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-2">
              <div className="flex items-center justify-center gap-2 text-xs text-blue-700">
                <BookOpen className="h-3 w-3" />
                <span className="font-medium">
                  Clique nos cards para ver detalhes dos estudos
                </span>
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Dialog de detalhes do tópico */}
      <StudyTopicDialog
        topic={selectedTopic}
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onMarkAsStudied={handleMarkAsStudiedClick}
        onMarkAsPending={onMarkAsPending}
      />

      {/* Dialog de confirmação */}
      <ConfirmStudyDialog
        topic={topicToMarkAsStudied}
        open={isConfirmDialogOpen}
        onOpenChange={setIsConfirmDialogOpen}
        onConfirm={handleConfirmStudied}
      />

      {/* Dialog de sucesso */}
      <StudySuccessDialog
        topic={topicToMarkAsStudied}
        open={isSuccessDialogOpen}
        onOpenChange={setIsSuccessDialogOpen}
      />
    </motion.div>
  );
};

// ✅ MEMOIZAR componente para evitar re-renderizações desnecessárias
export default memo(TimelineWeekView);
