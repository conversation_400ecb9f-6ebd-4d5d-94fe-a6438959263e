import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import type { SelectedFilters } from '@/types/question';

export const useDynamicFilters = (selectedFilters: SelectedFilters) => {
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);
  const [availableYears, setAvailableYears] = useState<string[]>([]);
  const [totalQuestions, setTotalQuestions] = useState(0);

  useEffect(() => {
    const updateAvailableOptions = async () => {
      console.log('🔄 Updating available options with filters:', selectedFilters);

      // ✅ OTIMIZADO: Usar dados do useQuestionMetadata em vez de requisições diretas
      // Dados já estão disponíveis no cache, não precisamos fazer novas requisições

      // ✅ OTIMIZADO: Usar dados do useQuestionMetadata
      // Este hook não deveria fazer requisições diretas
      console.warn('⚠️ useDynamicFilters está obsoleto - usar useQuestionMetadata');
      return;

      // Create OR conditions for specialties, themes, focuses and institutions
      const conditions = [];

      if (selectedFilters.specialties?.length > 0) {
        const validIds = selectedFilters.specialties.filter(Boolean);
        if (validIds.length > 0) {
          conditions.push(`specialty_id.in.(${validIds.join(',')})`);
        }
      }
      if (selectedFilters.themes?.length > 0) {
        const validIds = selectedFilters.themes.filter(Boolean);
        if (validIds.length > 0) {
          conditions.push(`theme_id.in.(${validIds.join(',')})`);
        }
      }
      if (selectedFilters.focuses?.length > 0) {
        const validIds = selectedFilters.focuses.filter(Boolean);
        if (validIds.length > 0) {
          conditions.push(`focus_id.in.(${validIds.join(',')})`);
        }
      }

      // Apply OR conditions if they exist
      if (conditions.length > 0) {
        query = query.or(conditions.join(','));
      }

      const { data: questions, error } = await query;

      if (error) {
        return;
      }

      // Update available filters data
      setAvailableLocations(allLocations?.map(loc => loc.id).filter(Boolean) || []);
      setAvailableYears(uniqueYears);
      setTotalQuestions(questions?.length || 0);
    };

    updateAvailableOptions();
  }, [selectedFilters]);

  return {
    availableLocations,
    availableYears,
    totalQuestions
  };
};