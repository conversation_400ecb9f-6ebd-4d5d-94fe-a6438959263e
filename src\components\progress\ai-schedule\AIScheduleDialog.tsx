
import { useState, useEffect, useRef } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import type { AIScheduleFormData, DayConfig, GenerationPhase, DialogVisibilityMode } from "./types";
import { ProgressSection } from "./sections/ProgressSection";
import { ScheduleForm } from "./sections/ScheduleForm";
import { DialogHeader } from "./sections/DialogHeader";
import { SuccessSection } from "./sections/SuccessSection";
import { WEEK_DAYS } from "./types";
import { useDomain } from "@/hooks/useDomain";
import { toast } from "@/hooks/use-toast";
import { useStudyPreferences } from "@/hooks/useStudyPreferences";

// Utility function to validate time ranges
const isValidTimeRange = (startTime: string, endTime: string): boolean => {
  if (!startTime || !endTime) return false;

  // Convert times to minutes since midnight for comparison
  const startParts = startTime.split(':');
  const endParts = endTime.split(':');

  if (startParts.length !== 2 || endParts.length !== 2) return false;

  const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
  const endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);

  // End time should be after start time and have at least 15 minutes duration
  return endMinutes > startMinutes && (endMinutes - startMinutes) >= 15;
};

interface AIScheduleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: AIScheduleFormData) => void;
  isLoading: boolean;
  isComplete?: boolean;
  generationStats?: any;
  existingWeeks?: number[];
  showProgress?: boolean;
}

export const AIScheduleDialog = ({
  open,
  onOpenChange,
  onSubmit,
  isLoading,
  isComplete = false,
  generationStats,
  existingWeeks = [],
  showProgress = false
}: AIScheduleDialogProps) => {
  // Dialog and progress state
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");
  const [currentStep, setCurrentStep] = useState(0);
  const [generationPhase, setGenerationPhase] = useState<GenerationPhase>('not_started');

  // ✅ Usar isComplete do hook em vez de estado local
  const isCompleteState = isComplete;

  // Refs for tracking progress
  const topicsCreatedRef = useRef<number>(0);
  const lastLogRef = useRef<string>("");
  const lastCompletionCheckRef = useRef<number>(0);
  const lastRenderTimeRef = useRef<number>(Date.now());
  const renderCountRef = useRef<number>(0);

  // Control what's displayed in the dialog
  const [dialogMode, setDialogMode] = useState<DialogVisibilityMode>('form');
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Domain info
  const { domain, isReady } = useDomain();

  // User preferences
  const { preferences } = useStudyPreferences();

  // Form state
  const [customWeeks, setCustomWeeks] = useState(false);



  // Efeito para forçar a visualização de progresso quando showProgress ou isLoading mudam
  useEffect(() => {
    if (open && (showProgress || isLoading)) {
      setDialogMode('progress');

      // Inicializar o progresso se estiver começando
      if (progress === 0) {
        setProgress(5);
        setProgressMessage("Iniciando geração do cronograma...");
      }
    }
  }, [open, showProgress, isLoading, progress]);

  const form = useForm<AIScheduleFormData>({
    defaultValues: {
      availableDays: WEEK_DAYS.reduce((acc, day) => {
        acc[day.id] = {
          enabled: ["Segunda-feira", "Quarta-feira", "Sexta-feira"].includes(day.id),
          periods: [{ startTime: "09:00", endTime: "11:00" }]
        };
        return acc;
      }, {} as { [key: string]: DayConfig }),
      weeksCount: 4,
      topicDuration: "30",
      scheduleOption: "new",
      generationMode: "random",
      institutionIds: [],
      startYear: undefined,
      endYear: undefined
    },
    mode: "onChange"
  });

  useEffect(() => {
    const subscription = form.watch((formData) => {
      const validation = validateForm(formData, preferences);
      setValidationErrors(validation.errors);
    });

    return () => subscription.unsubscribe();
  }, [form, preferences]);

  useEffect(() => {
    if (!open) return;

    // If we're loading, force progress view
    if (isLoading && dialogMode !== 'progress') {
      setDialogMode('progress');
    }

    // Force success view if complete
    if (isCompleteState && dialogMode !== 'success') {
      setDialogMode('success');
    }

    if (!isLoading && !isCompleteState) return;

    // Track progress through console logs

    const originalConsoleLog = console.log;
    console.log = function() {
      const args = Array.from(arguments);
      const message = args[0];

      if (typeof message === 'string') {
        lastLogRef.current = message;

        // Track creation phase
        if (message.includes('Creating new weeks') ||
            message.includes('Adding to existing week') ||
            message.includes('[useScheduleManagement] Adding') ||
            message.includes('[useScheduleManagement] Creating')) {

          setGenerationPhase('creating_weeks');
          setProgress(20);
          setProgressMessage("Criando estrutura de semanas...");

          if (dialogMode !== 'progress') {
            setDialogMode('progress');
          }
        }
        // Track analysis phase
        else if (message.includes('Fetching specialties for domain') ||
                message.includes('Found') && message.includes('specialties before filtering')) {
          setGenerationPhase('analyzing_specialties');
          setProgress(40);
          setProgressMessage("Analisando especialidades disponíveis...");

          if (dialogMode !== 'progress') {
            setDialogMode('progress');
          }
        }
        // Track generation beginning
        else if (message.includes('Starting topic generation')) {
          setGenerationPhase('generating_topics');
          setProgress(60);
          setProgressMessage("Gerando tópicos de estudo...");

          if (dialogMode !== 'progress') {
            setDialogMode('progress');
          }
        }
        // Track individual topic generation
        else if (message.includes('Generating') && message.includes('topics for day')) {
          setGenerationPhase('generating_topics');

          // Calculate expected total topics
          const enabledDaysCount = Object.values(form.watch('availableDays'))
            .filter(day => day.enabled).length;
          const scheduleOption = form.watch('scheduleOption');
          const weeksCount = form.watch('weeksCount');
          const topicsPerDay = 4; // Hardcoded based on what we see in logs
          const estimatedTotalTopics = enabledDaysCount * topicsPerDay *
            (scheduleOption === "new" ? weeksCount : 1);

          // Extract number of topics being generated from the log
          const topicMatch = message.match(/Generating (\d+) topics/);
          if (topicMatch && topicMatch[1]) {
            const currentTopics = parseInt(topicMatch[1], 10);
            topicsCreatedRef.current += currentTopics;
          }

          // Map progress to a percentage from 60% to 95%
          const topicsProgress = Math.min(topicsCreatedRef.current / estimatedTotalTopics, 1);
          const mappedProgress = 60 + (topicsProgress * 35);
          const newProgress = Math.min(Math.round(mappedProgress), 95);

          console.log(`🔍 [AIScheduleDialog] Progress update: ${newProgress}%, topics=${topicsCreatedRef.current}/${estimatedTotalTopics}`);
          setProgress(newProgress);
          setProgressMessage("Gerando tópicos de estudo...");

          if (dialogMode !== 'progress') {
            setDialogMode('progress');
          }
        }
        // Track completion
        else if (message.includes('Generation complete') ||
                (message.includes('Created') && message.includes('topics in total'))) {

          setGenerationPhase('completed');
          setProgress(100);
          setProgressMessage("Cronograma concluído!");

          // Não mudar para progress aqui - deixar o useEffect principal gerenciar
        }
        // Track errors
        else if (message.includes('Error generating AI schedule')) {

          setGenerationPhase('error');
          setProgressMessage("Erro na geração do cronograma");
        }
      }

      // Pass through to original console.log
      return originalConsoleLog.apply(console, args);
    };

    // Periodically check for completion
    const completionInterval = setInterval(() => {
      const now = Date.now();

      // If we see completion in logs
      if (lastLogRef.current.includes('Generation complete') ||
          (lastLogRef.current.includes('Created') && lastLogRef.current.includes('topics in total'))) {
        console.log("✅ [AIScheduleDialog] Completion detected in completion check");
        setGenerationPhase('completed');
        setProgress(100);

        if (dialogMode !== 'progress') {
          setDialogMode('progress');
        }

        clearInterval(completionInterval);
      }
      // If we've been in generating_topics phase for a while with no activity
      else if (now - lastCompletionCheckRef.current > 3000 &&
             generationPhase === 'generating_topics' &&
             progress >= 90) {
        console.log("⏰ [AIScheduleDialog] No activity for 3 seconds, finalizing");
        setGenerationPhase('completing');
        setProgress(98);

        // Force completion after a bit longer
        setTimeout(() => {
          if (isLoading) {
            console.log("⏰ [AIScheduleDialog] Forcing completion after timeout");
            setGenerationPhase('completed');
            setProgress(100);

            if (dialogMode !== 'progress') {
              setDialogMode('progress');
            }
          }
        }, 2000);

        clearInterval(completionInterval);
      }

      lastCompletionCheckRef.current = now;
    }, 1000);

    // Clean up
    return () => {
      console.log = originalConsoleLog;
      clearInterval(completionInterval);
      console.log("🧹 [AIScheduleDialog] Cleaned up log listener");
    };
  }, [isLoading, open, form, generationPhase, progress, dialogMode, isCompleteState, showProgress, domain]);

  // Load message rotation effect when loading
  useEffect(() => {
    if (!open) return;
    if (!isLoading) return;

    console.log("🔄 [AIScheduleDialog] Setting up message rotation during loading");
    topicsCreatedRef.current = 0;
    lastCompletionCheckRef.current = Date.now();

    // Force showing progress when loading
    if (dialogMode !== 'progress') {
      console.log("⚠️ [AIScheduleDialog] FORCING dialogMode='progress' in message rotation");
      setDialogMode('progress');
    }

    // Rotate progress messages
    const progressMessages = [
      "Analisando suas preferências...",
      "Selecionando tópicos relevantes...",
      "Organizando seu cronograma...",
      "Calculando distribuição de temas...",
      "Equilibrando carga de estudos...",
      "Aplicando personalização...",
      "Ajustando tempos de estudo...",
      "Verificando compatibilidade de horários...",
      "Priorizando conteúdos essenciais...",
      "Alocando recursos de estudo...",
      "Finalizando seu plano personalizado...",
    ];

    // Start with a message
    if (!progressMessage) {
      setProgressMessage(progressMessages[0]);
    }

    const messageInterval = setInterval(() => {
      setCurrentStep((prev) => {
        const next = (prev + 1) % progressMessages.length;
        const newMessage = progressMessages[next];

        setProgressMessage(newMessage);
        return next;
      });
    }, 2000);

    return () => {
      clearInterval(messageInterval);

    };
  }, [isLoading, open, dialogMode, progressMessage]);

  // Reset state when dialog closes
  useEffect(() => {
    if (open) {
      // Force progress view if we're loading or showProgress is true
      // Force success view if complete
      if (isCompleteState && dialogMode !== 'success') {
        console.log('🎯 [AIScheduleDialog] Reset useEffect - mudando para success');
        setDialogMode('success');
      } else if ((isLoading || showProgress) && dialogMode !== 'progress') {
        setDialogMode('progress');
      }
    } else {
      // Reset state with a delay to avoid visual glitches
      const timeoutId = setTimeout(() => {
        if (!isLoading && !isCompleteState) {
          setProgress(0);
          setProgressMessage("");
          setCurrentStep(0);
          setGenerationPhase('not_started');
          setDialogMode('form');
          topicsCreatedRef.current = 0;
        }
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [open, isLoading, progress, dialogMode, isCompleteState, showProgress]);

  // Decide what to show based on all states
  const shouldShowProgress = showProgress || isLoading || isCompleteState || dialogMode === 'progress';

  const handleClose = () => {
    if (!isLoading) {
      onOpenChange(false);
      // Reset dialog state
      setDialogMode('form');
      setGenerationPhase('not_started');
      setProgress(0);
      setCurrentStep(0);
      setProgressMessage("");
    } else {
      toast({
        title: "Operação em andamento",
        description: "Por favor, aguarde a conclusão da geração do cronograma.",
        variant: "default"
      });
    }
  };

  const handleSubmitForm = (data: AIScheduleFormData) => {
    const validation = validateForm(data, preferences);
    if (!validation.isValid) {
      return;
    }

    // Make sure dialog is open
    if (!open) {
      onOpenChange(true);
    }

    // Reset progress tracking
    setProgress(5);
    setProgressMessage("Iniciando geração do cronograma...");
    setGenerationPhase('not_started');
    topicsCreatedRef.current = 0;

    // FORCE the progress view to be shown
    setDialogMode('progress');

    // Add domain to form data
    const enhancedData = {
      ...data,
      domain
    };

    // Submit form to parent component
    onSubmit(enhancedData);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-hidden p-0 border-0 shadow-2xl rounded-xl">
        <div className="absolute inset-0 bg-amber-50 rounded-xl"></div>
        <div className="absolute top-0 right-0 w-40 h-40 bg-yellow-300 rounded-full blur-3xl opacity-10 -mr-10 -mt-10"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-blue-400 rounded-full blur-3xl opacity-15 -ml-10 -mb-10"></div>

        <div className="relative z-10 p-8">
          <DialogHeader />

          {dialogMode === 'success' ? (
            <SuccessSection
              stats={generationStats}
              onClose={handleClose}
            />
          ) : isLoading || showProgress || dialogMode === 'progress' ? (
            <ProgressSection
              isLoading={isLoading}
              isComplete={isCompleteState}
              progress={progress}
              progressMessage={progressMessage}
              generationPhase={generationPhase}
              topicsCreatedRef={topicsCreatedRef}
              handleClose={handleClose}
              form={form}
            />
          ) : (
            <ScheduleForm
              form={form}
              onSubmit={handleSubmitForm}
              customWeeks={customWeeks}
              setCustomWeeks={setCustomWeeks}
              existingWeeks={existingWeeks}
              isLoading={isLoading}
              validationErrors={validationErrors}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

import { validateForm } from "./utils/validation";
