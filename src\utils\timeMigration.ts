import { supabase } from '@/integrations/supabase/client';

/**
 * 🔧 MIGRAÇÃO DE DADOS DE TEMPO - Corri<PERSON>r <PERSON>
 * 
 * Corrige sessões com tempo incorreto usando timestamps reais
 */

interface SessionToFix {
  id: string;
  total_time_spent: number;
  started_at: string;
  completed_at: string | null;
  status: string;
  real_duration: number;
}

/**
 * Identificar sessões com tempo incorreto
 */
export const identifyIncorrectSessions = async (userId?: string): Promise<SessionToFix[]> => {
  try {
    let query = supabase
      .from('study_sessions')
      .select('id, total_time_spent, started_at, completed_at, status')
      .not('completed_at', 'is', null); // Apenas sessões completadas

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data: sessions, error } = await query;

    if (error) throw error;

    const problematicSessions: SessionToFix[] = [];

    sessions?.forEach(session => {
      const startTime = new Date(session.started_at).getTime();
      const endTime = session.completed_at ? new Date(session.completed_at).getTime() : Date.now();
      const realDuration = Math.floor((endTime - startTime) / 1000);
      
      // Identificar sessões com discrepância significativa (>30 segundos ou 50%)
      const timeDiff = Math.abs(realDuration - (session.total_time_spent || 0));
      const percentDiff = realDuration > 0 ? (timeDiff / realDuration) * 100 : 100;
      
      if (timeDiff > 30 || percentDiff > 50) {
        problematicSessions.push({
          id: session.id,
          total_time_spent: session.total_time_spent || 0,
          started_at: session.started_at,
          completed_at: session.completed_at,
          status: session.status,
          real_duration: realDuration
        });
      }
    });

    return problematicSessions;
  } catch (error) {
    console.error('❌ [TimeMigration] Erro ao identificar sessões:', error);
    return [];
  }
};

/**
 * Corrigir uma sessão específica
 */
export const fixSessionTime = async (sessionId: string): Promise<boolean> => {
  try {
    // Buscar dados da sessão
    const { data: session, error: fetchError } = await supabase
      .from('study_sessions')
      .select('started_at, completed_at, total_time_spent')
      .eq('id', sessionId)
      .single();

    if (fetchError || !session) {
      console.error('❌ [TimeMigration] Sessão não encontrada:', sessionId);
      return false;
    }

    // Calcular tempo real
    const startTime = new Date(session.started_at).getTime();
    const endTime = session.completed_at ? new Date(session.completed_at).getTime() : Date.now();
    const realDuration = Math.floor((endTime - startTime) / 1000);

    // Usar o maior valor (mais conservador)
    const correctedTime = Math.max(realDuration, session.total_time_spent || 0);

    // Atualizar no banco
    const { error: updateError } = await supabase
      .from('study_sessions')
      .update({
        total_time_spent: correctedTime,
        // Adicionar flag de migração para auditoria
        migration_notes: `Corrigido de ${session.total_time_spent}s para ${correctedTime}s (real: ${realDuration}s)`
      })
      .eq('id', sessionId);

    if (updateError) {
      console.error('❌ [TimeMigration] Erro ao atualizar sessão:', updateError);
      return false;
    }

    console.log(`✅ [TimeMigration] Sessão ${sessionId} corrigida: ${session.total_time_spent}s → ${correctedTime}s`);
    return true;
  } catch (error) {
    console.error('❌ [TimeMigration] Erro ao corrigir sessão:', error);
    return false;
  }
};

/**
 * Corrigir todas as sessões problemáticas de um usuário
 */
export const fixAllUserSessions = async (userId: string): Promise<{
  total: number;
  fixed: number;
  errors: number;
}> => {
  const problematicSessions = await identifyIncorrectSessions(userId);
  
  console.log(`🔧 [TimeMigration] Encontradas ${problematicSessions.length} sessões para corrigir`);
  
  let fixed = 0;
  let errors = 0;

  for (const session of problematicSessions) {
    const success = await fixSessionTime(session.id);
    if (success) {
      fixed++;
    } else {
      errors++;
    }
    
    // Pequena pausa para não sobrecarregar o banco
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  console.log(`✅ [TimeMigration] Migração concluída: ${fixed} corrigidas, ${errors} erros`);
  
  return {
    total: problematicSessions.length,
    fixed,
    errors
  };
};

/**
 * Relatório de sessões problemáticas
 */
export const generateTimeReport = async (userId?: string): Promise<{
  totalSessions: number;
  problematicSessions: number;
  averageDiscrepancy: number;
  worstCases: SessionToFix[];
}> => {
  try {
    let query = supabase
      .from('study_sessions')
      .select('id, total_time_spent, started_at, completed_at, status')
      .not('completed_at', 'is', null);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data: sessions, error } = await query;
    if (error) throw error;

    const problematicSessions = await identifyIncorrectSessions(userId);
    
    let totalDiscrepancy = 0;
    problematicSessions.forEach(session => {
      totalDiscrepancy += Math.abs(session.real_duration - session.total_time_spent);
    });

    const averageDiscrepancy = problematicSessions.length > 0 
      ? totalDiscrepancy / problematicSessions.length 
      : 0;

    // Pior casos (maior discrepância)
    const worstCases = problematicSessions
      .sort((a, b) => {
        const discrepancyA = Math.abs(a.real_duration - a.total_time_spent);
        const discrepancyB = Math.abs(b.real_duration - b.total_time_spent);
        return discrepancyB - discrepancyA;
      })
      .slice(0, 5);

    return {
      totalSessions: sessions?.length || 0,
      problematicSessions: problematicSessions.length,
      averageDiscrepancy,
      worstCases
    };
  } catch (error) {
    console.error('❌ [TimeMigration] Erro ao gerar relatório:', error);
    return {
      totalSessions: 0,
      problematicSessions: 0,
      averageDiscrepancy: 0,
      worstCases: []
    };
  }
};

/**
 * Hook para usar no console/debug
 */
export const useTimeMigrationTools = () => {
  return {
    identifyIncorrectSessions,
    fixSessionTime,
    fixAllUserSessions,
    generateTimeReport
  };
};

// Expor no console para debug (apenas desenvolvimento)
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).timeMigration = {
    identifyIncorrectSessions,
    fixSessionTime,
    fixAllUserSessions,
    generateTimeReport
  };
}
